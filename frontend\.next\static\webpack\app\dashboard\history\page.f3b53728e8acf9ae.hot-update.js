"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/history/page",{

/***/ "(app-pages-browser)/./src/contexts/TradingContext.tsx":
/*!*****************************************!*\
  !*** ./src/contexts/TradingContext.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TradingProvider: () => (/* binding */ TradingProvider),\n/* harmony export */   useTradingContext: () => (/* binding */ useTradingContext)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/types */ \"(app-pages-browser)/./src/lib/types.tsx\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/session-manager */ \"(app-pages-browser)/./src/lib/session-manager.ts\");\n/* harmony import */ var _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/network-monitor */ \"(app-pages-browser)/./src/lib/network-monitor.ts\");\n/* __next_internal_client_entry_do_not_use__ TradingProvider,useTradingContext auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n// Define locally to avoid import issues\nconst DEFAULT_QUOTE_CURRENCIES = [\n    \"USDT\",\n    \"USDC\",\n    \"BTC\"\n];\n\n\n\n\n// Static fallback prices (used when API is unavailable)\nconst getStaticUSDPrice = (crypto)=>{\n    const usdPrices = {\n        // Major cryptocurrencies - Updated to current market prices\n        'BTC': 106000,\n        'ETH': 2500,\n        'SOL': 180,\n        'ADA': 0.85,\n        'DOGE': 0.32,\n        'LINK': 22,\n        'MATIC': 0.42,\n        'DOT': 6.5,\n        'AVAX': 38,\n        'SHIB': 0.000022,\n        'XRP': 2.1,\n        'LTC': 95,\n        'BCH': 420,\n        // Stablecoins\n        'USDT': 1.0,\n        'USDC': 1.0,\n        'FDUSD': 1.0,\n        'BUSD': 1.0,\n        'DAI': 1.0\n    };\n    return usdPrices[crypto.toUpperCase()] || 100;\n};\n// Static fallback function for market price calculation (no fluctuations)\nconst calculateStaticMarketPrice = (config)=>{\n    const crypto1USDPrice = getStaticUSDPrice(config.crypto1);\n    const crypto2USDPrice = getStaticUSDPrice(config.crypto2);\n    // Calculate the ratio: how many units of crypto2 = 1 unit of crypto1\n    const basePrice = crypto1USDPrice / crypto2USDPrice;\n    console.log(\"\\uD83D\\uDCCA Static price calculation: \".concat(config.crypto1, \" ($\").concat(crypto1USDPrice, \") / \").concat(config.crypto2, \" ($\").concat(crypto2USDPrice, \") = \").concat(basePrice.toFixed(6)));\n    return basePrice;\n};\n// Add this function to calculate the initial market price\nconst calculateInitialMarketPrice = (config)=>{\n    // Return 0 if either crypto is not selected\n    if (!config.crypto1 || !config.crypto2) {\n        return 0;\n    }\n    // Default fallback value for valid pairs\n    return calculateStaticMarketPrice(config);\n};\n// Get real market price with multiple fallback strategies\nconst getRealBinancePrice = async (config)=>{\n    try {\n        // Return 0 if either crypto is not selected\n        if (!config.crypto1 || !config.crypto2) {\n            return 0;\n        }\n        const pair = \"\".concat(config.crypto1, \"/\").concat(config.crypto2);\n        // Strategy 1: Try backend endpoint (if available)\n        try {\n            const symbol = pair.replace('/', '-'); // Convert BTC/USDT to BTC-USDT for API\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.tradingApi.getMarketPrice(symbol);\n            if (response && response.price > 0) {\n                console.log(\"✅ Backend Binance price: \".concat(config.crypto1, \"/\").concat(config.crypto2, \" = \").concat(response.price));\n                return response.price;\n            }\n        } catch (error) {\n            console.warn(\"⚠️ Backend API failed for \".concat(pair, \", trying alternative:\"), error);\n        }\n        // Strategy 2: Try CoinGecko API as reliable fallback\n        try {\n            const crypto1Id = getCoinGeckoId(config.crypto1);\n            const crypto2Id = getCoinGeckoId(config.crypto2);\n            if (crypto1Id && crypto2Id) {\n                const response = await fetch(\"https://api.coingecko.com/api/v3/simple/price?ids=\".concat(crypto1Id, \"&vs_currencies=\").concat(crypto2Id), {\n                    method: 'GET',\n                    headers: {\n                        'Accept': 'application/json'\n                    }\n                });\n                if (response.ok) {\n                    var _data_crypto1Id;\n                    const data = await response.json();\n                    const price = (_data_crypto1Id = data[crypto1Id]) === null || _data_crypto1Id === void 0 ? void 0 : _data_crypto1Id[crypto2Id];\n                    if (price > 0) {\n                        console.log(\"✅ CoinGecko price: \".concat(config.crypto1, \"/\").concat(config.crypto2, \" = \").concat(price));\n                        return price;\n                    }\n                }\n            }\n        } catch (error) {\n            console.warn(\"⚠️ CoinGecko API failed:\", error);\n        }\n        // If all APIs fail, throw error to use static fallback\n        throw new Error('All price APIs failed');\n    } catch (error) {\n        console.error('❌ Error fetching real price:', error);\n        throw error;\n    }\n};\n// Helper function to map crypto symbols to CoinGecko IDs\nconst getCoinGeckoId = (symbol)=>{\n    const mapping = {\n        'BTC': 'bitcoin',\n        'ETH': 'ethereum',\n        'SOL': 'solana',\n        'ADA': 'cardano',\n        'DOT': 'polkadot',\n        'MATIC': 'matic-network',\n        'AVAX': 'avalanche-2',\n        'LINK': 'chainlink',\n        'UNI': 'uniswap',\n        'USDT': 'tether',\n        'USDC': 'usd-coin',\n        'BUSD': 'binance-usd',\n        'DAI': 'dai'\n    };\n    return mapping[symbol.toUpperCase()] || null;\n};\n// Helper function to get stablecoin exchange rates for real market data\nconst getStablecoinExchangeRate = async (crypto, stablecoin)=>{\n    try {\n        // For stablecoin-to-stablecoin, assume 1:1 rate\n        if (getCoinGeckoId(crypto) && getCoinGeckoId(stablecoin)) {\n            const cryptoId = getCoinGeckoId(crypto);\n            const stablecoinId = getCoinGeckoId(stablecoin);\n            if (cryptoId === stablecoinId) return 1.0; // Same currency\n            // Get real exchange rate from CoinGecko\n            const response = await fetch(\"https://api.coingecko.com/api/v3/simple/price?ids=\".concat(cryptoId, \"&vs_currencies=\").concat(stablecoinId));\n            if (response.ok) {\n                var _data_cryptoId;\n                const data = await response.json();\n                const rate = cryptoId && stablecoinId ? (_data_cryptoId = data[cryptoId]) === null || _data_cryptoId === void 0 ? void 0 : _data_cryptoId[stablecoinId] : null;\n                if (rate > 0) {\n                    console.log(\"\\uD83D\\uDCCA Stablecoin rate: \".concat(crypto, \"/\").concat(stablecoin, \" = \").concat(rate));\n                    return rate;\n                }\n            }\n        }\n        // Fallback: calculate via USD prices\n        const cryptoUSDPrice = getUSDPrice(crypto);\n        const stablecoinUSDPrice = getUSDPrice(stablecoin);\n        const rate = cryptoUSDPrice / stablecoinUSDPrice;\n        console.log(\"\\uD83D\\uDCCA Fallback stablecoin rate: \".concat(crypto, \"/\").concat(stablecoin, \" = \").concat(rate, \" (via USD)\"));\n        return rate;\n    } catch (error) {\n        console.error('Error fetching stablecoin exchange rate:', error);\n        // Final fallback\n        const cryptoUSDPrice = getUSDPrice(crypto);\n        const stablecoinUSDPrice = getUSDPrice(stablecoin);\n        return cryptoUSDPrice / stablecoinUSDPrice;\n    }\n};\n// Real-time price cache\nlet priceCache = {};\nlet lastPriceUpdate = 0;\nconst PRICE_CACHE_DURATION = 2000; // 2 seconds cache for real-time updates\n// Get real USD price with multiple fallback strategies\nconst getRealBinanceUSDPrice = async (crypto)=>{\n    const now = Date.now();\n    const cacheKey = crypto.toUpperCase();\n    // Return cached price if still valid (2 seconds)\n    if (priceCache[cacheKey] && now - lastPriceUpdate < PRICE_CACHE_DURATION) {\n        return priceCache[cacheKey];\n    }\n    try {\n        // Strategy 1: Try backend API to get real Binance USD price (via USDT)\n        try {\n            const pair = \"\".concat(crypto.toUpperCase(), \"-USDT\");\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.tradingApi.getMarketPrice(pair);\n            if (response && response.price > 0) {\n                priceCache[cacheKey] = response.price;\n                lastPriceUpdate = now;\n                console.log(\"\\uD83D\\uDCCA Backend USD price for \".concat(crypto, \": $\").concat(response.price));\n                return response.price;\n            }\n        } catch (error) {\n            console.warn(\"⚠️ Backend USD price failed for \".concat(crypto, \", trying CoinGecko:\"), error);\n        }\n        // Strategy 2: Try CoinGecko API as reliable fallback\n        try {\n            const coinGeckoId = getCoinGeckoId(crypto);\n            if (coinGeckoId) {\n                const response = await fetch(\"https://api.coingecko.com/api/v3/simple/price?ids=\".concat(coinGeckoId, \"&vs_currencies=usd\"), {\n                    method: 'GET',\n                    headers: {\n                        'Accept': 'application/json'\n                    }\n                });\n                if (response.ok) {\n                    var _data_coinGeckoId;\n                    const data = await response.json();\n                    const price = (_data_coinGeckoId = data[coinGeckoId]) === null || _data_coinGeckoId === void 0 ? void 0 : _data_coinGeckoId.usd;\n                    if (price && price > 0) {\n                        priceCache[cacheKey] = price;\n                        lastPriceUpdate = now;\n                        console.log(\"\\uD83D\\uDCCA CoinGecko USD price for \".concat(crypto, \": $\").concat(price));\n                        return price;\n                    }\n                }\n            }\n        } catch (error) {\n            console.warn(\"⚠️ CoinGecko USD price failed for \".concat(crypto, \":\"), error);\n        }\n        // If all APIs fail, throw error to use static fallback\n        throw new Error(\"All USD price APIs failed for \".concat(crypto));\n    } catch (error) {\n        console.error(\"❌ Failed to get USD price for \".concat(crypto, \":\"), error);\n        throw error;\n    }\n};\n// Synchronous helper function to get USD price (uses cached values or static fallback)\nconst getUSDPrice = (crypto)=>{\n    const cacheKey = crypto.toUpperCase();\n    // Return cached price if available\n    if (priceCache[cacheKey]) {\n        return priceCache[cacheKey];\n    }\n    // Fallback to static prices\n    return getStaticUSDPrice(crypto);\n};\nconst initialBaseConfig = {\n    tradingMode: \"SimpleSpot\",\n    crypto1: \"\",\n    crypto2: \"\",\n    baseBid: 100,\n    multiplier: 1.005,\n    numDigits: 4,\n    slippagePercent: 0.2,\n    incomeSplitCrypto1Percent: 50,\n    incomeSplitCrypto2Percent: 50,\n    preferredStablecoin: _lib_types__WEBPACK_IMPORTED_MODULE_2__.AVAILABLE_STABLECOINS[0]\n};\nconst initialTradingState = {\n    config: initialBaseConfig,\n    targetPriceRows: [],\n    orderHistory: [],\n    appSettings: _lib_types__WEBPACK_IMPORTED_MODULE_2__.DEFAULT_APP_SETTINGS,\n    currentMarketPrice: calculateInitialMarketPrice(initialBaseConfig),\n    // isBotActive: false, // Remove\n    botSystemStatus: 'Stopped',\n    crypto1Balance: 10,\n    crypto2Balance: 100000,\n    stablecoinBalance: 0,\n    backendStatus: 'unknown'\n};\nconst lastActionTimestampPerCounter = new Map();\n// LocalStorage persistence functions\nconst STORAGE_KEY = 'pluto_trading_state';\nconst saveStateToLocalStorage = (state)=>{\n    try {\n        if (true) {\n            const stateToSave = {\n                config: state.config,\n                targetPriceRows: state.targetPriceRows,\n                orderHistory: state.orderHistory,\n                appSettings: state.appSettings,\n                currentMarketPrice: state.currentMarketPrice,\n                crypto1Balance: state.crypto1Balance,\n                crypto2Balance: state.crypto2Balance,\n                stablecoinBalance: state.stablecoinBalance,\n                botSystemStatus: state.botSystemStatus,\n                timestamp: Date.now()\n            };\n            localStorage.setItem(STORAGE_KEY, JSON.stringify(stateToSave));\n        }\n    } catch (error) {\n        console.error('Failed to save state to localStorage:', error);\n    }\n};\nconst loadStateFromLocalStorage = ()=>{\n    try {\n        if (true) {\n            const savedState = localStorage.getItem(STORAGE_KEY);\n            if (savedState) {\n                const parsed = JSON.parse(savedState);\n                // Check if state is not too old (24 hours)\n                if (parsed.timestamp && Date.now() - parsed.timestamp < 24 * 60 * 60 * 1000) {\n                    return parsed;\n                }\n            }\n        }\n    } catch (error) {\n        console.error('Failed to load state from localStorage:', error);\n    }\n    return null;\n};\nconst tradingReducer = (state, action)=>{\n    switch(action.type){\n        case 'SET_CONFIG':\n            const newConfig = {\n                ...state.config,\n                ...action.payload\n            };\n            // If trading pair changes, reset market price (it will be re-calculated by effect)\n            if (action.payload.crypto1 || action.payload.crypto2) {\n                return {\n                    ...state,\n                    config: newConfig,\n                    currentMarketPrice: calculateInitialMarketPrice(newConfig)\n                };\n            }\n            return {\n                ...state,\n                config: newConfig\n            };\n        case 'SET_TARGET_PRICE_ROWS':\n            return {\n                ...state,\n                targetPriceRows: action.payload.sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }))\n            };\n        case 'ADD_TARGET_PRICE_ROW':\n            {\n                const newRows = [\n                    ...state.targetPriceRows,\n                    action.payload\n                ].sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }));\n                return {\n                    ...state,\n                    targetPriceRows: newRows\n                };\n            }\n        case 'UPDATE_TARGET_PRICE_ROW':\n            {\n                const updatedRows = state.targetPriceRows.map((row)=>row.id === action.payload.id ? action.payload : row).sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }));\n                return {\n                    ...state,\n                    targetPriceRows: updatedRows\n                };\n            }\n        case 'REMOVE_TARGET_PRICE_ROW':\n            {\n                const filteredRows = state.targetPriceRows.filter((row)=>row.id !== action.payload).sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }));\n                return {\n                    ...state,\n                    targetPriceRows: filteredRows\n                };\n            }\n        case 'ADD_ORDER_HISTORY_ENTRY':\n            return {\n                ...state,\n                orderHistory: [\n                    action.payload,\n                    ...state.orderHistory\n                ]\n            };\n        case 'CLEAR_ORDER_HISTORY':\n            return {\n                ...state,\n                orderHistory: []\n            };\n        case 'SET_APP_SETTINGS':\n            return {\n                ...state,\n                appSettings: {\n                    ...state.appSettings,\n                    ...action.payload\n                }\n            };\n        case 'SET_MARKET_PRICE':\n            return {\n                ...state,\n                currentMarketPrice: action.payload\n            };\n        // case 'SET_BOT_STATUS': // Removed\n        case 'UPDATE_BALANCES':\n            return {\n                ...state,\n                crypto1Balance: action.payload.crypto1 !== undefined ? action.payload.crypto1 : state.crypto1Balance,\n                crypto2Balance: action.payload.crypto2 !== undefined ? action.payload.crypto2 : state.crypto2Balance,\n                stablecoinBalance: action.payload.stablecoin !== undefined ? action.payload.stablecoin : state.stablecoinBalance\n            };\n        case 'SET_BALANCES':\n            return {\n                ...state,\n                crypto1Balance: action.payload.crypto1,\n                crypto2Balance: action.payload.crypto2,\n                stablecoinBalance: action.payload.stablecoin !== undefined ? action.payload.stablecoin : state.stablecoinBalance\n            };\n        case 'UPDATE_STABLECOIN_BALANCE':\n            return {\n                ...state,\n                stablecoinBalance: action.payload\n            };\n        case 'RESET_SESSION':\n            const configForReset = {\n                ...state.config\n            };\n            return {\n                ...initialTradingState,\n                config: configForReset,\n                appSettings: {\n                    ...state.appSettings\n                },\n                currentMarketPrice: calculateInitialMarketPrice(configForReset),\n                // Preserve current balances instead of resetting to initial values\n                crypto1Balance: state.crypto1Balance,\n                crypto2Balance: state.crypto2Balance,\n                stablecoinBalance: state.stablecoinBalance\n            };\n        case 'SET_BACKEND_STATUS':\n            return {\n                ...state,\n                backendStatus: action.payload\n            };\n        case 'SYSTEM_START_BOT_INITIATE':\n            // Continue from previous state instead of resetting\n            return {\n                ...state,\n                botSystemStatus: 'WarmingUp'\n            };\n        case 'SYSTEM_COMPLETE_WARMUP':\n            return {\n                ...state,\n                botSystemStatus: 'Running'\n            };\n        case 'SYSTEM_STOP_BOT':\n            return {\n                ...state,\n                botSystemStatus: 'Stopped'\n            };\n        case 'SYSTEM_RESET_BOT':\n            // Fresh Start: Clear all target price rows and order history completely\n            lastActionTimestampPerCounter.clear();\n            // Clear current session to force new session name generation\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            sessionManager.clearCurrentSession();\n            return {\n                ...state,\n                botSystemStatus: 'Stopped',\n                targetPriceRows: [],\n                orderHistory: []\n            };\n        case 'SET_TARGET_PRICE_ROWS':\n            return {\n                ...state,\n                targetPriceRows: action.payload\n            };\n        case 'RESET_FOR_NEW_CRYPTO':\n            return {\n                ...initialTradingState,\n                config: state.config,\n                backendStatus: state.backendStatus,\n                botSystemStatus: 'Stopped',\n                currentMarketPrice: 0,\n                // Preserve current balances instead of resetting to initial values\n                crypto1Balance: state.crypto1Balance,\n                crypto2Balance: state.crypto2Balance,\n                stablecoinBalance: state.stablecoinBalance\n            };\n        default:\n            return state;\n    }\n};\nconst TradingContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// SIMPLIFIED LOGIC - NO COMPLEX COOLDOWNS\nconst TradingProvider = (param)=>{\n    let { children } = param;\n    _s();\n    // Initialize state with localStorage data if available\n    const initializeState = ()=>{\n        // Check if this is a new session from URL parameter\n        if (true) {\n            const urlParams = new URLSearchParams(window.location.search);\n            const isNewSession = urlParams.get('newSession') === 'true';\n            if (isNewSession) {\n                console.log('🆕 New session requested - starting with completely fresh state');\n                // Note: URL parameter will be cleared in useEffect to avoid render-time state updates\n                return initialTradingState;\n            }\n        }\n        // Check if this is a new window/tab by checking if we have a current session\n        const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n        const currentSessionId = sessionManager.getCurrentSessionId();\n        // If no current session exists for this window, start fresh\n        if (!currentSessionId) {\n            console.log('🆕 New window detected - starting with fresh state');\n            return initialTradingState;\n        }\n        // If we have a session, try to load the session data first (priority over localStorage)\n        const currentSession = sessionManager.loadSession(currentSessionId);\n        if (currentSession) {\n            console.log('🔄 Loading session data with balances:', {\n                crypto1: currentSession.crypto1Balance,\n                crypto2: currentSession.crypto2Balance,\n                stablecoin: currentSession.stablecoinBalance\n            });\n            return {\n                ...initialTradingState,\n                config: currentSession.config,\n                targetPriceRows: currentSession.targetPriceRows,\n                orderHistory: currentSession.orderHistory,\n                currentMarketPrice: currentSession.currentMarketPrice,\n                crypto1Balance: currentSession.crypto1Balance,\n                crypto2Balance: currentSession.crypto2Balance,\n                stablecoinBalance: currentSession.stablecoinBalance,\n                botSystemStatus: currentSession.isActive ? 'Running' : 'Stopped'\n            };\n        }\n        // Fallback to localStorage if session loading fails\n        const savedState = loadStateFromLocalStorage();\n        if (savedState) {\n            return {\n                ...initialTradingState,\n                ...savedState\n            };\n        }\n        return initialTradingState;\n    };\n    const [state, dispatch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useReducer)(tradingReducer, initializeState());\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__.useToast)();\n    // Clear URL parameter after component mounts to avoid render-time state updates\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            if (true) {\n                const urlParams = new URLSearchParams(window.location.search);\n                const isNewSession = urlParams.get('newSession') === 'true';\n                if (isNewSession) {\n                    // Clear the URL parameter to avoid confusion\n                    const newUrl = window.location.pathname;\n                    window.history.replaceState({}, '', newUrl);\n                }\n            }\n        }\n    }[\"TradingProvider.useEffect\"], []); // Run only once on mount\n    const audioRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Removed processing locks and cooldowns for continuous trading\n    // Initialize fetchMarketPrice to get real Binance prices with fallback\n    const fetchMarketPrice = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[fetchMarketPrice]\": async ()=>{\n            try {\n                // Don't fetch price if either crypto is not selected\n                if (!state.config.crypto1 || !state.config.crypto2) {\n                    dispatch({\n                        type: 'SET_MARKET_PRICE',\n                        payload: 0\n                    });\n                    return;\n                }\n                let price;\n                try {\n                    // Try to get real Binance prices with timeout protection\n                    const timeoutPromise = new Promise({\n                        \"TradingProvider.useCallback[fetchMarketPrice]\": (_, reject)=>{\n                            setTimeout({\n                                \"TradingProvider.useCallback[fetchMarketPrice]\": ()=>reject(new Error('Price fetch timeout'))\n                            }[\"TradingProvider.useCallback[fetchMarketPrice]\"], 5000); // 5 second timeout\n                        }\n                    }[\"TradingProvider.useCallback[fetchMarketPrice]\"]);\n                    if (state.config.tradingMode === \"StablecoinSwap\") {\n                        // For StablecoinSwap mode, try real Binance USD prices with timeout\n                        const pricePromise = Promise.all([\n                            getRealBinanceUSDPrice(state.config.crypto1),\n                            getRealBinanceUSDPrice(state.config.crypto2)\n                        ]).then({\n                            \"TradingProvider.useCallback[fetchMarketPrice].pricePromise\": (param)=>{\n                                let [crypto1USDPrice, crypto2USDPrice] = param;\n                                const calculatedPrice = crypto1USDPrice / crypto2USDPrice;\n                                console.log(\"\\uD83D\\uDCCA StablecoinSwap Real Binance Price:\\n              - \".concat(state.config.crypto1, \" USD Price: $\").concat(crypto1USDPrice.toLocaleString(), \"\\n              - \").concat(state.config.crypto2, \" USD Price: $\").concat(crypto2USDPrice.toLocaleString(), \"\\n              - Market Price (\").concat(state.config.crypto1, \"/\").concat(state.config.crypto2, \"): \").concat(calculatedPrice.toFixed(6)));\n                                return calculatedPrice;\n                            }\n                        }[\"TradingProvider.useCallback[fetchMarketPrice].pricePromise\"]);\n                        price = await Promise.race([\n                            pricePromise,\n                            timeoutPromise\n                        ]);\n                    } else {\n                        // For SimpleSpot mode, try real Binance API via backend with timeout\n                        const pricePromise = getRealBinancePrice(state.config);\n                        price = await Promise.race([\n                            pricePromise,\n                            timeoutPromise\n                        ]);\n                    }\n                    dispatch({\n                        type: 'SET_MARKET_PRICE',\n                        payload: price\n                    });\n                    console.log(\"✅ Successfully fetched real Binance price: \".concat(price));\n                } catch (error) {\n                    console.warn('⚠️ Real Binance API failed, using static price:', error);\n                    // Use static fallback if real API fails or times out\n                    const fallbackPrice = calculateStaticMarketPrice(state.config);\n                    dispatch({\n                        type: 'SET_MARKET_PRICE',\n                        payload: fallbackPrice\n                    });\n                }\n            } catch (error) {\n                console.error('❌ Critical error in fetchMarketPrice:', error);\n                const fallbackPrice = calculateStaticMarketPrice(state.config);\n                dispatch({\n                    type: 'SET_MARKET_PRICE',\n                    payload: fallbackPrice\n                });\n            }\n        }\n    }[\"TradingProvider.useCallback[fetchMarketPrice]\"], [\n        state.config,\n        dispatch\n    ]);\n    // Market price real-time updates from Binance\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            // Initial fetch\n            fetchMarketPrice();\n            // Set up real-time price updates every 2 seconds from Binance\n            const priceUpdateInterval = setInterval({\n                \"TradingProvider.useEffect.priceUpdateInterval\": async ()=>{\n                    const networkMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.NetworkMonitor.getInstance();\n                    if (networkMonitor.getStatus().isOnline) {\n                        try {\n                            await fetchMarketPrice();\n                            console.log('📊 Binance price updated');\n                        } catch (error) {\n                            console.warn('⚠️ Failed to update price from Binance:', error);\n                        }\n                    }\n                }\n            }[\"TradingProvider.useEffect.priceUpdateInterval\"], 2000); // Update every 2 seconds as requested\n            return ({\n                \"TradingProvider.useEffect\": ()=>{\n                    clearInterval(priceUpdateInterval);\n                }\n            })[\"TradingProvider.useEffect\"];\n        }\n    }[\"TradingProvider.useEffect\"], [\n        fetchMarketPrice,\n        state.config.crypto1,\n        state.config.crypto2,\n        state.config.tradingMode\n    ]);\n    // Audio initialization and user interaction detection\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            if (true) {\n                audioRef.current = new Audio();\n                // Add user interaction listener to enable audio\n                const enableAudio = {\n                    \"TradingProvider.useEffect.enableAudio\": ()=>{\n                        if (audioRef.current) {\n                            // Try to play a silent audio to enable audio context\n                            audioRef.current.volume = 0;\n                            audioRef.current.play().then({\n                                \"TradingProvider.useEffect.enableAudio\": ()=>{\n                                    if (audioRef.current) {\n                                        audioRef.current.volume = 1;\n                                    }\n                                    console.log('🔊 Audio context enabled after user interaction');\n                                }\n                            }[\"TradingProvider.useEffect.enableAudio\"]).catch({\n                                \"TradingProvider.useEffect.enableAudio\": ()=>{\n                                // Still blocked, but we tried\n                                }\n                            }[\"TradingProvider.useEffect.enableAudio\"]);\n                        }\n                        // Remove the listener after first interaction\n                        document.removeEventListener('click', enableAudio);\n                        document.removeEventListener('keydown', enableAudio);\n                    }\n                }[\"TradingProvider.useEffect.enableAudio\"];\n                // Listen for first user interaction\n                document.addEventListener('click', enableAudio);\n                document.addEventListener('keydown', enableAudio);\n                return ({\n                    \"TradingProvider.useEffect\": ()=>{\n                        document.removeEventListener('click', enableAudio);\n                        document.removeEventListener('keydown', enableAudio);\n                    }\n                })[\"TradingProvider.useEffect\"];\n            }\n        }\n    }[\"TradingProvider.useEffect\"], []);\n    const playSound = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[playSound]\": (soundKey)=>{\n            // Get current session's alarm settings, fallback to global app settings\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            const currentSessionId = sessionManager.getCurrentSessionId();\n            let alarmSettings = state.appSettings; // Default fallback\n            if (currentSessionId) {\n                const currentSession = sessionManager.loadSession(currentSessionId);\n                if (currentSession && currentSession.alarmSettings) {\n                    alarmSettings = currentSession.alarmSettings;\n                }\n            }\n            if (alarmSettings.soundAlertsEnabled && audioRef.current) {\n                let soundPath;\n                if (soundKey === 'soundOrderExecution' && alarmSettings.alertOnOrderExecution) {\n                    soundPath = alarmSettings.soundOrderExecution;\n                } else if (soundKey === 'soundError' && alarmSettings.alertOnError) {\n                    soundPath = alarmSettings.soundError;\n                }\n                if (soundPath) {\n                    try {\n                        // Stop any currently playing audio first\n                        audioRef.current.pause();\n                        audioRef.current.currentTime = 0;\n                        // Set the source and load the audio\n                        audioRef.current.src = soundPath;\n                        audioRef.current.load(); // Explicitly load the audio\n                        // Add error handler for audio loading\n                        audioRef.current.onerror = ({\n                            \"TradingProvider.useCallback[playSound]\": (e)=>{\n                                console.warn(\"⚠️ Audio loading failed for \".concat(soundPath, \":\"), e);\n                                // Try fallback sound\n                                if (soundPath !== '/sounds/chime2.wav' && audioRef.current) {\n                                    audioRef.current.src = '/sounds/chime2.wav';\n                                    audioRef.current.load();\n                                }\n                            }\n                        })[\"TradingProvider.useCallback[playSound]\"];\n                        // Play the sound and limit duration to 2 seconds\n                        const playPromise = audioRef.current.play();\n                        if (playPromise !== undefined) {\n                            playPromise.then({\n                                \"TradingProvider.useCallback[playSound]\": ()=>{\n                                    // Set a timeout to pause the audio after 2 seconds\n                                    setTimeout({\n                                        \"TradingProvider.useCallback[playSound]\": ()=>{\n                                            if (audioRef.current && !audioRef.current.paused) {\n                                                audioRef.current.pause();\n                                                audioRef.current.currentTime = 0; // Reset for next play\n                                            }\n                                        }\n                                    }[\"TradingProvider.useCallback[playSound]\"], 2000); // 2 seconds\n                                }\n                            }[\"TradingProvider.useCallback[playSound]\"]).catch({\n                                \"TradingProvider.useCallback[playSound]\": (err)=>{\n                                    // Handle different types of audio errors gracefully\n                                    const errorMessage = err.message || String(err);\n                                    if (errorMessage.includes('user didn\\'t interact') || errorMessage.includes('autoplay')) {\n                                        console.log('🔇 Audio autoplay blocked by browser - user interaction required');\n                                    // Could show a notification to user about enabling audio\n                                    } else if (errorMessage.includes('interrupted') || errorMessage.includes('supported source')) {\n                                    // These are expected errors, don't log them\n                                    } else {\n                                        console.warn(\"⚠️ Audio playback failed:\", errorMessage);\n                                    }\n                                }\n                            }[\"TradingProvider.useCallback[playSound]\"]);\n                        }\n                    } catch (error) {\n                        console.warn(\"⚠️ Audio setup failed for \".concat(soundPath, \":\"), error);\n                    }\n                }\n            }\n        }\n    }[\"TradingProvider.useCallback[playSound]\"], [\n        state.appSettings\n    ]);\n    // Enhanced Telegram notification function with error handling\n    const sendTelegramNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[sendTelegramNotification]\": async function(message) {\n            let isError = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n            try {\n                const telegramToken = localStorage.getItem('telegram_bot_token');\n                const telegramChatId = localStorage.getItem('telegram_chat_id');\n                if (!telegramToken || !telegramChatId) {\n                    console.log('Telegram not configured - skipping notification');\n                    return;\n                }\n                const response = await fetch(\"https://api.telegram.org/bot\".concat(telegramToken, \"/sendMessage\"), {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        chat_id: telegramChatId,\n                        text: message,\n                        parse_mode: 'HTML'\n                    })\n                });\n                if (!response.ok) {\n                    console.error('Failed to send Telegram notification:', response.statusText);\n                    // Don't retry error notifications to avoid infinite loops\n                    if (!isError) {\n                        await sendTelegramErrorNotification('Telegram API Error', \"Failed to send notification: \".concat(response.statusText));\n                    }\n                } else {\n                    console.log('✅ Telegram notification sent successfully');\n                }\n            } catch (error) {\n                console.error('Error sending Telegram notification:', error);\n                // Network disconnection detected\n                if (!isError) {\n                    await sendTelegramErrorNotification('Network Disconnection', \"Failed to connect to Telegram API: \".concat(error));\n                }\n            }\n        }\n    }[\"TradingProvider.useCallback[sendTelegramNotification]\"], []);\n    // Specific error notification functions\n    const sendTelegramErrorNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[sendTelegramErrorNotification]\": async (errorType, errorMessage)=>{\n            const timestamp = new Date().toLocaleString();\n            const message = \"⚠️ <b>ERROR ALERT</b>\\n\\n\" + \"\\uD83D\\uDD34 <b>Type:</b> \".concat(errorType, \"\\n\") + \"\\uD83D\\uDCDD <b>Message:</b> \".concat(errorMessage, \"\\n\") + \"⏰ <b>Time:</b> \".concat(timestamp, \"\\n\") + \"\\uD83E\\uDD16 <b>Bot:</b> \".concat(state.config.crypto1, \"/\").concat(state.config.crypto2, \" \").concat(state.config.tradingMode);\n            await sendTelegramNotification(message, true); // Mark as error to prevent recursion\n        }\n    }[\"TradingProvider.useCallback[sendTelegramErrorNotification]\"], [\n        state.config,\n        sendTelegramNotification\n    ]);\n    const sendLowBalanceNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[sendLowBalanceNotification]\": async (crypto, currentBalance, requiredAmount)=>{\n            const timestamp = new Date().toLocaleString();\n            const message = \"\\uD83D\\uDCB0 <b>LOW BALANCE ALERT</b>\\n\\n\" + \"\\uD83E\\uDE99 <b>Currency:</b> \".concat(crypto, \"\\n\") + \"\\uD83D\\uDCCA <b>Current Balance:</b> \".concat(currentBalance.toFixed(6), \" \").concat(crypto, \"\\n\") + \"⚡ <b>Required Amount:</b> \".concat(requiredAmount.toFixed(6), \" \").concat(crypto, \"\\n\") + \"\\uD83D\\uDCC9 <b>Shortage:</b> \".concat((requiredAmount - currentBalance).toFixed(6), \" \").concat(crypto, \"\\n\") + \"⏰ <b>Time:</b> \".concat(timestamp, \"\\n\") + \"\\uD83E\\uDD16 <b>Bot:</b> \".concat(state.config.crypto1, \"/\").concat(state.config.crypto2, \" \").concat(state.config.tradingMode);\n            await sendTelegramNotification(message);\n        }\n    }[\"TradingProvider.useCallback[sendLowBalanceNotification]\"], [\n        state.config,\n        sendTelegramNotification\n    ]);\n    const sendAPIErrorNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[sendAPIErrorNotification]\": async (apiName, errorDetails)=>{\n            const timestamp = new Date().toLocaleString();\n            const message = \"\\uD83D\\uDD0C <b>API ERROR ALERT</b>\\n\\n\" + \"\\uD83C\\uDF10 <b>API:</b> \".concat(apiName, \"\\n\") + \"❌ <b>Error:</b> \".concat(errorDetails, \"\\n\") + \"⏰ <b>Time:</b> \".concat(timestamp, \"\\n\") + \"\\uD83E\\uDD16 <b>Bot:</b> \".concat(state.config.crypto1, \"/\").concat(state.config.crypto2, \" \").concat(state.config.tradingMode);\n            await sendTelegramNotification(message);\n        }\n    }[\"TradingProvider.useCallback[sendAPIErrorNotification]\"], [\n        state.config,\n        sendTelegramNotification\n    ]);\n    // Effect to update market price when trading pair changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n        // When crypto1 or crypto2 (parts of state.config) change,\n        // the fetchMarketPrice useCallback gets a new reference.\n        // The useEffect above (which depends on fetchMarketPrice)\n        // will re-run, clear the old interval, make an initial fetch with the new config,\n        // and set up a new interval.\n        // The reducer for SET_CONFIG also sets an initial market price if crypto1/crypto2 changes.\n        // Thus, no explicit dispatch is needed here.\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.config.crypto1,\n        state.config.crypto2\n    ]); // Dependencies ensure this reacts to pair changes\n    const setTargetPrices = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[setTargetPrices]\": (prices)=>{\n            if (!prices || !Array.isArray(prices)) return;\n            // Sort prices from lowest to highest for proper counter assignment\n            const sortedPrices = [\n                ...prices\n            ].filter({\n                \"TradingProvider.useCallback[setTargetPrices].sortedPrices\": (price)=>!isNaN(price) && price > 0\n            }[\"TradingProvider.useCallback[setTargetPrices].sortedPrices\"]).sort({\n                \"TradingProvider.useCallback[setTargetPrices].sortedPrices\": (a, b)=>a - b\n            }[\"TradingProvider.useCallback[setTargetPrices].sortedPrices\"]);\n            const newRows = sortedPrices.map({\n                \"TradingProvider.useCallback[setTargetPrices].newRows\": (price, index)=>{\n                    const existingRow = state.targetPriceRows.find({\n                        \"TradingProvider.useCallback[setTargetPrices].newRows.existingRow\": (r)=>r.targetPrice === price\n                    }[\"TradingProvider.useCallback[setTargetPrices].newRows.existingRow\"]);\n                    if (existingRow) {\n                        // Update counter for existing row based on sorted position\n                        return {\n                            ...existingRow,\n                            counter: index + 1\n                        };\n                    }\n                    return {\n                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                        counter: index + 1,\n                        status: 'Free',\n                        orderLevel: 0,\n                        valueLevel: state.config.baseBid,\n                        targetPrice: price\n                    };\n                }\n            }[\"TradingProvider.useCallback[setTargetPrices].newRows\"]);\n            dispatch({\n                type: 'SET_TARGET_PRICE_ROWS',\n                payload: newRows\n            });\n        }\n    }[\"TradingProvider.useCallback[setTargetPrices]\"], [\n        state.targetPriceRows,\n        state.config.baseBid,\n        dispatch\n    ]);\n    // Core Trading Logic (Simulated) - CONTINUOUS TRADING VERSION\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            // Check network status first\n            const networkMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.NetworkMonitor.getInstance();\n            const isOnline = networkMonitor.getStatus().isOnline;\n            // Only check essential conditions AND network status\n            if (state.botSystemStatus !== 'Running' || state.targetPriceRows.length === 0 || state.currentMarketPrice <= 0 || !isOnline) {\n                if (!isOnline && state.botSystemStatus === 'Running') {\n                    console.log('🔴 Trading paused - network offline');\n                }\n                return;\n            }\n            // Execute trading logic immediately - no locks, no cooldowns, no delays\n            const { config, currentMarketPrice, targetPriceRows, crypto1Balance, crypto2Balance } = state;\n            const sortedRowsForLogic = [\n                ...targetPriceRows\n            ].sort({\n                \"TradingProvider.useEffect.sortedRowsForLogic\": (a, b)=>a.targetPrice - b.targetPrice\n            }[\"TradingProvider.useEffect.sortedRowsForLogic\"]);\n            // Use mutable variables for balance tracking within this cycle\n            let currentCrypto1Balance = crypto1Balance;\n            let currentCrypto2Balance = crypto2Balance;\n            let actionsTaken = 0;\n            console.log(\"\\uD83D\\uDE80 CONTINUOUS TRADING: Price $\".concat(currentMarketPrice.toFixed(2), \" | Targets: \").concat(sortedRowsForLogic.length, \" | Balance: $\").concat(currentCrypto2Balance, \" \").concat(config.crypto2));\n            // Show which targets are in range\n            const targetsInRange = sortedRowsForLogic.filter({\n                \"TradingProvider.useEffect.targetsInRange\": (row)=>{\n                    const diffPercent = Math.abs(currentMarketPrice - row.targetPrice) / currentMarketPrice * 100;\n                    return diffPercent <= config.slippagePercent;\n                }\n            }[\"TradingProvider.useEffect.targetsInRange\"]);\n            if (targetsInRange.length > 0) {\n                console.log(\"\\uD83C\\uDFAF TARGETS IN RANGE (\\xb1\".concat(config.slippagePercent, \"%):\"), targetsInRange.map({\n                    \"TradingProvider.useEffect\": (row)=>\"Counter \".concat(row.counter, \" (\").concat(row.status, \")\")\n                }[\"TradingProvider.useEffect\"]));\n            }\n            // CONTINUOUS TRADING LOGIC: Process all targets immediately\n            for(let i = 0; i < sortedRowsForLogic.length; i++){\n                const activeRow = sortedRowsForLogic[i];\n                const priceDiffPercent = Math.abs(currentMarketPrice - activeRow.targetPrice) / currentMarketPrice * 100;\n                // STEP 1: Check if TargetRowN is triggered (within slippage range)\n                if (priceDiffPercent <= config.slippagePercent) {\n                    if (config.tradingMode === \"SimpleSpot\") {\n                        // STEP 2: Evaluate Triggered TargetRowN's Status\n                        if (activeRow.status === \"Free\") {\n                            // STEP 2a: Execute BUY on TargetRowN\n                            const costCrypto2 = activeRow.valueLevel;\n                            if (currentCrypto2Balance >= costCrypto2) {\n                                const amountCrypto1Bought = costCrypto2 / currentMarketPrice;\n                                const updatedRow = {\n                                    ...activeRow,\n                                    status: \"Full\",\n                                    orderLevel: activeRow.orderLevel + 1,\n                                    valueLevel: config.baseBid * Math.pow(config.multiplier, activeRow.orderLevel + 1),\n                                    crypto1AmountHeld: amountCrypto1Bought,\n                                    originalCostCrypto2: costCrypto2,\n                                    crypto1Var: amountCrypto1Bought,\n                                    crypto2Var: -costCrypto2\n                                };\n                                dispatch({\n                                    type: 'UPDATE_TARGET_PRICE_ROW',\n                                    payload: updatedRow\n                                });\n                                dispatch({\n                                    type: 'UPDATE_BALANCES',\n                                    payload: {\n                                        crypto1: currentCrypto1Balance + amountCrypto1Bought,\n                                        crypto2: currentCrypto2Balance - costCrypto2\n                                    }\n                                });\n                                dispatch({\n                                    type: 'ADD_ORDER_HISTORY_ENTRY',\n                                    payload: {\n                                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                        timestamp: Date.now(),\n                                        pair: \"\".concat(config.crypto1, \"/\").concat(config.crypto2),\n                                        crypto1: config.crypto1,\n                                        orderType: \"BUY\",\n                                        amountCrypto1: amountCrypto1Bought,\n                                        avgPrice: currentMarketPrice,\n                                        valueCrypto2: costCrypto2,\n                                        price1: currentMarketPrice,\n                                        crypto1Symbol: config.crypto1 || '',\n                                        crypto2Symbol: config.crypto2 || ''\n                                    }\n                                });\n                                console.log(\"✅ BUY: Counter \".concat(activeRow.counter, \" bought \").concat(amountCrypto1Bought.toFixed(6), \" \").concat(config.crypto1, \" at $\").concat(currentMarketPrice.toFixed(2)));\n                                toast({\n                                    title: \"BUY Executed\",\n                                    description: \"Counter \".concat(activeRow.counter, \": \").concat(amountCrypto1Bought.toFixed(6), \" \").concat(config.crypto1),\n                                    duration: 2000\n                                });\n                                playSound('soundOrderExecution');\n                                // Send Telegram notification for BUY\n                                sendTelegramNotification(\"\\uD83D\\uDFE2 <b>BUY EXECUTED</b>\\n\" + \"\\uD83D\\uDCCA Counter: \".concat(activeRow.counter, \"\\n\") + \"\\uD83D\\uDCB0 Amount: \".concat(amountCrypto1Bought.toFixed(6), \" \").concat(config.crypto1, \"\\n\") + \"\\uD83D\\uDCB5 Price: $\".concat(currentMarketPrice.toFixed(2), \"\\n\") + \"\\uD83D\\uDCB8 Cost: $\".concat(costCrypto2.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\\uD83D\\uDCC8 Mode: Simple Spot\");\n                                actionsTaken++;\n                                // Update local balance for subsequent checks in this cycle\n                                currentCrypto2Balance -= costCrypto2;\n                                currentCrypto1Balance += amountCrypto1Bought;\n                            } else {\n                                // Insufficient balance - send Telegram notification\n                                console.log(\"❌ Insufficient \".concat(config.crypto2, \" balance: \").concat(currentCrypto2Balance.toFixed(6), \" < \").concat(costCrypto2.toFixed(6)));\n                                sendLowBalanceNotification(config.crypto2, currentCrypto2Balance, costCrypto2).catch(console.error);\n                            }\n                        }\n                        // STEP 3: Check and Process Inferior TargetRow (TargetRowN_minus_1) - ALWAYS, regardless of BUY\n                        const currentCounter = activeRow.counter;\n                        const inferiorRow = sortedRowsForLogic.find({\n                            \"TradingProvider.useEffect.inferiorRow\": (row)=>row.counter === currentCounter - 1\n                        }[\"TradingProvider.useEffect.inferiorRow\"]);\n                        if (inferiorRow && inferiorRow.status === \"Full\" && inferiorRow.crypto1AmountHeld && inferiorRow.originalCostCrypto2) {\n                            const amountCrypto1ToSell = inferiorRow.crypto1AmountHeld;\n                            const crypto2Received = amountCrypto1ToSell * currentMarketPrice;\n                            const realizedProfit = crypto2Received - inferiorRow.originalCostCrypto2;\n                            // Calculate Crypto1 profit/loss based on income split percentage\n                            const realizedProfitCrypto1 = currentMarketPrice > 0 ? realizedProfit * config.incomeSplitCrypto1Percent / 100 / currentMarketPrice : 0;\n                            const updatedInferiorRow = {\n                                ...inferiorRow,\n                                status: \"Free\",\n                                crypto1AmountHeld: undefined,\n                                originalCostCrypto2: undefined,\n                                valueLevel: config.baseBid * Math.pow(config.multiplier, inferiorRow.orderLevel),\n                                crypto1Var: -amountCrypto1ToSell,\n                                crypto2Var: crypto2Received\n                            };\n                            dispatch({\n                                type: 'UPDATE_TARGET_PRICE_ROW',\n                                payload: updatedInferiorRow\n                            });\n                            dispatch({\n                                type: 'UPDATE_BALANCES',\n                                payload: {\n                                    crypto1: currentCrypto1Balance - amountCrypto1ToSell,\n                                    crypto2: currentCrypto2Balance + crypto2Received\n                                }\n                            });\n                            dispatch({\n                                type: 'ADD_ORDER_HISTORY_ENTRY',\n                                payload: {\n                                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                    timestamp: Date.now(),\n                                    pair: \"\".concat(config.crypto1, \"/\").concat(config.crypto2),\n                                    crypto1: config.crypto1,\n                                    orderType: \"SELL\",\n                                    amountCrypto1: amountCrypto1ToSell,\n                                    avgPrice: currentMarketPrice,\n                                    valueCrypto2: crypto2Received,\n                                    price1: currentMarketPrice,\n                                    crypto1Symbol: config.crypto1 || '',\n                                    crypto2Symbol: config.crypto2 || '',\n                                    realizedProfitLossCrypto2: realizedProfit,\n                                    realizedProfitLossCrypto1: realizedProfitCrypto1\n                                }\n                            });\n                            console.log(\"✅ SELL: Counter \".concat(currentCounter - 1, \" sold \").concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \". Profit: $\").concat(realizedProfit.toFixed(2)));\n                            toast({\n                                title: \"SELL Executed\",\n                                description: \"Counter \".concat(currentCounter - 1, \": Profit $\").concat(realizedProfit.toFixed(2)),\n                                duration: 2000\n                            });\n                            playSound('soundOrderExecution');\n                            // Send Telegram notification for SELL\n                            const profitEmoji = realizedProfit > 0 ? '📈' : realizedProfit < 0 ? '📉' : '➖';\n                            sendTelegramNotification(\"\\uD83D\\uDD34 <b>SELL EXECUTED</b>\\n\" + \"\\uD83D\\uDCCA Counter: \".concat(currentCounter - 1, \"\\n\") + \"\\uD83D\\uDCB0 Amount: \".concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \"\\n\") + \"\\uD83D\\uDCB5 Price: $\".concat(currentMarketPrice.toFixed(2), \"\\n\") + \"\\uD83D\\uDCB8 Received: $\".concat(crypto2Received.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\".concat(profitEmoji, \" Profit: $\").concat(realizedProfit.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\\uD83D\\uDCC8 Mode: Simple Spot\");\n                            actionsTaken++;\n                            // Update local balance for subsequent checks in this cycle\n                            currentCrypto1Balance -= amountCrypto1ToSell;\n                            currentCrypto2Balance += crypto2Received;\n                        }\n                    } else if (config.tradingMode === \"StablecoinSwap\") {\n                        // STEP 2: Evaluate Triggered TargetRowN's Status (Stablecoin Swap Mode)\n                        if (activeRow.status === \"Free\") {\n                            // STEP 2a: Execute Two-Step \"Buy Crypto1 via Stablecoin\" on TargetRowN\n                            const amountCrypto2ToUse = activeRow.valueLevel; // Value = BaseBid * (Multiplier ^ Level)\n                            if (currentCrypto2Balance >= amountCrypto2ToUse) {\n                                // Step 1: Sell Crypto2 for PreferredStablecoin\n                                // Use current fluctuating market price for more realistic P/L\n                                const crypto2USDPrice = getUSDPrice(config.crypto2 || 'USDT');\n                                const stablecoinUSDPrice = getUSDPrice(config.preferredStablecoin || 'USDT');\n                                const crypto2StablecoinPrice = crypto2USDPrice / stablecoinUSDPrice;\n                                const stablecoinObtained = amountCrypto2ToUse * crypto2StablecoinPrice;\n                                // Step 2: Buy Crypto1 with Stablecoin\n                                // Use current fluctuating market price (this is the key for P/L calculation)\n                                const crypto1USDPrice = getUSDPrice(config.crypto1 || 'BTC');\n                                // Apply current market price fluctuation to Crypto1 price\n                                const fluctuatedCrypto1Price = crypto1USDPrice * (state.currentMarketPrice / (crypto1USDPrice / crypto2USDPrice));\n                                const crypto1StablecoinPrice = fluctuatedCrypto1Price / stablecoinUSDPrice;\n                                const crypto1Bought = stablecoinObtained / crypto1StablecoinPrice;\n                                // Update Row N: Free → Full, Level++, Value recalculated\n                                const newLevel = activeRow.orderLevel + 1;\n                                const newValue = config.baseBid * Math.pow(config.multiplier, newLevel);\n                                const updatedRow = {\n                                    ...activeRow,\n                                    status: \"Full\",\n                                    orderLevel: newLevel,\n                                    valueLevel: newValue,\n                                    crypto1AmountHeld: crypto1Bought,\n                                    originalCostCrypto2: amountCrypto2ToUse,\n                                    crypto1Var: crypto1Bought,\n                                    crypto2Var: -amountCrypto2ToUse\n                                };\n                                console.log(\"\\uD83D\\uDCB0 StablecoinSwap BUY - Setting originalCostCrypto2:\", {\n                                    counter: activeRow.counter,\n                                    amountCrypto2ToUse: amountCrypto2ToUse,\n                                    crypto1Bought: crypto1Bought,\n                                    originalCostCrypto2: amountCrypto2ToUse,\n                                    newLevel: newLevel,\n                                    crypto1StablecoinPrice: crypto1StablecoinPrice,\n                                    fluctuatedCrypto1Price: fluctuatedCrypto1Price,\n                                    currentMarketPrice: state.currentMarketPrice\n                                });\n                                dispatch({\n                                    type: 'UPDATE_TARGET_PRICE_ROW',\n                                    payload: updatedRow\n                                });\n                                dispatch({\n                                    type: 'UPDATE_BALANCES',\n                                    payload: {\n                                        crypto1: currentCrypto1Balance + crypto1Bought,\n                                        crypto2: currentCrypto2Balance - amountCrypto2ToUse\n                                    }\n                                });\n                                // Add history entries for both steps of the stablecoin swap\n                                // Calculate P/L for the crypto2 sell based on price fluctuation\n                                const baseCrypto2Price = getUSDPrice(config.crypto2) / getUSDPrice(config.preferredStablecoin);\n                                const estimatedProfitCrypto2 = amountCrypto2ToUse * (crypto2StablecoinPrice - baseCrypto2Price);\n                                const estimatedProfitCrypto1 = estimatedProfitCrypto2 / crypto1StablecoinPrice;\n                                console.log(\"\\uD83D\\uDCCA StablecoinSwap Step A SELL P/L:\", {\n                                    crypto2: config.crypto2,\n                                    amountSold: amountCrypto2ToUse,\n                                    currentPrice: crypto2StablecoinPrice,\n                                    basePrice: baseCrypto2Price,\n                                    estimatedProfitCrypto2: estimatedProfitCrypto2,\n                                    estimatedProfitCrypto1: estimatedProfitCrypto1\n                                });\n                                dispatch({\n                                    type: 'ADD_ORDER_HISTORY_ENTRY',\n                                    payload: {\n                                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                        timestamp: Date.now(),\n                                        pair: \"\".concat(config.crypto2, \"/\").concat(config.preferredStablecoin),\n                                        crypto1: config.crypto2,\n                                        orderType: \"SELL\",\n                                        amountCrypto1: amountCrypto2ToUse,\n                                        avgPrice: crypto2StablecoinPrice,\n                                        valueCrypto2: stablecoinObtained,\n                                        price1: crypto2StablecoinPrice,\n                                        crypto1Symbol: config.crypto2 || '',\n                                        crypto2Symbol: config.preferredStablecoin || ''\n                                    }\n                                });\n                                dispatch({\n                                    type: 'ADD_ORDER_HISTORY_ENTRY',\n                                    payload: {\n                                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                        timestamp: Date.now(),\n                                        pair: \"\".concat(config.crypto1, \"/\").concat(config.preferredStablecoin),\n                                        crypto1: config.crypto1,\n                                        orderType: \"BUY\",\n                                        amountCrypto1: crypto1Bought,\n                                        avgPrice: crypto1StablecoinPrice,\n                                        valueCrypto2: stablecoinObtained,\n                                        price1: crypto1StablecoinPrice,\n                                        crypto1Symbol: config.crypto1 || '',\n                                        crypto2Symbol: config.preferredStablecoin || ''\n                                    }\n                                });\n                                console.log(\"✅ STABLECOIN BUY: Counter \".concat(activeRow.counter, \" | Step 1: Sold \").concat(amountCrypto2ToUse, \" \").concat(config.crypto2, \" → \").concat(stablecoinObtained.toFixed(2), \" \").concat(config.preferredStablecoin, \" | Step 2: Bought \").concat(crypto1Bought.toFixed(6), \" \").concat(config.crypto1, \" | Level: \").concat(activeRow.orderLevel, \" → \").concat(newLevel));\n                                toast({\n                                    title: \"BUY Executed (Stablecoin)\",\n                                    description: \"Counter \".concat(activeRow.counter, \": \").concat(crypto1Bought.toFixed(6), \" \").concat(config.crypto1, \" via \").concat(config.preferredStablecoin),\n                                    duration: 2000\n                                });\n                                playSound('soundOrderExecution');\n                                // Send Telegram notification for Stablecoin BUY\n                                sendTelegramNotification(\"\\uD83D\\uDFE2 <b>BUY EXECUTED (Stablecoin Swap)</b>\\n\" + \"\\uD83D\\uDCCA Counter: \".concat(activeRow.counter, \"\\n\") + \"\\uD83D\\uDD04 Step 1: Sold \".concat(amountCrypto2ToUse.toFixed(2), \" \").concat(config.crypto2, \" → \").concat(stablecoinObtained.toFixed(2), \" \").concat(config.preferredStablecoin, \"\\n\") + \"\\uD83D\\uDD04 Step 2: Bought \".concat(crypto1Bought.toFixed(6), \" \").concat(config.crypto1, \"\\n\") + \"\\uD83D\\uDCCA Level: \".concat(activeRow.orderLevel, \" → \").concat(newLevel, \"\\n\") + \"\\uD83D\\uDCC8 Mode: Stablecoin Swap\");\n                                actionsTaken++;\n                                // Update local balance for subsequent checks in this cycle\n                                currentCrypto2Balance -= amountCrypto2ToUse;\n                                currentCrypto1Balance += crypto1Bought;\n                            } else {\n                                // Insufficient balance - send Telegram notification\n                                console.log(\"❌ Insufficient \".concat(config.crypto2, \" balance for stablecoin swap: \").concat(currentCrypto2Balance.toFixed(6), \" < \").concat(amountCrypto2ToUse.toFixed(6)));\n                                sendLowBalanceNotification(config.crypto2, currentCrypto2Balance, amountCrypto2ToUse).catch(console.error);\n                            }\n                        }\n                        // STEP 3: Check and Process Inferior TargetRow (TargetRowN_minus_1) - ALWAYS, regardless of BUY\n                        const currentCounter = activeRow.counter;\n                        const inferiorRow = sortedRowsForLogic.find({\n                            \"TradingProvider.useEffect.inferiorRow\": (row)=>row.counter === currentCounter - 1\n                        }[\"TradingProvider.useEffect.inferiorRow\"]);\n                        console.log(\"\\uD83D\\uDD0D StablecoinSwap SELL Check:\", {\n                            currentCounter: currentCounter,\n                            inferiorRowFound: !!inferiorRow,\n                            inferiorRowStatus: inferiorRow === null || inferiorRow === void 0 ? void 0 : inferiorRow.status,\n                            inferiorRowCrypto1Held: inferiorRow === null || inferiorRow === void 0 ? void 0 : inferiorRow.crypto1AmountHeld,\n                            inferiorRowOriginalCost: inferiorRow === null || inferiorRow === void 0 ? void 0 : inferiorRow.originalCostCrypto2,\n                            canExecuteSell: !!(inferiorRow && inferiorRow.status === \"Full\" && inferiorRow.crypto1AmountHeld && inferiorRow.originalCostCrypto2)\n                        });\n                        if (inferiorRow && inferiorRow.status === \"Full\" && inferiorRow.crypto1AmountHeld && inferiorRow.originalCostCrypto2) {\n                            // Execute Two-Step \"Sell Crypto1 & Reacquire Crypto2 via Stablecoin\" for TargetRowN_minus_1\n                            const amountCrypto1ToSell = inferiorRow.crypto1AmountHeld;\n                            // Step A: Sell Crypto1 for PreferredStablecoin\n                            // Use current fluctuating market price for realistic P/L\n                            const crypto1USDPrice = getUSDPrice(config.crypto1 || 'BTC');\n                            const crypto2USDPrice = getUSDPrice(config.crypto2 || 'USDT');\n                            const stablecoinUSDPrice = getUSDPrice(config.preferredStablecoin || 'USDT');\n                            // Apply current market price fluctuation to Crypto1 price\n                            const fluctuatedCrypto1Price = crypto1USDPrice * (state.currentMarketPrice / (crypto1USDPrice / crypto2USDPrice));\n                            const crypto1StablecoinPrice = fluctuatedCrypto1Price / stablecoinUSDPrice;\n                            const stablecoinFromC1Sell = amountCrypto1ToSell * crypto1StablecoinPrice;\n                            // Step B: Buy Crypto2 with Stablecoin\n                            const crypto2StablecoinPrice = crypto2USDPrice / stablecoinUSDPrice;\n                            const crypto2Reacquired = stablecoinFromC1Sell / crypto2StablecoinPrice;\n                            // Calculate realized profit in Crypto2\n                            // We compare the value of crypto2 we got back vs what we originally spent\n                            const originalCost = inferiorRow.originalCostCrypto2 || 0;\n                            const realizedProfitInCrypto2 = crypto2Reacquired - originalCost;\n                            // Calculate Crypto1 profit/loss - this should be the profit in terms of Crypto1\n                            // For StablecoinSwap, we need to calculate how much Crypto1 profit this represents\n                            const realizedProfitCrypto1 = crypto1StablecoinPrice > 0 ? realizedProfitInCrypto2 / crypto1StablecoinPrice : 0;\n                            // IMPORTANT: If P/L is exactly 0, it might indicate a price calculation issue\n                            if (realizedProfitInCrypto2 === 0) {\n                                console.warn(\"⚠️ P/L is exactly 0 - this might indicate an issue:\\n                - Are prices changing between BUY and SELL?\\n                - Is the market price fluctuation working?\\n                - Current market price: \".concat(state.currentMarketPrice));\n                            }\n                            console.log(\"\\uD83D\\uDCB0 StablecoinSwap P/L Calculation DETAILED:\\n              - Inferior Row Counter: \".concat(inferiorRow.counter, \"\\n              - Original Cost (Crypto2): \").concat(originalCost, \"\\n              - Crypto2 Reacquired: \").concat(crypto2Reacquired, \"\\n              - Realized P/L (Crypto2): \").concat(realizedProfitInCrypto2, \"\\n              - Crypto1 Stablecoin Price: \").concat(crypto1StablecoinPrice, \"\\n              - Realized P/L (Crypto1): \").concat(realizedProfitCrypto1, \"\\n              - Is Profitable: \").concat(realizedProfitInCrypto2 > 0 ? 'YES' : 'NO', \"\\n              - Calculation: \").concat(crypto2Reacquired, \" - \").concat(originalCost, \" = \").concat(realizedProfitInCrypto2));\n                            // Update Row N-1: Full → Free, Level UNCHANGED, Vars cleared\n                            const updatedInferiorRow = {\n                                ...inferiorRow,\n                                status: \"Free\",\n                                // orderLevel: REMAINS UNCHANGED per specification\n                                crypto1AmountHeld: undefined,\n                                originalCostCrypto2: undefined,\n                                valueLevel: config.baseBid * Math.pow(config.multiplier, inferiorRow.orderLevel),\n                                crypto1Var: 0,\n                                crypto2Var: 0\n                            };\n                            dispatch({\n                                type: 'UPDATE_TARGET_PRICE_ROW',\n                                payload: updatedInferiorRow\n                            });\n                            dispatch({\n                                type: 'UPDATE_BALANCES',\n                                payload: {\n                                    crypto1: currentCrypto1Balance - amountCrypto1ToSell,\n                                    crypto2: currentCrypto2Balance + crypto2Reacquired\n                                }\n                            });\n                            // Add history entries for both steps of the N-1 stablecoin swap\n                            // Step A: SELL Crypto1 for Stablecoin (with profit/loss data for analytics)\n                            console.log(\"\\uD83D\\uDCCA Adding StablecoinSwap SELL order to history with P/L:\", {\n                                realizedProfitLossCrypto2: realizedProfitInCrypto2,\n                                realizedProfitLossCrypto1: realizedProfitCrypto1,\n                                pair: \"\".concat(config.crypto1, \"/\").concat(config.preferredStablecoin),\n                                orderType: \"SELL\",\n                                amountCrypto1: amountCrypto1ToSell,\n                                avgPrice: crypto1StablecoinPrice,\n                                valueCrypto2: stablecoinFromC1Sell,\n                                price1: crypto1StablecoinPrice,\n                                crypto1Symbol: config.crypto1,\n                                crypto2Symbol: config.preferredStablecoin\n                            });\n                            dispatch({\n                                type: 'ADD_ORDER_HISTORY_ENTRY',\n                                payload: {\n                                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                    timestamp: Date.now(),\n                                    pair: \"\".concat(config.crypto1, \"/\").concat(config.preferredStablecoin),\n                                    crypto1: config.crypto1,\n                                    orderType: \"SELL\",\n                                    amountCrypto1: amountCrypto1ToSell,\n                                    avgPrice: crypto1StablecoinPrice,\n                                    valueCrypto2: stablecoinFromC1Sell,\n                                    price1: crypto1StablecoinPrice,\n                                    crypto1Symbol: config.crypto1 || '',\n                                    crypto2Symbol: config.preferredStablecoin || ''\n                                }\n                            });\n                            // Step B: BUY Crypto2 with Stablecoin (no profit/loss as it's just conversion)\n                            dispatch({\n                                type: 'ADD_ORDER_HISTORY_ENTRY',\n                                payload: {\n                                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                    timestamp: Date.now(),\n                                    pair: \"\".concat(config.crypto2, \"/\").concat(config.preferredStablecoin),\n                                    crypto1: config.crypto2,\n                                    orderType: \"BUY\",\n                                    amountCrypto1: crypto2Reacquired,\n                                    avgPrice: crypto2StablecoinPrice,\n                                    valueCrypto2: stablecoinFromC1Sell,\n                                    price1: crypto2StablecoinPrice,\n                                    crypto1Symbol: config.crypto2 || '',\n                                    crypto2Symbol: config.preferredStablecoin || ''\n                                }\n                            });\n                            console.log(\"✅ STABLECOIN SELL: Counter \".concat(currentCounter - 1, \" | Step A: Sold \").concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \" → \").concat(stablecoinFromC1Sell.toFixed(2), \" \").concat(config.preferredStablecoin, \" | Step B: Bought \").concat(crypto2Reacquired.toFixed(2), \" \").concat(config.crypto2, \" | Profit: \").concat(realizedProfitInCrypto2.toFixed(2), \" \").concat(config.crypto2, \" | Level: \").concat(inferiorRow.orderLevel, \" (unchanged)\"));\n                            toast({\n                                title: \"SELL Executed (Stablecoin)\",\n                                description: \"Counter \".concat(currentCounter - 1, \": Profit \").concat(realizedProfitInCrypto2.toFixed(2), \" \").concat(config.crypto2, \" via \").concat(config.preferredStablecoin),\n                                duration: 2000\n                            });\n                            playSound('soundOrderExecution');\n                            // Send Telegram notification for Stablecoin SELL\n                            const profitEmoji = realizedProfitInCrypto2 > 0 ? '📈' : realizedProfitInCrypto2 < 0 ? '📉' : '➖';\n                            sendTelegramNotification(\"\\uD83D\\uDD34 <b>SELL EXECUTED (Stablecoin Swap)</b>\\n\" + \"\\uD83D\\uDCCA Counter: \".concat(currentCounter - 1, \"\\n\") + \"\\uD83D\\uDD04 Step A: Sold \".concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \" → \").concat(stablecoinFromC1Sell.toFixed(2), \" \").concat(config.preferredStablecoin, \"\\n\") + \"\\uD83D\\uDD04 Step B: Bought \".concat(crypto2Reacquired.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\".concat(profitEmoji, \" Profit: \").concat(realizedProfitInCrypto2.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\\uD83D\\uDCCA Level: \".concat(inferiorRow.orderLevel, \" (unchanged)\\n\") + \"\\uD83D\\uDCC8 Mode: Stablecoin Swap\");\n                            actionsTaken++;\n                            // Update local balance for subsequent checks in this cycle\n                            currentCrypto1Balance -= amountCrypto1ToSell;\n                            currentCrypto2Balance += crypto2Reacquired;\n                        }\n                    }\n                }\n            }\n            if (actionsTaken > 0) {\n                console.log(\"\\uD83C\\uDFAF CYCLE COMPLETE: \".concat(actionsTaken, \" actions taken at price $\").concat(currentMarketPrice.toFixed(2)));\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus,\n        state.currentMarketPrice,\n        state.targetPriceRows,\n        state.config,\n        state.crypto1Balance,\n        state.crypto2Balance,\n        state.stablecoinBalance,\n        dispatch,\n        toast,\n        playSound,\n        sendTelegramNotification\n    ]);\n    const getDisplayOrders = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[getDisplayOrders]\": ()=>{\n            if (!state.targetPriceRows || !Array.isArray(state.targetPriceRows)) {\n                return [];\n            }\n            return state.targetPriceRows.map({\n                \"TradingProvider.useCallback[getDisplayOrders]\": (row)=>{\n                    const currentPrice = state.currentMarketPrice || 0;\n                    const targetPrice = row.targetPrice || 0;\n                    const percentFromActualPrice = currentPrice && targetPrice ? (currentPrice / targetPrice - 1) * 100 : 0;\n                    let incomeCrypto1;\n                    let incomeCrypto2;\n                    if (row.status === \"Full\" && row.crypto1AmountHeld && row.originalCostCrypto2) {\n                        const totalUnrealizedProfitInCrypto2 = currentPrice * row.crypto1AmountHeld - row.originalCostCrypto2;\n                        incomeCrypto2 = totalUnrealizedProfitInCrypto2 * state.config.incomeSplitCrypto2Percent / 100;\n                        if (currentPrice > 0) {\n                            incomeCrypto1 = totalUnrealizedProfitInCrypto2 * state.config.incomeSplitCrypto1Percent / 100 / currentPrice;\n                        }\n                    }\n                    return {\n                        ...row,\n                        currentPrice,\n                        priceDifference: targetPrice - currentPrice,\n                        priceDifferencePercent: currentPrice > 0 ? (targetPrice - currentPrice) / currentPrice * 100 : 0,\n                        potentialProfitCrypto1: state.config.incomeSplitCrypto1Percent / 100 * row.valueLevel / (targetPrice || 1),\n                        potentialProfitCrypto2: state.config.incomeSplitCrypto2Percent / 100 * row.valueLevel,\n                        percentFromActualPrice,\n                        incomeCrypto1,\n                        incomeCrypto2\n                    };\n                }\n            }[\"TradingProvider.useCallback[getDisplayOrders]\"]).sort({\n                \"TradingProvider.useCallback[getDisplayOrders]\": (a, b)=>b.targetPrice - a.targetPrice\n            }[\"TradingProvider.useCallback[getDisplayOrders]\"]);\n        }\n    }[\"TradingProvider.useCallback[getDisplayOrders]\"], [\n        state.targetPriceRows,\n        state.currentMarketPrice,\n        state.config.incomeSplitCrypto1Percent,\n        state.config.incomeSplitCrypto2Percent,\n        state.config.baseBid,\n        state.config.multiplier\n    ]);\n    // Backend Integration Functions\n    const saveConfigToBackend = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[saveConfigToBackend]\": async (config)=>{\n            try {\n                var _response_config;\n                const configData = {\n                    name: \"\".concat(config.crypto1, \"/\").concat(config.crypto2, \" \").concat(config.tradingMode),\n                    tradingMode: config.tradingMode,\n                    crypto1: config.crypto1,\n                    crypto2: config.crypto2,\n                    baseBid: config.baseBid,\n                    multiplier: config.multiplier,\n                    numDigits: config.numDigits,\n                    slippagePercent: config.slippagePercent,\n                    incomeSplitCrypto1Percent: config.incomeSplitCrypto1Percent,\n                    incomeSplitCrypto2Percent: config.incomeSplitCrypto2Percent,\n                    preferredStablecoin: config.preferredStablecoin,\n                    targetPrices: state.targetPriceRows.map({\n                        \"TradingProvider.useCallback[saveConfigToBackend]\": (row)=>row.targetPrice\n                    }[\"TradingProvider.useCallback[saveConfigToBackend]\"])\n                };\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.tradingApi.saveConfig(configData);\n                console.log('✅ Config saved to backend:', response);\n                return ((_response_config = response.config) === null || _response_config === void 0 ? void 0 : _response_config.id) || null;\n            } catch (error) {\n                console.error('❌ Failed to save config to backend:', error);\n                toast({\n                    title: \"Backend Error\",\n                    description: \"Failed to save configuration to backend\",\n                    variant: \"destructive\",\n                    duration: 3000\n                });\n                return null;\n            }\n        }\n    }[\"TradingProvider.useCallback[saveConfigToBackend]\"], [\n        state.targetPriceRows,\n        toast\n    ]);\n    const startBackendBot = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[startBackendBot]\": async (configId)=>{\n            try {\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.tradingApi.startBot(configId);\n                console.log('✅ Bot started on backend:', response);\n                toast({\n                    title: \"Bot Started\",\n                    description: \"Trading bot started successfully on backend\",\n                    duration: 3000\n                });\n                return true;\n            } catch (error) {\n                console.error('❌ Failed to start bot on backend:', error);\n                toast({\n                    title: \"Backend Error\",\n                    description: \"Failed to start bot on backend\",\n                    variant: \"destructive\",\n                    duration: 3000\n                });\n                return false;\n            }\n        }\n    }[\"TradingProvider.useCallback[startBackendBot]\"], [\n        toast\n    ]);\n    const stopBackendBot = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[stopBackendBot]\": async (configId)=>{\n            try {\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.tradingApi.stopBot(configId);\n                console.log('✅ Bot stopped on backend:', response);\n                toast({\n                    title: \"Bot Stopped\",\n                    description: \"Trading bot stopped successfully on backend\",\n                    duration: 3000\n                });\n                return true;\n            } catch (error) {\n                console.error('❌ Failed to stop bot on backend:', error);\n                toast({\n                    title: \"Backend Error\",\n                    description: \"Failed to stop bot on backend\",\n                    variant: \"destructive\",\n                    duration: 3000\n                });\n                return false;\n            }\n        }\n    }[\"TradingProvider.useCallback[stopBackendBot]\"], [\n        toast\n    ]);\n    const checkBackendStatus = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[checkBackendStatus]\": async ()=>{\n            const apiUrl = \"http://localhost:5000\";\n            if (!apiUrl) {\n                console.error('Error: NEXT_PUBLIC_API_URL is not defined. Backend connectivity check cannot be performed.');\n                dispatch({\n                    type: 'SET_BACKEND_STATUS',\n                    payload: 'offline'\n                });\n                return;\n            }\n            try {\n                const healthResponse = await fetch(\"\".concat(apiUrl, \"/health/\"));\n                if (!healthResponse.ok) {\n                    // Log more details if the response was received but not OK\n                    console.error(\"Backend health check failed with status: \".concat(healthResponse.status, \" \").concat(healthResponse.statusText));\n                    const responseText = await healthResponse.text().catch({\n                        \"TradingProvider.useCallback[checkBackendStatus]\": ()=>'Could not read response text.'\n                    }[\"TradingProvider.useCallback[checkBackendStatus]\"]);\n                    console.error('Backend health check response body:', responseText);\n                }\n                dispatch({\n                    type: 'SET_BACKEND_STATUS',\n                    payload: healthResponse.ok ? 'online' : 'offline'\n                });\n            } catch (error) {\n                dispatch({\n                    type: 'SET_BACKEND_STATUS',\n                    payload: 'offline'\n                });\n                console.error('Backend connectivity check failed. Error details:', error);\n                if (error.cause) {\n                    console.error('Fetch error cause:', error.cause);\n                }\n                // It's also useful to log the apiUrl to ensure it's what we expect\n                console.error('Attempted to fetch API URL:', \"\".concat(apiUrl, \"/health/\"));\n            }\n        }\n    }[\"TradingProvider.useCallback[checkBackendStatus]\"], [\n        dispatch\n    ]);\n    // Initialize backend status check (one-time only)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            // Initial check for backend status only\n            checkBackendStatus();\n        }\n    }[\"TradingProvider.useEffect\"], [\n        checkBackendStatus\n    ]);\n    // Save state to localStorage whenever it changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            saveStateToLocalStorage(state);\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state\n    ]);\n    // Effect to handle bot warm-up period (immediate execution)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            if (state.botSystemStatus === 'WarmingUp') {\n                console.log(\"Bot is Warming Up... Immediate execution enabled.\");\n                // Immediate transition to Running state - no delays\n                dispatch({\n                    type: 'SYSTEM_COMPLETE_WARMUP'\n                });\n                console.log(\"Bot is now Running immediately.\");\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus,\n        dispatch\n    ]);\n    // Effect to handle session runtime tracking\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            const currentSessionId = sessionManager.getCurrentSessionId();\n            if (currentSessionId) {\n                if (state.botSystemStatus === 'Running') {\n                    // Bot started running, start runtime tracking\n                    sessionManager.startSessionRuntime(currentSessionId);\n                    console.log('✅ Started runtime tracking for session:', currentSessionId);\n                } else if (state.botSystemStatus === 'Stopped') {\n                    // Bot stopped, stop runtime tracking\n                    sessionManager.stopSessionRuntime(currentSessionId);\n                    console.log('⏹️ Stopped runtime tracking for session:', currentSessionId);\n                }\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus\n    ]);\n    // Auto-create session when bot actually starts running (not during warmup)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            // Check if we need to create a session when bot actually starts running\n            if (state.botSystemStatus === 'Running' && !sessionManager.getCurrentSessionId()) {\n                // Only create if we have valid crypto configuration AND target prices set\n                // This prevents auto-creation for fresh windows\n                if (state.config.crypto1 && state.config.crypto2 && state.targetPriceRows.length > 0) {\n                    sessionManager.createNewSessionWithAutoName(state.config).then({\n                        \"TradingProvider.useEffect\": (sessionId)=>{\n                            sessionManager.setCurrentSession(sessionId);\n                            console.log('✅ Auto-created session when bot started:', sessionId);\n                        }\n                    }[\"TradingProvider.useEffect\"]).catch({\n                        \"TradingProvider.useEffect\": (error)=>{\n                            console.error('❌ Failed to auto-create session:', error);\n                        }\n                    }[\"TradingProvider.useEffect\"]);\n                }\n            }\n            // Handle runtime tracking based on bot status\n            const currentSessionId = sessionManager.getCurrentSessionId();\n            if (currentSessionId) {\n                if (state.botSystemStatus === 'Running') {\n                    // Start runtime tracking when bot becomes active\n                    sessionManager.startSessionRuntime(currentSessionId);\n                    console.log('⏱️ Started runtime tracking for session:', currentSessionId);\n                } else if (state.botSystemStatus === 'Stopped') {\n                    // Stop runtime tracking when bot stops\n                    sessionManager.stopSessionRuntime(currentSessionId);\n                    console.log('⏹️ Stopped runtime tracking for session:', currentSessionId);\n                }\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus,\n        state.config.crypto1,\n        state.config.crypto2\n    ]);\n    // Handle crypto pair changes during active trading\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            const currentSessionId = sessionManager.getCurrentSessionId();\n            if (currentSessionId) {\n                const currentSession = sessionManager.loadSession(currentSessionId);\n                // Check if crypto pair has changed from the current session\n                if (currentSession && (currentSession.config.crypto1 !== state.config.crypto1 || currentSession.config.crypto2 !== state.config.crypto2)) {\n                    console.log('🔄 Crypto pair changed during session, auto-saving and resetting...');\n                    // Auto-save current session if bot was running or has data\n                    if (state.botSystemStatus === 'Running' || state.targetPriceRows.length > 0 || state.orderHistory.length > 0) {\n                        // Stop the bot if it's running\n                        if (state.botSystemStatus === 'Running') {\n                            stopBackendBot(currentSessionId);\n                        }\n                        // Save current session with timestamp\n                        const timestamp = new Date().toLocaleString('en-US', {\n                            month: 'short',\n                            day: 'numeric',\n                            hour: '2-digit',\n                            minute: '2-digit',\n                            hour12: false\n                        });\n                        const savedName = \"\".concat(currentSession.name, \" (AutoSaved \").concat(timestamp, \")\");\n                        sessionManager.createNewSession(savedName, currentSession.config).then({\n                            \"TradingProvider.useEffect\": async (savedSessionId)=>{\n                                await sessionManager.saveSession(savedSessionId, currentSession.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, false);\n                                console.log('💾 AutoSaved session:', savedName);\n                            }\n                        }[\"TradingProvider.useEffect\"]);\n                    }\n                    // Reset for new crypto pair\n                    dispatch({\n                        type: 'RESET_FOR_NEW_CRYPTO'\n                    });\n                    // Only create new session for the new crypto pair if bot was actually running\n                    if (state.config.crypto1 && state.config.crypto2 && state.botSystemStatus === 'Running') {\n                        const currentBalances = {\n                            crypto1: state.crypto1Balance,\n                            crypto2: state.crypto2Balance,\n                            stablecoin: state.stablecoinBalance\n                        };\n                        sessionManager.createNewSessionWithAutoName(state.config, undefined, currentBalances).then({\n                            \"TradingProvider.useEffect\": (newSessionId)=>{\n                                sessionManager.setCurrentSession(newSessionId);\n                                console.log('🆕 Created new session for crypto pair:', state.config.crypto1, '/', state.config.crypto2, 'with balances:', currentBalances);\n                                toast({\n                                    title: \"Crypto Pair Changed\",\n                                    description: \"Previous session AutoSaved. New session created for \".concat(state.config.crypto1, \"/\").concat(state.config.crypto2),\n                                    duration: 5000\n                                });\n                            }\n                        }[\"TradingProvider.useEffect\"]);\n                    } else {\n                        // If bot wasn't running, just clear the current session without creating a new one\n                        sessionManager.clearCurrentSession();\n                        console.log('🔄 Crypto pair changed but bot wasn\\'t running - cleared current session');\n                    }\n                }\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.config.crypto1,\n        state.config.crypto2\n    ]);\n    // Initialize network monitoring and auto-save\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const networkMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.NetworkMonitor.getInstance();\n            const autoSaveManager = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.AutoSaveManager.getInstance();\n            const memoryMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.MemoryMonitor.getInstance();\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            // Set up network status listener\n            const unsubscribeNetwork = networkMonitor.addListener({\n                \"TradingProvider.useEffect.unsubscribeNetwork\": (isOnline, isInitial)=>{\n                    console.log(\"\\uD83C\\uDF10 Network status changed: \".concat(isOnline ? 'Online' : 'Offline'));\n                    // Handle offline state - stop bot and save session\n                    if (!isOnline && !isInitial) {\n                        // Stop the bot if it's running\n                        if (state.botSystemStatus === 'Running') {\n                            console.log('🔴 Internet lost - stopping bot and saving session');\n                            dispatch({\n                                type: 'SYSTEM_STOP_BOT'\n                            });\n                            // Auto-save current session\n                            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n                            const currentSessionId = sessionManager.getCurrentSessionId();\n                            if (currentSessionId) {\n                                const currentSession = sessionManager.loadSession(currentSessionId);\n                                if (currentSession) {\n                                    // Create offline backup session\n                                    const timestamp = new Date().toLocaleString('en-US', {\n                                        month: 'short',\n                                        day: 'numeric',\n                                        hour: '2-digit',\n                                        minute: '2-digit',\n                                        hour12: false\n                                    });\n                                    const offlineName = \"\".concat(currentSession.name, \" (Offline Backup \").concat(timestamp, \")\");\n                                    sessionManager.createNewSession(offlineName, currentSession.config).then({\n                                        \"TradingProvider.useEffect.unsubscribeNetwork\": async (backupSessionId)=>{\n                                            await sessionManager.saveSession(backupSessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, false);\n                                            console.log('💾 Created offline backup session:', offlineName);\n                                        }\n                                    }[\"TradingProvider.useEffect.unsubscribeNetwork\"]);\n                                }\n                            }\n                        }\n                        toast({\n                            title: \"Network Disconnected\",\n                            description: \"Bot stopped and session saved. Trading paused until connection restored.\",\n                            variant: \"destructive\",\n                            duration: 8000\n                        });\n                    } else if (isOnline && !isInitial) {\n                        toast({\n                            title: \"Network Reconnected\",\n                            description: \"Connection restored. You can resume trading.\",\n                            duration: 3000\n                        });\n                    }\n                }\n            }[\"TradingProvider.useEffect.unsubscribeNetwork\"]);\n            // Set up memory monitoring\n            const unsubscribeMemory = memoryMonitor.addListener({\n                \"TradingProvider.useEffect.unsubscribeMemory\": (memory)=>{\n                    const usedMB = memory.usedJSHeapSize / 1024 / 1024;\n                    if (usedMB > 150) {\n                        console.warn(\"\\uD83E\\uDDE0 High memory usage: \".concat(usedMB.toFixed(2), \"MB\"));\n                        toast({\n                            title: \"High Memory Usage\",\n                            description: \"Memory usage is high (\".concat(usedMB.toFixed(0), \"MB). Consider refreshing the page.\"),\n                            variant: \"destructive\",\n                            duration: 5000\n                        });\n                    }\n                }\n            }[\"TradingProvider.useEffect.unsubscribeMemory\"]);\n            // Set up auto-save\n            const saveFunction = {\n                \"TradingProvider.useEffect.saveFunction\": async ()=>{\n                    try {\n                        // Save to session manager if we have a current session\n                        const currentSessionId = sessionManager.getCurrentSessionId();\n                        if (currentSessionId) {\n                            await sessionManager.saveSession(currentSessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, state.botSystemStatus === 'Running');\n                        }\n                        // Also save to localStorage as backup\n                        saveStateToLocalStorage(state);\n                    } catch (error) {\n                        console.error('Auto-save failed:', error);\n                    }\n                }\n            }[\"TradingProvider.useEffect.saveFunction\"];\n            autoSaveManager.enable(saveFunction, 30000); // Auto-save every 30 seconds\n            // Add beforeunload listener to save session on browser close/refresh\n            const handleBeforeUnload = {\n                \"TradingProvider.useEffect.handleBeforeUnload\": (event)=>{\n                    // Save immediately before unload\n                    saveFunction();\n                    // If bot is running, show warning\n                    if (state.botSystemStatus === 'Running') {\n                        const message = 'Trading bot is currently running. Are you sure you want to leave?';\n                        event.returnValue = message;\n                        return message;\n                    }\n                }\n            }[\"TradingProvider.useEffect.handleBeforeUnload\"];\n            window.addEventListener('beforeunload', handleBeforeUnload);\n            // Cleanup function\n            return ({\n                \"TradingProvider.useEffect\": ()=>{\n                    unsubscribeNetwork();\n                    unsubscribeMemory();\n                    autoSaveManager.disable();\n                    window.removeEventListener('beforeunload', handleBeforeUnload);\n                }\n            })[\"TradingProvider.useEffect\"];\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state,\n        toast\n    ]);\n    // Force save when bot status changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const autoSaveManager = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.AutoSaveManager.getInstance();\n            autoSaveManager.saveNow();\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus\n    ]);\n    // Manual save session function\n    const saveCurrentSession = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[saveCurrentSession]\": async ()=>{\n            try {\n                const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n                const currentSessionId = sessionManager.getCurrentSessionId();\n                if (!currentSessionId) {\n                    // No current session, only create one if bot is running or has meaningful data\n                    if (state.config.crypto1 && state.config.crypto2 && (state.botSystemStatus === 'Running' || state.targetPriceRows.length > 0 || state.orderHistory.length > 0)) {\n                        const currentBalances = {\n                            crypto1: state.crypto1Balance,\n                            crypto2: state.crypto2Balance,\n                            stablecoin: state.stablecoinBalance\n                        };\n                        const sessionId = await sessionManager.createNewSessionWithAutoName(state.config, undefined, currentBalances);\n                        sessionManager.setCurrentSession(sessionId);\n                        await sessionManager.saveSession(sessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, state.botSystemStatus === 'Running');\n                        return true;\n                    }\n                    console.log('⚠️ No session to save - bot not running and no meaningful data');\n                    return false;\n                }\n                // Save existing session\n                return await sessionManager.saveSession(currentSessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, state.botSystemStatus === 'Running');\n            } catch (error) {\n                console.error('Failed to save current session:', error);\n                return false;\n            }\n        }\n    }[\"TradingProvider.useCallback[saveCurrentSession]\"], [\n        state\n    ]);\n    // Context value\n    const contextValue = {\n        ...state,\n        dispatch,\n        setTargetPrices,\n        getDisplayOrders,\n        checkBackendStatus,\n        fetchMarketPrice,\n        startBackendBot,\n        stopBackendBot,\n        saveConfigToBackend,\n        saveCurrentSession,\n        backendStatus: state.backendStatus,\n        botSystemStatus: state.botSystemStatus,\n        isBotActive: state.botSystemStatus === 'Running'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TradingContext.Provider, {\n        value: contextValue,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\contexts\\\\TradingContext.tsx\",\n        lineNumber: 1862,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TradingProvider, \"Dul/342NNfDBwKH7syILMVemn7Y=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__.useToast\n    ];\n});\n_c = TradingProvider;\n// Custom hook to use the trading context\nconst useTradingContext = ()=>{\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(TradingContext);\n    if (context === undefined) {\n        throw new Error('useTradingContext must be used within a TradingProvider');\n    }\n    return context;\n};\n_s1(useTradingContext, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"TradingProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/TradingContext.tsx\n"));

/***/ })

});