"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/contexts/TradingContext.tsx":
/*!*****************************************!*\
  !*** ./src/contexts/TradingContext.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TradingProvider: () => (/* binding */ TradingProvider),\n/* harmony export */   useTradingContext: () => (/* binding */ useTradingContext)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/types */ \"(app-pages-browser)/./src/lib/types.tsx\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/session-manager */ \"(app-pages-browser)/./src/lib/session-manager.ts\");\n/* harmony import */ var _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/network-monitor */ \"(app-pages-browser)/./src/lib/network-monitor.ts\");\n/* __next_internal_client_entry_do_not_use__ TradingProvider,useTradingContext auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n// Define locally to avoid import issues\nconst DEFAULT_QUOTE_CURRENCIES = [\n    \"USDT\",\n    \"USDC\",\n    \"BTC\"\n];\n\n\n\n\n// Static fallback prices (used when API is unavailable)\nconst getStaticUSDPrice = (crypto)=>{\n    const usdPrices = {\n        // Major cryptocurrencies - Updated to current market prices\n        'BTC': 106000,\n        'ETH': 2500,\n        'SOL': 180,\n        'ADA': 0.85,\n        'DOGE': 0.32,\n        'LINK': 22,\n        'MATIC': 0.42,\n        'DOT': 6.5,\n        'AVAX': 38,\n        'SHIB': 0.000022,\n        'XRP': 2.1,\n        'LTC': 95,\n        'BCH': 420,\n        // Stablecoins\n        'USDT': 1.0,\n        'USDC': 1.0,\n        'FDUSD': 1.0,\n        'BUSD': 1.0,\n        'DAI': 1.0\n    };\n    return usdPrices[crypto.toUpperCase()] || 100;\n};\n// Static fallback function for market price calculation (no fluctuations)\nconst calculateStaticMarketPrice = (config)=>{\n    const crypto1USDPrice = getStaticUSDPrice(config.crypto1);\n    const crypto2USDPrice = getStaticUSDPrice(config.crypto2);\n    // Calculate the ratio: how many units of crypto2 = 1 unit of crypto1\n    const basePrice = crypto1USDPrice / crypto2USDPrice;\n    console.log(\"\\uD83D\\uDCCA Static price calculation: \".concat(config.crypto1, \" ($\").concat(crypto1USDPrice, \") / \").concat(config.crypto2, \" ($\").concat(crypto2USDPrice, \") = \").concat(basePrice.toFixed(6)));\n    return basePrice;\n};\n// Add this function to calculate the initial market price\nconst calculateInitialMarketPrice = (config)=>{\n    // Return 0 if either crypto is not selected\n    if (!config.crypto1 || !config.crypto2) {\n        return 0;\n    }\n    // Default fallback value for valid pairs\n    return calculateStaticMarketPrice(config);\n};\n// Get real market price with multiple fallback strategies\nconst getRealBinancePrice = async (config)=>{\n    try {\n        // Return 0 if either crypto is not selected\n        if (!config.crypto1 || !config.crypto2) {\n            return 0;\n        }\n        const pair = \"\".concat(config.crypto1).concat(config.crypto2);\n        // Strategy 1: Try public backend endpoint (no auth required)\n        try {\n            const response = await fetch(\"http://localhost:5000/trading/public/price/\".concat(pair), {\n                method: 'GET',\n                headers: {\n                    'Accept': 'application/json',\n                    'Content-Type': 'application/json'\n                },\n                signal: AbortSignal.timeout(5000) // 5 second timeout\n            });\n            if (response.ok) {\n                const data = await response.json();\n                if (data.price > 0) {\n                    console.log(\"✅ Backend Binance price: \".concat(config.crypto1, \"/\").concat(config.crypto2, \" = \").concat(data.price));\n                    return data.price;\n                }\n            }\n        } catch (error) {\n            console.warn(\"⚠️ Backend API failed for \".concat(pair, \":\"), error);\n        }\n        // Strategy 2: Try CoinGecko API as reliable fallback\n        try {\n            const crypto1Id = getCoinGeckoId(config.crypto1);\n            const crypto2Id = getCoinGeckoId(config.crypto2);\n            if (crypto1Id && crypto2Id) {\n                const response = await fetch(\"https://api.coingecko.com/api/v3/simple/price?ids=\".concat(crypto1Id, \"&vs_currencies=\").concat(crypto2Id), {\n                    method: 'GET',\n                    headers: {\n                        'Accept': 'application/json'\n                    },\n                    signal: AbortSignal.timeout(5000) // 5 second timeout\n                });\n                if (response.ok) {\n                    var _data_crypto1Id;\n                    const data = await response.json();\n                    const price = (_data_crypto1Id = data[crypto1Id]) === null || _data_crypto1Id === void 0 ? void 0 : _data_crypto1Id[crypto2Id];\n                    if (price > 0) {\n                        console.log(\"✅ CoinGecko price: \".concat(config.crypto1, \"/\").concat(config.crypto2, \" = \").concat(price));\n                        return price;\n                    }\n                }\n            }\n        } catch (error) {\n            console.warn(\"⚠️ CoinGecko API failed:\", error);\n        }\n        // Strategy 3: Try direct Binance public API (CORS might block this)\n        try {\n            const symbol = \"\".concat(config.crypto1).concat(config.crypto2).toUpperCase();\n            const response = await fetch(\"https://api.binance.com/api/v3/ticker/price?symbol=\".concat(symbol), {\n                method: 'GET',\n                headers: {\n                    'Accept': 'application/json'\n                },\n                signal: AbortSignal.timeout(5000) // 5 second timeout\n            });\n            if (response.ok) {\n                const data = await response.json();\n                const price = parseFloat(data.price);\n                if (price > 0) {\n                    console.log(\"✅ Direct Binance price: \".concat(config.crypto1, \"/\").concat(config.crypto2, \" = \").concat(price));\n                    return price;\n                }\n            }\n        } catch (error) {\n            console.warn(\"⚠️ Direct Binance API failed (CORS expected):\", error);\n        }\n        // If all APIs fail, throw error to use static fallback\n        throw new Error('All price APIs failed');\n    } catch (error) {\n        console.error('❌ Error fetching real price:', error);\n        throw error;\n    }\n};\n// Helper function to map crypto symbols to CoinGecko IDs\nconst getCoinGeckoId = (symbol)=>{\n    const mapping = {\n        'BTC': 'bitcoin',\n        'ETH': 'ethereum',\n        'SOL': 'solana',\n        'ADA': 'cardano',\n        'DOT': 'polkadot',\n        'MATIC': 'matic-network',\n        'AVAX': 'avalanche-2',\n        'LINK': 'chainlink',\n        'UNI': 'uniswap',\n        'USDT': 'tether',\n        'USDC': 'usd-coin',\n        'BUSD': 'binance-usd',\n        'DAI': 'dai'\n    };\n    return mapping[symbol.toUpperCase()] || null;\n};\n// Helper function to get stablecoin exchange rates for real market data\nconst getStablecoinExchangeRate = async (crypto, stablecoin)=>{\n    try {\n        // For stablecoin-to-stablecoin, assume 1:1 rate\n        if (getCoinGeckoId(crypto) && getCoinGeckoId(stablecoin)) {\n            const cryptoId = getCoinGeckoId(crypto);\n            const stablecoinId = getCoinGeckoId(stablecoin);\n            if (cryptoId === stablecoinId) return 1.0; // Same currency\n            // Get real exchange rate from CoinGecko\n            const response = await fetch(\"https://api.coingecko.com/api/v3/simple/price?ids=\".concat(cryptoId, \"&vs_currencies=\").concat(stablecoinId));\n            if (response.ok) {\n                var _data_cryptoId;\n                const data = await response.json();\n                const rate = cryptoId && stablecoinId ? (_data_cryptoId = data[cryptoId]) === null || _data_cryptoId === void 0 ? void 0 : _data_cryptoId[stablecoinId] : null;\n                if (rate > 0) {\n                    console.log(\"\\uD83D\\uDCCA Stablecoin rate: \".concat(crypto, \"/\").concat(stablecoin, \" = \").concat(rate));\n                    return rate;\n                }\n            }\n        }\n        // Fallback: calculate via USD prices\n        const cryptoUSDPrice = getUSDPrice(crypto);\n        const stablecoinUSDPrice = getUSDPrice(stablecoin);\n        const rate = cryptoUSDPrice / stablecoinUSDPrice;\n        console.log(\"\\uD83D\\uDCCA Fallback stablecoin rate: \".concat(crypto, \"/\").concat(stablecoin, \" = \").concat(rate, \" (via USD)\"));\n        return rate;\n    } catch (error) {\n        console.error('Error fetching stablecoin exchange rate:', error);\n        // Final fallback\n        const cryptoUSDPrice = getUSDPrice(crypto);\n        const stablecoinUSDPrice = getUSDPrice(stablecoin);\n        return cryptoUSDPrice / stablecoinUSDPrice;\n    }\n};\n// Real-time price cache\nlet priceCache = {};\nlet lastPriceUpdate = 0;\nconst PRICE_CACHE_DURATION = 2000; // 2 seconds cache for real-time updates\n// Get real USD price with multiple fallback strategies\nconst getRealBinanceUSDPrice = async (crypto)=>{\n    const now = Date.now();\n    const cacheKey = crypto.toUpperCase();\n    // Return cached price if still valid (2 seconds)\n    if (priceCache[cacheKey] && now - lastPriceUpdate < PRICE_CACHE_DURATION) {\n        return priceCache[cacheKey];\n    }\n    try {\n        // Strategy 1: Try backend API to get real Binance USD price (via USDT)\n        try {\n            const pair = \"\".concat(crypto.toUpperCase(), \"-USDT\");\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.tradingApi.getMarketPrice(pair);\n            if (response && response.price > 0) {\n                priceCache[cacheKey] = response.price;\n                lastPriceUpdate = now;\n                console.log(\"\\uD83D\\uDCCA Backend USD price for \".concat(crypto, \": $\").concat(response.price));\n                return response.price;\n            }\n        } catch (error) {\n            console.warn(\"⚠️ Backend USD price failed for \".concat(crypto, \", trying CoinGecko:\"), error);\n        }\n        // Strategy 2: Try CoinGecko API as reliable fallback\n        try {\n            const coinGeckoId = getCoinGeckoId(crypto);\n            if (coinGeckoId) {\n                const response = await fetch(\"https://api.coingecko.com/api/v3/simple/price?ids=\".concat(coinGeckoId, \"&vs_currencies=usd\"), {\n                    method: 'GET',\n                    headers: {\n                        'Accept': 'application/json'\n                    }\n                });\n                if (response.ok) {\n                    var _data_coinGeckoId;\n                    const data = await response.json();\n                    const price = (_data_coinGeckoId = data[coinGeckoId]) === null || _data_coinGeckoId === void 0 ? void 0 : _data_coinGeckoId.usd;\n                    if (price && price > 0) {\n                        priceCache[cacheKey] = price;\n                        lastPriceUpdate = now;\n                        console.log(\"\\uD83D\\uDCCA CoinGecko USD price for \".concat(crypto, \": $\").concat(price));\n                        return price;\n                    }\n                }\n            }\n        } catch (error) {\n            console.warn(\"⚠️ CoinGecko USD price failed for \".concat(crypto, \":\"), error);\n        }\n        // If all APIs fail, throw error to use static fallback\n        throw new Error(\"All USD price APIs failed for \".concat(crypto));\n    } catch (error) {\n        console.error(\"❌ Failed to get USD price for \".concat(crypto, \":\"), error);\n        throw error;\n    }\n};\n// Synchronous helper function to get USD price (uses cached values or static fallback)\nconst getUSDPrice = (crypto)=>{\n    const cacheKey = crypto.toUpperCase();\n    // Return cached price if available\n    if (priceCache[cacheKey]) {\n        return priceCache[cacheKey];\n    }\n    // Fallback to static prices\n    return getStaticUSDPrice(crypto);\n};\nconst initialBaseConfig = {\n    tradingMode: \"SimpleSpot\",\n    crypto1: \"\",\n    crypto2: \"\",\n    baseBid: 100,\n    multiplier: 1.005,\n    numDigits: 4,\n    slippagePercent: 0.2,\n    incomeSplitCrypto1Percent: 50,\n    incomeSplitCrypto2Percent: 50,\n    preferredStablecoin: _lib_types__WEBPACK_IMPORTED_MODULE_2__.AVAILABLE_STABLECOINS[0]\n};\nconst initialTradingState = {\n    config: initialBaseConfig,\n    targetPriceRows: [],\n    orderHistory: [],\n    appSettings: _lib_types__WEBPACK_IMPORTED_MODULE_2__.DEFAULT_APP_SETTINGS,\n    currentMarketPrice: calculateInitialMarketPrice(initialBaseConfig),\n    // isBotActive: false, // Remove\n    botSystemStatus: 'Stopped',\n    crypto1Balance: 10,\n    crypto2Balance: 100000,\n    stablecoinBalance: 0,\n    backendStatus: 'unknown'\n};\nconst lastActionTimestampPerCounter = new Map();\n// LocalStorage persistence functions\nconst STORAGE_KEY = 'pluto_trading_state';\nconst saveStateToLocalStorage = (state)=>{\n    try {\n        if (true) {\n            const stateToSave = {\n                config: state.config,\n                targetPriceRows: state.targetPriceRows,\n                orderHistory: state.orderHistory,\n                appSettings: state.appSettings,\n                currentMarketPrice: state.currentMarketPrice,\n                crypto1Balance: state.crypto1Balance,\n                crypto2Balance: state.crypto2Balance,\n                stablecoinBalance: state.stablecoinBalance,\n                botSystemStatus: state.botSystemStatus,\n                timestamp: Date.now()\n            };\n            localStorage.setItem(STORAGE_KEY, JSON.stringify(stateToSave));\n        }\n    } catch (error) {\n        console.error('Failed to save state to localStorage:', error);\n    }\n};\nconst loadStateFromLocalStorage = ()=>{\n    try {\n        if (true) {\n            const savedState = localStorage.getItem(STORAGE_KEY);\n            if (savedState) {\n                const parsed = JSON.parse(savedState);\n                // Check if state is not too old (24 hours)\n                if (parsed.timestamp && Date.now() - parsed.timestamp < 24 * 60 * 60 * 1000) {\n                    return parsed;\n                }\n            }\n        }\n    } catch (error) {\n        console.error('Failed to load state from localStorage:', error);\n    }\n    return null;\n};\nconst tradingReducer = (state, action)=>{\n    switch(action.type){\n        case 'SET_CONFIG':\n            const newConfig = {\n                ...state.config,\n                ...action.payload\n            };\n            // If trading pair changes, reset market price (it will be re-calculated by effect)\n            if (action.payload.crypto1 || action.payload.crypto2) {\n                return {\n                    ...state,\n                    config: newConfig,\n                    currentMarketPrice: calculateInitialMarketPrice(newConfig)\n                };\n            }\n            return {\n                ...state,\n                config: newConfig\n            };\n        case 'SET_TARGET_PRICE_ROWS':\n            return {\n                ...state,\n                targetPriceRows: action.payload.sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }))\n            };\n        case 'ADD_TARGET_PRICE_ROW':\n            {\n                const newRows = [\n                    ...state.targetPriceRows,\n                    action.payload\n                ].sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }));\n                return {\n                    ...state,\n                    targetPriceRows: newRows\n                };\n            }\n        case 'UPDATE_TARGET_PRICE_ROW':\n            {\n                const updatedRows = state.targetPriceRows.map((row)=>row.id === action.payload.id ? action.payload : row).sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }));\n                return {\n                    ...state,\n                    targetPriceRows: updatedRows\n                };\n            }\n        case 'REMOVE_TARGET_PRICE_ROW':\n            {\n                const filteredRows = state.targetPriceRows.filter((row)=>row.id !== action.payload).sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }));\n                return {\n                    ...state,\n                    targetPriceRows: filteredRows\n                };\n            }\n        case 'ADD_ORDER_HISTORY_ENTRY':\n            return {\n                ...state,\n                orderHistory: [\n                    action.payload,\n                    ...state.orderHistory\n                ]\n            };\n        case 'CLEAR_ORDER_HISTORY':\n            return {\n                ...state,\n                orderHistory: []\n            };\n        case 'SET_APP_SETTINGS':\n            return {\n                ...state,\n                appSettings: {\n                    ...state.appSettings,\n                    ...action.payload\n                }\n            };\n        case 'SET_MARKET_PRICE':\n            return {\n                ...state,\n                currentMarketPrice: action.payload\n            };\n        // case 'SET_BOT_STATUS': // Removed\n        case 'UPDATE_BALANCES':\n            return {\n                ...state,\n                crypto1Balance: action.payload.crypto1 !== undefined ? action.payload.crypto1 : state.crypto1Balance,\n                crypto2Balance: action.payload.crypto2 !== undefined ? action.payload.crypto2 : state.crypto2Balance,\n                stablecoinBalance: action.payload.stablecoin !== undefined ? action.payload.stablecoin : state.stablecoinBalance\n            };\n        case 'SET_BALANCES':\n            return {\n                ...state,\n                crypto1Balance: action.payload.crypto1,\n                crypto2Balance: action.payload.crypto2,\n                stablecoinBalance: action.payload.stablecoin !== undefined ? action.payload.stablecoin : state.stablecoinBalance\n            };\n        case 'UPDATE_STABLECOIN_BALANCE':\n            return {\n                ...state,\n                stablecoinBalance: action.payload\n            };\n        case 'RESET_SESSION':\n            const configForReset = {\n                ...state.config\n            };\n            return {\n                ...initialTradingState,\n                config: configForReset,\n                appSettings: {\n                    ...state.appSettings\n                },\n                currentMarketPrice: calculateInitialMarketPrice(configForReset),\n                // Preserve current balances instead of resetting to initial values\n                crypto1Balance: state.crypto1Balance,\n                crypto2Balance: state.crypto2Balance,\n                stablecoinBalance: state.stablecoinBalance\n            };\n        case 'SET_BACKEND_STATUS':\n            return {\n                ...state,\n                backendStatus: action.payload\n            };\n        case 'SYSTEM_START_BOT_INITIATE':\n            // Continue from previous state instead of resetting\n            return {\n                ...state,\n                botSystemStatus: 'WarmingUp'\n            };\n        case 'SYSTEM_COMPLETE_WARMUP':\n            return {\n                ...state,\n                botSystemStatus: 'Running'\n            };\n        case 'SYSTEM_STOP_BOT':\n            return {\n                ...state,\n                botSystemStatus: 'Stopped'\n            };\n        case 'SYSTEM_RESET_BOT':\n            // Fresh Start: Clear all target price rows and order history completely\n            lastActionTimestampPerCounter.clear();\n            // Clear current session to force new session name generation\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            sessionManager.clearCurrentSession();\n            return {\n                ...state,\n                botSystemStatus: 'Stopped',\n                targetPriceRows: [],\n                orderHistory: []\n            };\n        case 'SET_TARGET_PRICE_ROWS':\n            return {\n                ...state,\n                targetPriceRows: action.payload\n            };\n        case 'RESET_FOR_NEW_CRYPTO':\n            return {\n                ...initialTradingState,\n                config: state.config,\n                backendStatus: state.backendStatus,\n                botSystemStatus: 'Stopped',\n                currentMarketPrice: 0,\n                // Preserve current balances instead of resetting to initial values\n                crypto1Balance: state.crypto1Balance,\n                crypto2Balance: state.crypto2Balance,\n                stablecoinBalance: state.stablecoinBalance\n            };\n        default:\n            return state;\n    }\n};\nconst TradingContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// SIMPLIFIED LOGIC - NO COMPLEX COOLDOWNS\nconst TradingProvider = (param)=>{\n    let { children } = param;\n    _s();\n    // Initialize state with localStorage data if available\n    const initializeState = ()=>{\n        // Check if this is a new session from URL parameter\n        if (true) {\n            const urlParams = new URLSearchParams(window.location.search);\n            const isNewSession = urlParams.get('newSession') === 'true';\n            if (isNewSession) {\n                console.log('🆕 New session requested - starting with completely fresh state');\n                // Note: URL parameter will be cleared in useEffect to avoid render-time state updates\n                return initialTradingState;\n            }\n        }\n        // Check if this is a new window/tab by checking if we have a current session\n        const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n        const currentSessionId = sessionManager.getCurrentSessionId();\n        // If no current session exists for this window, start fresh\n        if (!currentSessionId) {\n            console.log('🆕 New window detected - starting with fresh state');\n            return initialTradingState;\n        }\n        // If we have a session, try to load the session data first (priority over localStorage)\n        const currentSession = sessionManager.loadSession(currentSessionId);\n        if (currentSession) {\n            console.log('🔄 Loading session data with balances:', {\n                crypto1: currentSession.crypto1Balance,\n                crypto2: currentSession.crypto2Balance,\n                stablecoin: currentSession.stablecoinBalance\n            });\n            return {\n                ...initialTradingState,\n                config: currentSession.config,\n                targetPriceRows: currentSession.targetPriceRows,\n                orderHistory: currentSession.orderHistory,\n                currentMarketPrice: currentSession.currentMarketPrice,\n                crypto1Balance: currentSession.crypto1Balance,\n                crypto2Balance: currentSession.crypto2Balance,\n                stablecoinBalance: currentSession.stablecoinBalance,\n                botSystemStatus: currentSession.isActive ? 'Running' : 'Stopped'\n            };\n        }\n        // Fallback to localStorage if session loading fails\n        const savedState = loadStateFromLocalStorage();\n        if (savedState) {\n            return {\n                ...initialTradingState,\n                ...savedState\n            };\n        }\n        return initialTradingState;\n    };\n    const [state, dispatch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useReducer)(tradingReducer, initializeState());\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__.useToast)();\n    // Clear URL parameter after component mounts to avoid render-time state updates\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            if (true) {\n                const urlParams = new URLSearchParams(window.location.search);\n                const isNewSession = urlParams.get('newSession') === 'true';\n                if (isNewSession) {\n                    // Clear the URL parameter to avoid confusion\n                    const newUrl = window.location.pathname;\n                    window.history.replaceState({}, '', newUrl);\n                }\n            }\n        }\n    }[\"TradingProvider.useEffect\"], []); // Run only once on mount\n    const audioRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Removed processing locks and cooldowns for continuous trading\n    // Initialize fetchMarketPrice to get real Binance prices with fallback\n    const fetchMarketPrice = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[fetchMarketPrice]\": async ()=>{\n            try {\n                // Don't fetch price if either crypto is not selected\n                if (!state.config.crypto1 || !state.config.crypto2) {\n                    dispatch({\n                        type: 'SET_MARKET_PRICE',\n                        payload: 0\n                    });\n                    return;\n                }\n                let price;\n                try {\n                    // Try to get real Binance prices with timeout protection\n                    const timeoutPromise = new Promise({\n                        \"TradingProvider.useCallback[fetchMarketPrice]\": (_, reject)=>{\n                            setTimeout({\n                                \"TradingProvider.useCallback[fetchMarketPrice]\": ()=>reject(new Error('Price fetch timeout'))\n                            }[\"TradingProvider.useCallback[fetchMarketPrice]\"], 5000); // 5 second timeout\n                        }\n                    }[\"TradingProvider.useCallback[fetchMarketPrice]\"]);\n                    if (state.config.tradingMode === \"StablecoinSwap\") {\n                        // For StablecoinSwap mode, try real Binance USD prices with timeout\n                        const pricePromise = Promise.all([\n                            getRealBinanceUSDPrice(state.config.crypto1),\n                            getRealBinanceUSDPrice(state.config.crypto2)\n                        ]).then({\n                            \"TradingProvider.useCallback[fetchMarketPrice].pricePromise\": (param)=>{\n                                let [crypto1USDPrice, crypto2USDPrice] = param;\n                                const calculatedPrice = crypto1USDPrice / crypto2USDPrice;\n                                console.log(\"\\uD83D\\uDCCA StablecoinSwap Real Binance Price:\\n              - \".concat(state.config.crypto1, \" USD Price: $\").concat(crypto1USDPrice.toLocaleString(), \"\\n              - \").concat(state.config.crypto2, \" USD Price: $\").concat(crypto2USDPrice.toLocaleString(), \"\\n              - Market Price (\").concat(state.config.crypto1, \"/\").concat(state.config.crypto2, \"): \").concat(calculatedPrice.toFixed(6)));\n                                return calculatedPrice;\n                            }\n                        }[\"TradingProvider.useCallback[fetchMarketPrice].pricePromise\"]);\n                        price = await Promise.race([\n                            pricePromise,\n                            timeoutPromise\n                        ]);\n                    } else {\n                        // For SimpleSpot mode, try real Binance API via backend with timeout\n                        const pricePromise = getRealBinancePrice(state.config);\n                        price = await Promise.race([\n                            pricePromise,\n                            timeoutPromise\n                        ]);\n                    }\n                    dispatch({\n                        type: 'SET_MARKET_PRICE',\n                        payload: price\n                    });\n                    console.log(\"✅ Successfully fetched real Binance price: \".concat(price));\n                } catch (error) {\n                    console.warn('⚠️ Real Binance API failed, using static price:', error);\n                    // Use static fallback if real API fails or times out\n                    const fallbackPrice = calculateStaticMarketPrice(state.config);\n                    dispatch({\n                        type: 'SET_MARKET_PRICE',\n                        payload: fallbackPrice\n                    });\n                }\n            } catch (error) {\n                console.error('❌ Critical error in fetchMarketPrice:', error);\n                const fallbackPrice = calculateStaticMarketPrice(state.config);\n                dispatch({\n                    type: 'SET_MARKET_PRICE',\n                    payload: fallbackPrice\n                });\n            }\n        }\n    }[\"TradingProvider.useCallback[fetchMarketPrice]\"], [\n        state.config,\n        dispatch\n    ]);\n    // Market price real-time updates from Binance\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            // Initial fetch\n            fetchMarketPrice();\n            // Set up real-time price updates every 2 seconds from Binance\n            const priceUpdateInterval = setInterval({\n                \"TradingProvider.useEffect.priceUpdateInterval\": async ()=>{\n                    const networkMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.NetworkMonitor.getInstance();\n                    if (networkMonitor.getStatus().isOnline) {\n                        try {\n                            await fetchMarketPrice();\n                            console.log('📊 Binance price updated');\n                        } catch (error) {\n                            console.warn('⚠️ Failed to update price from Binance:', error);\n                        }\n                    }\n                }\n            }[\"TradingProvider.useEffect.priceUpdateInterval\"], 2000); // Update every 2 seconds as requested\n            return ({\n                \"TradingProvider.useEffect\": ()=>{\n                    clearInterval(priceUpdateInterval);\n                }\n            })[\"TradingProvider.useEffect\"];\n        }\n    }[\"TradingProvider.useEffect\"], [\n        fetchMarketPrice,\n        state.config.crypto1,\n        state.config.crypto2,\n        state.config.tradingMode\n    ]);\n    // Audio initialization and user interaction detection\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            if (true) {\n                audioRef.current = new Audio();\n                // Add user interaction listener to enable audio\n                const enableAudio = {\n                    \"TradingProvider.useEffect.enableAudio\": ()=>{\n                        if (audioRef.current) {\n                            // Try to play a silent audio to enable audio context\n                            audioRef.current.volume = 0;\n                            audioRef.current.play().then({\n                                \"TradingProvider.useEffect.enableAudio\": ()=>{\n                                    if (audioRef.current) {\n                                        audioRef.current.volume = 1;\n                                    }\n                                    console.log('🔊 Audio context enabled after user interaction');\n                                }\n                            }[\"TradingProvider.useEffect.enableAudio\"]).catch({\n                                \"TradingProvider.useEffect.enableAudio\": ()=>{\n                                // Still blocked, but we tried\n                                }\n                            }[\"TradingProvider.useEffect.enableAudio\"]);\n                        }\n                        // Remove the listener after first interaction\n                        document.removeEventListener('click', enableAudio);\n                        document.removeEventListener('keydown', enableAudio);\n                    }\n                }[\"TradingProvider.useEffect.enableAudio\"];\n                // Listen for first user interaction\n                document.addEventListener('click', enableAudio);\n                document.addEventListener('keydown', enableAudio);\n                return ({\n                    \"TradingProvider.useEffect\": ()=>{\n                        document.removeEventListener('click', enableAudio);\n                        document.removeEventListener('keydown', enableAudio);\n                    }\n                })[\"TradingProvider.useEffect\"];\n            }\n        }\n    }[\"TradingProvider.useEffect\"], []);\n    const playSound = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[playSound]\": (soundKey)=>{\n            // Get current session's alarm settings, fallback to global app settings\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            const currentSessionId = sessionManager.getCurrentSessionId();\n            let alarmSettings = state.appSettings; // Default fallback\n            if (currentSessionId) {\n                const currentSession = sessionManager.loadSession(currentSessionId);\n                if (currentSession && currentSession.alarmSettings) {\n                    alarmSettings = currentSession.alarmSettings;\n                }\n            }\n            if (alarmSettings.soundAlertsEnabled && audioRef.current) {\n                let soundPath;\n                if (soundKey === 'soundOrderExecution' && alarmSettings.alertOnOrderExecution) {\n                    soundPath = alarmSettings.soundOrderExecution;\n                } else if (soundKey === 'soundError' && alarmSettings.alertOnError) {\n                    soundPath = alarmSettings.soundError;\n                }\n                if (soundPath) {\n                    try {\n                        // Stop any currently playing audio first\n                        audioRef.current.pause();\n                        audioRef.current.currentTime = 0;\n                        // Set the source and load the audio\n                        audioRef.current.src = soundPath;\n                        audioRef.current.load(); // Explicitly load the audio\n                        // Add error handler for audio loading\n                        audioRef.current.onerror = ({\n                            \"TradingProvider.useCallback[playSound]\": (e)=>{\n                                console.warn(\"⚠️ Audio loading failed for \".concat(soundPath, \":\"), e);\n                                // Try fallback sound\n                                if (soundPath !== '/sounds/chime2.wav' && audioRef.current) {\n                                    audioRef.current.src = '/sounds/chime2.wav';\n                                    audioRef.current.load();\n                                }\n                            }\n                        })[\"TradingProvider.useCallback[playSound]\"];\n                        // Play the sound and limit duration to 2 seconds\n                        const playPromise = audioRef.current.play();\n                        if (playPromise !== undefined) {\n                            playPromise.then({\n                                \"TradingProvider.useCallback[playSound]\": ()=>{\n                                    // Set a timeout to pause the audio after 2 seconds\n                                    setTimeout({\n                                        \"TradingProvider.useCallback[playSound]\": ()=>{\n                                            if (audioRef.current && !audioRef.current.paused) {\n                                                audioRef.current.pause();\n                                                audioRef.current.currentTime = 0; // Reset for next play\n                                            }\n                                        }\n                                    }[\"TradingProvider.useCallback[playSound]\"], 2000); // 2 seconds\n                                }\n                            }[\"TradingProvider.useCallback[playSound]\"]).catch({\n                                \"TradingProvider.useCallback[playSound]\": (err)=>{\n                                    // Handle different types of audio errors gracefully\n                                    const errorMessage = err.message || String(err);\n                                    if (errorMessage.includes('user didn\\'t interact') || errorMessage.includes('autoplay')) {\n                                        console.log('🔇 Audio autoplay blocked by browser - user interaction required');\n                                    // Could show a notification to user about enabling audio\n                                    } else if (errorMessage.includes('interrupted') || errorMessage.includes('supported source')) {\n                                    // These are expected errors, don't log them\n                                    } else {\n                                        console.warn(\"⚠️ Audio playback failed:\", errorMessage);\n                                    }\n                                }\n                            }[\"TradingProvider.useCallback[playSound]\"]);\n                        }\n                    } catch (error) {\n                        console.warn(\"⚠️ Audio setup failed for \".concat(soundPath, \":\"), error);\n                    }\n                }\n            }\n        }\n    }[\"TradingProvider.useCallback[playSound]\"], [\n        state.appSettings\n    ]);\n    // Enhanced Telegram notification function with error handling\n    const sendTelegramNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[sendTelegramNotification]\": async function(message) {\n            let isError = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n            try {\n                const telegramToken = localStorage.getItem('telegram_bot_token');\n                const telegramChatId = localStorage.getItem('telegram_chat_id');\n                if (!telegramToken || !telegramChatId) {\n                    console.log('Telegram not configured - skipping notification');\n                    return;\n                }\n                const response = await fetch(\"https://api.telegram.org/bot\".concat(telegramToken, \"/sendMessage\"), {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        chat_id: telegramChatId,\n                        text: message,\n                        parse_mode: 'HTML'\n                    })\n                });\n                if (!response.ok) {\n                    console.error('Failed to send Telegram notification:', response.statusText);\n                    // Don't retry error notifications to avoid infinite loops\n                    if (!isError) {\n                        await sendTelegramErrorNotification('Telegram API Error', \"Failed to send notification: \".concat(response.statusText));\n                    }\n                } else {\n                    console.log('✅ Telegram notification sent successfully');\n                }\n            } catch (error) {\n                console.error('Error sending Telegram notification:', error);\n                // Network disconnection detected\n                if (!isError) {\n                    await sendTelegramErrorNotification('Network Disconnection', \"Failed to connect to Telegram API: \".concat(error));\n                }\n            }\n        }\n    }[\"TradingProvider.useCallback[sendTelegramNotification]\"], []);\n    // Specific error notification functions\n    const sendTelegramErrorNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[sendTelegramErrorNotification]\": async (errorType, errorMessage)=>{\n            const timestamp = new Date().toLocaleString();\n            const message = \"⚠️ <b>ERROR ALERT</b>\\n\\n\" + \"\\uD83D\\uDD34 <b>Type:</b> \".concat(errorType, \"\\n\") + \"\\uD83D\\uDCDD <b>Message:</b> \".concat(errorMessage, \"\\n\") + \"⏰ <b>Time:</b> \".concat(timestamp, \"\\n\") + \"\\uD83E\\uDD16 <b>Bot:</b> \".concat(state.config.crypto1, \"/\").concat(state.config.crypto2, \" \").concat(state.config.tradingMode);\n            await sendTelegramNotification(message, true); // Mark as error to prevent recursion\n        }\n    }[\"TradingProvider.useCallback[sendTelegramErrorNotification]\"], [\n        state.config,\n        sendTelegramNotification\n    ]);\n    const sendLowBalanceNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[sendLowBalanceNotification]\": async (crypto, currentBalance, requiredAmount)=>{\n            const timestamp = new Date().toLocaleString();\n            const message = \"\\uD83D\\uDCB0 <b>LOW BALANCE ALERT</b>\\n\\n\" + \"\\uD83E\\uDE99 <b>Currency:</b> \".concat(crypto, \"\\n\") + \"\\uD83D\\uDCCA <b>Current Balance:</b> \".concat(currentBalance.toFixed(6), \" \").concat(crypto, \"\\n\") + \"⚡ <b>Required Amount:</b> \".concat(requiredAmount.toFixed(6), \" \").concat(crypto, \"\\n\") + \"\\uD83D\\uDCC9 <b>Shortage:</b> \".concat((requiredAmount - currentBalance).toFixed(6), \" \").concat(crypto, \"\\n\") + \"⏰ <b>Time:</b> \".concat(timestamp, \"\\n\") + \"\\uD83E\\uDD16 <b>Bot:</b> \".concat(state.config.crypto1, \"/\").concat(state.config.crypto2, \" \").concat(state.config.tradingMode);\n            await sendTelegramNotification(message);\n        }\n    }[\"TradingProvider.useCallback[sendLowBalanceNotification]\"], [\n        state.config,\n        sendTelegramNotification\n    ]);\n    const sendAPIErrorNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[sendAPIErrorNotification]\": async (apiName, errorDetails)=>{\n            const timestamp = new Date().toLocaleString();\n            const message = \"\\uD83D\\uDD0C <b>API ERROR ALERT</b>\\n\\n\" + \"\\uD83C\\uDF10 <b>API:</b> \".concat(apiName, \"\\n\") + \"❌ <b>Error:</b> \".concat(errorDetails, \"\\n\") + \"⏰ <b>Time:</b> \".concat(timestamp, \"\\n\") + \"\\uD83E\\uDD16 <b>Bot:</b> \".concat(state.config.crypto1, \"/\").concat(state.config.crypto2, \" \").concat(state.config.tradingMode);\n            await sendTelegramNotification(message);\n        }\n    }[\"TradingProvider.useCallback[sendAPIErrorNotification]\"], [\n        state.config,\n        sendTelegramNotification\n    ]);\n    // Effect to update market price when trading pair changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n        // When crypto1 or crypto2 (parts of state.config) change,\n        // the fetchMarketPrice useCallback gets a new reference.\n        // The useEffect above (which depends on fetchMarketPrice)\n        // will re-run, clear the old interval, make an initial fetch with the new config,\n        // and set up a new interval.\n        // The reducer for SET_CONFIG also sets an initial market price if crypto1/crypto2 changes.\n        // Thus, no explicit dispatch is needed here.\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.config.crypto1,\n        state.config.crypto2\n    ]); // Dependencies ensure this reacts to pair changes\n    const setTargetPrices = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[setTargetPrices]\": (prices)=>{\n            if (!prices || !Array.isArray(prices)) return;\n            // Sort prices from lowest to highest for proper counter assignment\n            const sortedPrices = [\n                ...prices\n            ].filter({\n                \"TradingProvider.useCallback[setTargetPrices].sortedPrices\": (price)=>!isNaN(price) && price > 0\n            }[\"TradingProvider.useCallback[setTargetPrices].sortedPrices\"]).sort({\n                \"TradingProvider.useCallback[setTargetPrices].sortedPrices\": (a, b)=>a - b\n            }[\"TradingProvider.useCallback[setTargetPrices].sortedPrices\"]);\n            const newRows = sortedPrices.map({\n                \"TradingProvider.useCallback[setTargetPrices].newRows\": (price, index)=>{\n                    const existingRow = state.targetPriceRows.find({\n                        \"TradingProvider.useCallback[setTargetPrices].newRows.existingRow\": (r)=>r.targetPrice === price\n                    }[\"TradingProvider.useCallback[setTargetPrices].newRows.existingRow\"]);\n                    if (existingRow) {\n                        // Update counter for existing row based on sorted position\n                        return {\n                            ...existingRow,\n                            counter: index + 1\n                        };\n                    }\n                    return {\n                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                        counter: index + 1,\n                        status: 'Free',\n                        orderLevel: 0,\n                        valueLevel: state.config.baseBid,\n                        targetPrice: price\n                    };\n                }\n            }[\"TradingProvider.useCallback[setTargetPrices].newRows\"]);\n            dispatch({\n                type: 'SET_TARGET_PRICE_ROWS',\n                payload: newRows\n            });\n        }\n    }[\"TradingProvider.useCallback[setTargetPrices]\"], [\n        state.targetPriceRows,\n        state.config.baseBid,\n        dispatch\n    ]);\n    // Core Trading Logic (Simulated) - CONTINUOUS TRADING VERSION\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            // Check network status first\n            const networkMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.NetworkMonitor.getInstance();\n            const isOnline = networkMonitor.getStatus().isOnline;\n            // Only check essential conditions AND network status\n            if (state.botSystemStatus !== 'Running' || state.targetPriceRows.length === 0 || state.currentMarketPrice <= 0 || !isOnline) {\n                if (!isOnline && state.botSystemStatus === 'Running') {\n                    console.log('🔴 Trading paused - network offline');\n                }\n                return;\n            }\n            // Execute trading logic immediately - no locks, no cooldowns, no delays\n            const { config, currentMarketPrice, targetPriceRows, crypto1Balance, crypto2Balance } = state;\n            const sortedRowsForLogic = [\n                ...targetPriceRows\n            ].sort({\n                \"TradingProvider.useEffect.sortedRowsForLogic\": (a, b)=>a.targetPrice - b.targetPrice\n            }[\"TradingProvider.useEffect.sortedRowsForLogic\"]);\n            // Use mutable variables for balance tracking within this cycle\n            let currentCrypto1Balance = crypto1Balance;\n            let currentCrypto2Balance = crypto2Balance;\n            let actionsTaken = 0;\n            console.log(\"\\uD83D\\uDE80 CONTINUOUS TRADING: Price $\".concat(currentMarketPrice.toFixed(2), \" | Targets: \").concat(sortedRowsForLogic.length, \" | Balance: $\").concat(currentCrypto2Balance, \" \").concat(config.crypto2));\n            // Show which targets are in range\n            const targetsInRange = sortedRowsForLogic.filter({\n                \"TradingProvider.useEffect.targetsInRange\": (row)=>{\n                    const diffPercent = Math.abs(currentMarketPrice - row.targetPrice) / currentMarketPrice * 100;\n                    return diffPercent <= config.slippagePercent;\n                }\n            }[\"TradingProvider.useEffect.targetsInRange\"]);\n            if (targetsInRange.length > 0) {\n                console.log(\"\\uD83C\\uDFAF TARGETS IN RANGE (\\xb1\".concat(config.slippagePercent, \"%):\"), targetsInRange.map({\n                    \"TradingProvider.useEffect\": (row)=>\"Counter \".concat(row.counter, \" (\").concat(row.status, \")\")\n                }[\"TradingProvider.useEffect\"]));\n            }\n            // CONTINUOUS TRADING LOGIC: Process all targets immediately\n            for(let i = 0; i < sortedRowsForLogic.length; i++){\n                const activeRow = sortedRowsForLogic[i];\n                const priceDiffPercent = Math.abs(currentMarketPrice - activeRow.targetPrice) / currentMarketPrice * 100;\n                // STEP 1: Check if TargetRowN is triggered (within slippage range)\n                if (priceDiffPercent <= config.slippagePercent) {\n                    if (config.tradingMode === \"SimpleSpot\") {\n                        // STEP 2: Evaluate Triggered TargetRowN's Status\n                        if (activeRow.status === \"Free\") {\n                            // STEP 2a: Execute BUY on TargetRowN\n                            const costCrypto2 = activeRow.valueLevel;\n                            if (currentCrypto2Balance >= costCrypto2) {\n                                const amountCrypto1Bought = costCrypto2 / currentMarketPrice;\n                                const updatedRow = {\n                                    ...activeRow,\n                                    status: \"Full\",\n                                    orderLevel: activeRow.orderLevel + 1,\n                                    valueLevel: config.baseBid * Math.pow(config.multiplier, activeRow.orderLevel + 1),\n                                    crypto1AmountHeld: amountCrypto1Bought,\n                                    originalCostCrypto2: costCrypto2,\n                                    crypto1Var: amountCrypto1Bought,\n                                    crypto2Var: -costCrypto2\n                                };\n                                dispatch({\n                                    type: 'UPDATE_TARGET_PRICE_ROW',\n                                    payload: updatedRow\n                                });\n                                dispatch({\n                                    type: 'UPDATE_BALANCES',\n                                    payload: {\n                                        crypto1: currentCrypto1Balance + amountCrypto1Bought,\n                                        crypto2: currentCrypto2Balance - costCrypto2\n                                    }\n                                });\n                                dispatch({\n                                    type: 'ADD_ORDER_HISTORY_ENTRY',\n                                    payload: {\n                                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                        timestamp: Date.now(),\n                                        pair: \"\".concat(config.crypto1, \"/\").concat(config.crypto2),\n                                        crypto1: config.crypto1,\n                                        orderType: \"BUY\",\n                                        amountCrypto1: amountCrypto1Bought,\n                                        avgPrice: currentMarketPrice,\n                                        valueCrypto2: costCrypto2,\n                                        price1: currentMarketPrice,\n                                        crypto1Symbol: config.crypto1 || '',\n                                        crypto2Symbol: config.crypto2 || ''\n                                    }\n                                });\n                                console.log(\"✅ BUY: Counter \".concat(activeRow.counter, \" bought \").concat(amountCrypto1Bought.toFixed(6), \" \").concat(config.crypto1, \" at $\").concat(currentMarketPrice.toFixed(2)));\n                                toast({\n                                    title: \"BUY Executed\",\n                                    description: \"Counter \".concat(activeRow.counter, \": \").concat(amountCrypto1Bought.toFixed(6), \" \").concat(config.crypto1),\n                                    duration: 2000\n                                });\n                                playSound('soundOrderExecution');\n                                // Send Telegram notification for BUY\n                                sendTelegramNotification(\"\\uD83D\\uDFE2 <b>BUY EXECUTED</b>\\n\" + \"\\uD83D\\uDCCA Counter: \".concat(activeRow.counter, \"\\n\") + \"\\uD83D\\uDCB0 Amount: \".concat(amountCrypto1Bought.toFixed(6), \" \").concat(config.crypto1, \"\\n\") + \"\\uD83D\\uDCB5 Price: $\".concat(currentMarketPrice.toFixed(2), \"\\n\") + \"\\uD83D\\uDCB8 Cost: $\".concat(costCrypto2.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\\uD83D\\uDCC8 Mode: Simple Spot\");\n                                actionsTaken++;\n                                // Update local balance for subsequent checks in this cycle\n                                currentCrypto2Balance -= costCrypto2;\n                                currentCrypto1Balance += amountCrypto1Bought;\n                            } else {\n                                // Insufficient balance - send Telegram notification\n                                console.log(\"❌ Insufficient \".concat(config.crypto2, \" balance: \").concat(currentCrypto2Balance.toFixed(6), \" < \").concat(costCrypto2.toFixed(6)));\n                                sendLowBalanceNotification(config.crypto2, currentCrypto2Balance, costCrypto2).catch(console.error);\n                            }\n                        }\n                        // STEP 3: Check and Process Inferior TargetRow (TargetRowN_minus_1) - ALWAYS, regardless of BUY\n                        const currentCounter = activeRow.counter;\n                        const inferiorRow = sortedRowsForLogic.find({\n                            \"TradingProvider.useEffect.inferiorRow\": (row)=>row.counter === currentCounter - 1\n                        }[\"TradingProvider.useEffect.inferiorRow\"]);\n                        if (inferiorRow && inferiorRow.status === \"Full\" && inferiorRow.crypto1AmountHeld && inferiorRow.originalCostCrypto2) {\n                            const amountCrypto1ToSell = inferiorRow.crypto1AmountHeld;\n                            const crypto2Received = amountCrypto1ToSell * currentMarketPrice;\n                            const realizedProfit = crypto2Received - inferiorRow.originalCostCrypto2;\n                            // Calculate Crypto1 profit/loss based on income split percentage\n                            const realizedProfitCrypto1 = currentMarketPrice > 0 ? realizedProfit * config.incomeSplitCrypto1Percent / 100 / currentMarketPrice : 0;\n                            const updatedInferiorRow = {\n                                ...inferiorRow,\n                                status: \"Free\",\n                                crypto1AmountHeld: undefined,\n                                originalCostCrypto2: undefined,\n                                valueLevel: config.baseBid * Math.pow(config.multiplier, inferiorRow.orderLevel),\n                                crypto1Var: -amountCrypto1ToSell,\n                                crypto2Var: crypto2Received\n                            };\n                            dispatch({\n                                type: 'UPDATE_TARGET_PRICE_ROW',\n                                payload: updatedInferiorRow\n                            });\n                            dispatch({\n                                type: 'UPDATE_BALANCES',\n                                payload: {\n                                    crypto1: currentCrypto1Balance - amountCrypto1ToSell,\n                                    crypto2: currentCrypto2Balance + crypto2Received\n                                }\n                            });\n                            dispatch({\n                                type: 'ADD_ORDER_HISTORY_ENTRY',\n                                payload: {\n                                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                    timestamp: Date.now(),\n                                    pair: \"\".concat(config.crypto1, \"/\").concat(config.crypto2),\n                                    crypto1: config.crypto1,\n                                    orderType: \"SELL\",\n                                    amountCrypto1: amountCrypto1ToSell,\n                                    avgPrice: currentMarketPrice,\n                                    valueCrypto2: crypto2Received,\n                                    price1: currentMarketPrice,\n                                    crypto1Symbol: config.crypto1 || '',\n                                    crypto2Symbol: config.crypto2 || '',\n                                    realizedProfitLossCrypto2: realizedProfit,\n                                    realizedProfitLossCrypto1: realizedProfitCrypto1\n                                }\n                            });\n                            console.log(\"✅ SELL: Counter \".concat(currentCounter - 1, \" sold \").concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \". Profit: $\").concat(realizedProfit.toFixed(2)));\n                            toast({\n                                title: \"SELL Executed\",\n                                description: \"Counter \".concat(currentCounter - 1, \": Profit $\").concat(realizedProfit.toFixed(2)),\n                                duration: 2000\n                            });\n                            playSound('soundOrderExecution');\n                            // Send Telegram notification for SELL\n                            const profitEmoji = realizedProfit > 0 ? '📈' : realizedProfit < 0 ? '📉' : '➖';\n                            sendTelegramNotification(\"\\uD83D\\uDD34 <b>SELL EXECUTED</b>\\n\" + \"\\uD83D\\uDCCA Counter: \".concat(currentCounter - 1, \"\\n\") + \"\\uD83D\\uDCB0 Amount: \".concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \"\\n\") + \"\\uD83D\\uDCB5 Price: $\".concat(currentMarketPrice.toFixed(2), \"\\n\") + \"\\uD83D\\uDCB8 Received: $\".concat(crypto2Received.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\".concat(profitEmoji, \" Profit: $\").concat(realizedProfit.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\\uD83D\\uDCC8 Mode: Simple Spot\");\n                            actionsTaken++;\n                            // Update local balance for subsequent checks in this cycle\n                            currentCrypto1Balance -= amountCrypto1ToSell;\n                            currentCrypto2Balance += crypto2Received;\n                        }\n                    } else if (config.tradingMode === \"StablecoinSwap\") {\n                        // STEP 2: Evaluate Triggered TargetRowN's Status (Stablecoin Swap Mode)\n                        if (activeRow.status === \"Free\") {\n                            // STEP 2a: Execute Two-Step \"Buy Crypto1 via Stablecoin\" on TargetRowN\n                            const amountCrypto2ToUse = activeRow.valueLevel; // Value = BaseBid * (Multiplier ^ Level)\n                            if (currentCrypto2Balance >= amountCrypto2ToUse) {\n                                // Step 1: Sell Crypto2 for PreferredStablecoin\n                                // Use current fluctuating market price for more realistic P/L\n                                const crypto2USDPrice = getUSDPrice(config.crypto2 || 'USDT');\n                                const stablecoinUSDPrice = getUSDPrice(config.preferredStablecoin || 'USDT');\n                                const crypto2StablecoinPrice = crypto2USDPrice / stablecoinUSDPrice;\n                                const stablecoinObtained = amountCrypto2ToUse * crypto2StablecoinPrice;\n                                // Step 2: Buy Crypto1 with Stablecoin\n                                // Use current fluctuating market price (this is the key for P/L calculation)\n                                const crypto1USDPrice = getUSDPrice(config.crypto1 || 'BTC');\n                                // Apply current market price fluctuation to Crypto1 price\n                                const fluctuatedCrypto1Price = crypto1USDPrice * (state.currentMarketPrice / (crypto1USDPrice / crypto2USDPrice));\n                                const crypto1StablecoinPrice = fluctuatedCrypto1Price / stablecoinUSDPrice;\n                                const crypto1Bought = stablecoinObtained / crypto1StablecoinPrice;\n                                // Update Row N: Free → Full, Level++, Value recalculated\n                                const newLevel = activeRow.orderLevel + 1;\n                                const newValue = config.baseBid * Math.pow(config.multiplier, newLevel);\n                                const updatedRow = {\n                                    ...activeRow,\n                                    status: \"Full\",\n                                    orderLevel: newLevel,\n                                    valueLevel: newValue,\n                                    crypto1AmountHeld: crypto1Bought,\n                                    originalCostCrypto2: amountCrypto2ToUse,\n                                    crypto1Var: crypto1Bought,\n                                    crypto2Var: -amountCrypto2ToUse\n                                };\n                                console.log(\"\\uD83D\\uDCB0 StablecoinSwap BUY - Setting originalCostCrypto2:\", {\n                                    counter: activeRow.counter,\n                                    amountCrypto2ToUse: amountCrypto2ToUse,\n                                    crypto1Bought: crypto1Bought,\n                                    originalCostCrypto2: amountCrypto2ToUse,\n                                    newLevel: newLevel,\n                                    crypto1StablecoinPrice: crypto1StablecoinPrice,\n                                    fluctuatedCrypto1Price: fluctuatedCrypto1Price,\n                                    currentMarketPrice: state.currentMarketPrice\n                                });\n                                dispatch({\n                                    type: 'UPDATE_TARGET_PRICE_ROW',\n                                    payload: updatedRow\n                                });\n                                dispatch({\n                                    type: 'UPDATE_BALANCES',\n                                    payload: {\n                                        crypto1: currentCrypto1Balance + crypto1Bought,\n                                        crypto2: currentCrypto2Balance - amountCrypto2ToUse\n                                    }\n                                });\n                                // Add history entries for both steps of the stablecoin swap\n                                // Calculate P/L for the crypto2 sell based on price fluctuation\n                                const baseCrypto2Price = getUSDPrice(config.crypto2) / getUSDPrice(config.preferredStablecoin);\n                                const estimatedProfitCrypto2 = amountCrypto2ToUse * (crypto2StablecoinPrice - baseCrypto2Price);\n                                const estimatedProfitCrypto1 = estimatedProfitCrypto2 / crypto1StablecoinPrice;\n                                console.log(\"\\uD83D\\uDCCA StablecoinSwap Step A SELL P/L:\", {\n                                    crypto2: config.crypto2,\n                                    amountSold: amountCrypto2ToUse,\n                                    currentPrice: crypto2StablecoinPrice,\n                                    basePrice: baseCrypto2Price,\n                                    estimatedProfitCrypto2: estimatedProfitCrypto2,\n                                    estimatedProfitCrypto1: estimatedProfitCrypto1\n                                });\n                                dispatch({\n                                    type: 'ADD_ORDER_HISTORY_ENTRY',\n                                    payload: {\n                                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                        timestamp: Date.now(),\n                                        pair: \"\".concat(config.crypto2, \"/\").concat(config.preferredStablecoin),\n                                        crypto1: config.crypto2,\n                                        orderType: \"SELL\",\n                                        amountCrypto1: amountCrypto2ToUse,\n                                        avgPrice: crypto2StablecoinPrice,\n                                        valueCrypto2: stablecoinObtained,\n                                        price1: crypto2StablecoinPrice,\n                                        crypto1Symbol: config.crypto2 || '',\n                                        crypto2Symbol: config.preferredStablecoin || ''\n                                    }\n                                });\n                                dispatch({\n                                    type: 'ADD_ORDER_HISTORY_ENTRY',\n                                    payload: {\n                                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                        timestamp: Date.now(),\n                                        pair: \"\".concat(config.crypto1, \"/\").concat(config.preferredStablecoin),\n                                        crypto1: config.crypto1,\n                                        orderType: \"BUY\",\n                                        amountCrypto1: crypto1Bought,\n                                        avgPrice: crypto1StablecoinPrice,\n                                        valueCrypto2: stablecoinObtained,\n                                        price1: crypto1StablecoinPrice,\n                                        crypto1Symbol: config.crypto1 || '',\n                                        crypto2Symbol: config.preferredStablecoin || ''\n                                    }\n                                });\n                                console.log(\"✅ STABLECOIN BUY: Counter \".concat(activeRow.counter, \" | Step 1: Sold \").concat(amountCrypto2ToUse, \" \").concat(config.crypto2, \" → \").concat(stablecoinObtained.toFixed(2), \" \").concat(config.preferredStablecoin, \" | Step 2: Bought \").concat(crypto1Bought.toFixed(6), \" \").concat(config.crypto1, \" | Level: \").concat(activeRow.orderLevel, \" → \").concat(newLevel));\n                                toast({\n                                    title: \"BUY Executed (Stablecoin)\",\n                                    description: \"Counter \".concat(activeRow.counter, \": \").concat(crypto1Bought.toFixed(6), \" \").concat(config.crypto1, \" via \").concat(config.preferredStablecoin),\n                                    duration: 2000\n                                });\n                                playSound('soundOrderExecution');\n                                // Send Telegram notification for Stablecoin BUY\n                                sendTelegramNotification(\"\\uD83D\\uDFE2 <b>BUY EXECUTED (Stablecoin Swap)</b>\\n\" + \"\\uD83D\\uDCCA Counter: \".concat(activeRow.counter, \"\\n\") + \"\\uD83D\\uDD04 Step 1: Sold \".concat(amountCrypto2ToUse.toFixed(2), \" \").concat(config.crypto2, \" → \").concat(stablecoinObtained.toFixed(2), \" \").concat(config.preferredStablecoin, \"\\n\") + \"\\uD83D\\uDD04 Step 2: Bought \".concat(crypto1Bought.toFixed(6), \" \").concat(config.crypto1, \"\\n\") + \"\\uD83D\\uDCCA Level: \".concat(activeRow.orderLevel, \" → \").concat(newLevel, \"\\n\") + \"\\uD83D\\uDCC8 Mode: Stablecoin Swap\");\n                                actionsTaken++;\n                                // Update local balance for subsequent checks in this cycle\n                                currentCrypto2Balance -= amountCrypto2ToUse;\n                                currentCrypto1Balance += crypto1Bought;\n                            } else {\n                                // Insufficient balance - send Telegram notification\n                                console.log(\"❌ Insufficient \".concat(config.crypto2, \" balance for stablecoin swap: \").concat(currentCrypto2Balance.toFixed(6), \" < \").concat(amountCrypto2ToUse.toFixed(6)));\n                                sendLowBalanceNotification(config.crypto2, currentCrypto2Balance, amountCrypto2ToUse).catch(console.error);\n                            }\n                        }\n                        // STEP 3: Check and Process Inferior TargetRow (TargetRowN_minus_1) - ALWAYS, regardless of BUY\n                        const currentCounter = activeRow.counter;\n                        const inferiorRow = sortedRowsForLogic.find({\n                            \"TradingProvider.useEffect.inferiorRow\": (row)=>row.counter === currentCounter - 1\n                        }[\"TradingProvider.useEffect.inferiorRow\"]);\n                        console.log(\"\\uD83D\\uDD0D StablecoinSwap SELL Check:\", {\n                            currentCounter: currentCounter,\n                            inferiorRowFound: !!inferiorRow,\n                            inferiorRowStatus: inferiorRow === null || inferiorRow === void 0 ? void 0 : inferiorRow.status,\n                            inferiorRowCrypto1Held: inferiorRow === null || inferiorRow === void 0 ? void 0 : inferiorRow.crypto1AmountHeld,\n                            inferiorRowOriginalCost: inferiorRow === null || inferiorRow === void 0 ? void 0 : inferiorRow.originalCostCrypto2,\n                            canExecuteSell: !!(inferiorRow && inferiorRow.status === \"Full\" && inferiorRow.crypto1AmountHeld && inferiorRow.originalCostCrypto2)\n                        });\n                        if (inferiorRow && inferiorRow.status === \"Full\" && inferiorRow.crypto1AmountHeld && inferiorRow.originalCostCrypto2) {\n                            // Execute Two-Step \"Sell Crypto1 & Reacquire Crypto2 via Stablecoin\" for TargetRowN_minus_1\n                            const amountCrypto1ToSell = inferiorRow.crypto1AmountHeld;\n                            // Step A: Sell Crypto1 for PreferredStablecoin\n                            // Use current fluctuating market price for realistic P/L\n                            const crypto1USDPrice = getUSDPrice(config.crypto1 || 'BTC');\n                            const crypto2USDPrice = getUSDPrice(config.crypto2 || 'USDT');\n                            const stablecoinUSDPrice = getUSDPrice(config.preferredStablecoin || 'USDT');\n                            // Apply current market price fluctuation to Crypto1 price\n                            const fluctuatedCrypto1Price = crypto1USDPrice * (state.currentMarketPrice / (crypto1USDPrice / crypto2USDPrice));\n                            const crypto1StablecoinPrice = fluctuatedCrypto1Price / stablecoinUSDPrice;\n                            const stablecoinFromC1Sell = amountCrypto1ToSell * crypto1StablecoinPrice;\n                            // Step B: Buy Crypto2 with Stablecoin\n                            const crypto2StablecoinPrice = crypto2USDPrice / stablecoinUSDPrice;\n                            const crypto2Reacquired = stablecoinFromC1Sell / crypto2StablecoinPrice;\n                            // Calculate realized profit in Crypto2\n                            // We compare the value of crypto2 we got back vs what we originally spent\n                            const originalCost = inferiorRow.originalCostCrypto2 || 0;\n                            const realizedProfitInCrypto2 = crypto2Reacquired - originalCost;\n                            // Calculate Crypto1 profit/loss - this should be the profit in terms of Crypto1\n                            // For StablecoinSwap, we need to calculate how much Crypto1 profit this represents\n                            const realizedProfitCrypto1 = crypto1StablecoinPrice > 0 ? realizedProfitInCrypto2 / crypto1StablecoinPrice : 0;\n                            // IMPORTANT: If P/L is exactly 0, it might indicate a price calculation issue\n                            if (realizedProfitInCrypto2 === 0) {\n                                console.warn(\"⚠️ P/L is exactly 0 - this might indicate an issue:\\n                - Are prices changing between BUY and SELL?\\n                - Is the market price fluctuation working?\\n                - Current market price: \".concat(state.currentMarketPrice));\n                            }\n                            console.log(\"\\uD83D\\uDCB0 StablecoinSwap P/L Calculation DETAILED:\\n              - Inferior Row Counter: \".concat(inferiorRow.counter, \"\\n              - Original Cost (Crypto2): \").concat(originalCost, \"\\n              - Crypto2 Reacquired: \").concat(crypto2Reacquired, \"\\n              - Realized P/L (Crypto2): \").concat(realizedProfitInCrypto2, \"\\n              - Crypto1 Stablecoin Price: \").concat(crypto1StablecoinPrice, \"\\n              - Realized P/L (Crypto1): \").concat(realizedProfitCrypto1, \"\\n              - Is Profitable: \").concat(realizedProfitInCrypto2 > 0 ? 'YES' : 'NO', \"\\n              - Calculation: \").concat(crypto2Reacquired, \" - \").concat(originalCost, \" = \").concat(realizedProfitInCrypto2));\n                            // Update Row N-1: Full → Free, Level UNCHANGED, Vars cleared\n                            const updatedInferiorRow = {\n                                ...inferiorRow,\n                                status: \"Free\",\n                                // orderLevel: REMAINS UNCHANGED per specification\n                                crypto1AmountHeld: undefined,\n                                originalCostCrypto2: undefined,\n                                valueLevel: config.baseBid * Math.pow(config.multiplier, inferiorRow.orderLevel),\n                                crypto1Var: 0,\n                                crypto2Var: 0\n                            };\n                            dispatch({\n                                type: 'UPDATE_TARGET_PRICE_ROW',\n                                payload: updatedInferiorRow\n                            });\n                            dispatch({\n                                type: 'UPDATE_BALANCES',\n                                payload: {\n                                    crypto1: currentCrypto1Balance - amountCrypto1ToSell,\n                                    crypto2: currentCrypto2Balance + crypto2Reacquired\n                                }\n                            });\n                            // Add history entries for both steps of the N-1 stablecoin swap\n                            // Step A: SELL Crypto1 for Stablecoin (with profit/loss data for analytics)\n                            console.log(\"\\uD83D\\uDCCA Adding StablecoinSwap SELL order to history with P/L:\", {\n                                realizedProfitLossCrypto2: realizedProfitInCrypto2,\n                                realizedProfitLossCrypto1: realizedProfitCrypto1,\n                                pair: \"\".concat(config.crypto1, \"/\").concat(config.preferredStablecoin),\n                                orderType: \"SELL\",\n                                amountCrypto1: amountCrypto1ToSell,\n                                avgPrice: crypto1StablecoinPrice,\n                                valueCrypto2: stablecoinFromC1Sell,\n                                price1: crypto1StablecoinPrice,\n                                crypto1Symbol: config.crypto1,\n                                crypto2Symbol: config.preferredStablecoin\n                            });\n                            dispatch({\n                                type: 'ADD_ORDER_HISTORY_ENTRY',\n                                payload: {\n                                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                    timestamp: Date.now(),\n                                    pair: \"\".concat(config.crypto1, \"/\").concat(config.preferredStablecoin),\n                                    crypto1: config.crypto1,\n                                    orderType: \"SELL\",\n                                    amountCrypto1: amountCrypto1ToSell,\n                                    avgPrice: crypto1StablecoinPrice,\n                                    valueCrypto2: stablecoinFromC1Sell,\n                                    price1: crypto1StablecoinPrice,\n                                    crypto1Symbol: config.crypto1 || '',\n                                    crypto2Symbol: config.preferredStablecoin || ''\n                                }\n                            });\n                            // Step B: BUY Crypto2 with Stablecoin (no profit/loss as it's just conversion)\n                            dispatch({\n                                type: 'ADD_ORDER_HISTORY_ENTRY',\n                                payload: {\n                                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                    timestamp: Date.now(),\n                                    pair: \"\".concat(config.crypto2, \"/\").concat(config.preferredStablecoin),\n                                    crypto1: config.crypto2,\n                                    orderType: \"BUY\",\n                                    amountCrypto1: crypto2Reacquired,\n                                    avgPrice: crypto2StablecoinPrice,\n                                    valueCrypto2: stablecoinFromC1Sell,\n                                    price1: crypto2StablecoinPrice,\n                                    crypto1Symbol: config.crypto2 || '',\n                                    crypto2Symbol: config.preferredStablecoin || ''\n                                }\n                            });\n                            console.log(\"✅ STABLECOIN SELL: Counter \".concat(currentCounter - 1, \" | Step A: Sold \").concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \" → \").concat(stablecoinFromC1Sell.toFixed(2), \" \").concat(config.preferredStablecoin, \" | Step B: Bought \").concat(crypto2Reacquired.toFixed(2), \" \").concat(config.crypto2, \" | Profit: \").concat(realizedProfitInCrypto2.toFixed(2), \" \").concat(config.crypto2, \" | Level: \").concat(inferiorRow.orderLevel, \" (unchanged)\"));\n                            toast({\n                                title: \"SELL Executed (Stablecoin)\",\n                                description: \"Counter \".concat(currentCounter - 1, \": Profit \").concat(realizedProfitInCrypto2.toFixed(2), \" \").concat(config.crypto2, \" via \").concat(config.preferredStablecoin),\n                                duration: 2000\n                            });\n                            playSound('soundOrderExecution');\n                            // Send Telegram notification for Stablecoin SELL\n                            const profitEmoji = realizedProfitInCrypto2 > 0 ? '📈' : realizedProfitInCrypto2 < 0 ? '📉' : '➖';\n                            sendTelegramNotification(\"\\uD83D\\uDD34 <b>SELL EXECUTED (Stablecoin Swap)</b>\\n\" + \"\\uD83D\\uDCCA Counter: \".concat(currentCounter - 1, \"\\n\") + \"\\uD83D\\uDD04 Step A: Sold \".concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \" → \").concat(stablecoinFromC1Sell.toFixed(2), \" \").concat(config.preferredStablecoin, \"\\n\") + \"\\uD83D\\uDD04 Step B: Bought \".concat(crypto2Reacquired.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\".concat(profitEmoji, \" Profit: \").concat(realizedProfitInCrypto2.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\\uD83D\\uDCCA Level: \".concat(inferiorRow.orderLevel, \" (unchanged)\\n\") + \"\\uD83D\\uDCC8 Mode: Stablecoin Swap\");\n                            actionsTaken++;\n                            // Update local balance for subsequent checks in this cycle\n                            currentCrypto1Balance -= amountCrypto1ToSell;\n                            currentCrypto2Balance += crypto2Reacquired;\n                        }\n                    }\n                }\n            }\n            if (actionsTaken > 0) {\n                console.log(\"\\uD83C\\uDFAF CYCLE COMPLETE: \".concat(actionsTaken, \" actions taken at price $\").concat(currentMarketPrice.toFixed(2)));\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus,\n        state.currentMarketPrice,\n        state.targetPriceRows,\n        state.config,\n        state.crypto1Balance,\n        state.crypto2Balance,\n        state.stablecoinBalance,\n        dispatch,\n        toast,\n        playSound,\n        sendTelegramNotification\n    ]);\n    const getDisplayOrders = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[getDisplayOrders]\": ()=>{\n            if (!state.targetPriceRows || !Array.isArray(state.targetPriceRows)) {\n                return [];\n            }\n            return state.targetPriceRows.map({\n                \"TradingProvider.useCallback[getDisplayOrders]\": (row)=>{\n                    const currentPrice = state.currentMarketPrice || 0;\n                    const targetPrice = row.targetPrice || 0;\n                    const percentFromActualPrice = currentPrice && targetPrice ? (currentPrice / targetPrice - 1) * 100 : 0;\n                    let incomeCrypto1;\n                    let incomeCrypto2;\n                    if (row.status === \"Full\" && row.crypto1AmountHeld && row.originalCostCrypto2) {\n                        const totalUnrealizedProfitInCrypto2 = currentPrice * row.crypto1AmountHeld - row.originalCostCrypto2;\n                        incomeCrypto2 = totalUnrealizedProfitInCrypto2 * state.config.incomeSplitCrypto2Percent / 100;\n                        if (currentPrice > 0) {\n                            incomeCrypto1 = totalUnrealizedProfitInCrypto2 * state.config.incomeSplitCrypto1Percent / 100 / currentPrice;\n                        }\n                    }\n                    return {\n                        ...row,\n                        currentPrice,\n                        priceDifference: targetPrice - currentPrice,\n                        priceDifferencePercent: currentPrice > 0 ? (targetPrice - currentPrice) / currentPrice * 100 : 0,\n                        potentialProfitCrypto1: state.config.incomeSplitCrypto1Percent / 100 * row.valueLevel / (targetPrice || 1),\n                        potentialProfitCrypto2: state.config.incomeSplitCrypto2Percent / 100 * row.valueLevel,\n                        percentFromActualPrice,\n                        incomeCrypto1,\n                        incomeCrypto2\n                    };\n                }\n            }[\"TradingProvider.useCallback[getDisplayOrders]\"]).sort({\n                \"TradingProvider.useCallback[getDisplayOrders]\": (a, b)=>b.targetPrice - a.targetPrice\n            }[\"TradingProvider.useCallback[getDisplayOrders]\"]);\n        }\n    }[\"TradingProvider.useCallback[getDisplayOrders]\"], [\n        state.targetPriceRows,\n        state.currentMarketPrice,\n        state.config.incomeSplitCrypto1Percent,\n        state.config.incomeSplitCrypto2Percent,\n        state.config.baseBid,\n        state.config.multiplier\n    ]);\n    // Backend Integration Functions\n    const saveConfigToBackend = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[saveConfigToBackend]\": async (config)=>{\n            try {\n                var _response_config;\n                const configData = {\n                    name: \"\".concat(config.crypto1, \"/\").concat(config.crypto2, \" \").concat(config.tradingMode),\n                    tradingMode: config.tradingMode,\n                    crypto1: config.crypto1,\n                    crypto2: config.crypto2,\n                    baseBid: config.baseBid,\n                    multiplier: config.multiplier,\n                    numDigits: config.numDigits,\n                    slippagePercent: config.slippagePercent,\n                    incomeSplitCrypto1Percent: config.incomeSplitCrypto1Percent,\n                    incomeSplitCrypto2Percent: config.incomeSplitCrypto2Percent,\n                    preferredStablecoin: config.preferredStablecoin,\n                    targetPrices: state.targetPriceRows.map({\n                        \"TradingProvider.useCallback[saveConfigToBackend]\": (row)=>row.targetPrice\n                    }[\"TradingProvider.useCallback[saveConfigToBackend]\"])\n                };\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.tradingApi.saveConfig(configData);\n                console.log('✅ Config saved to backend:', response);\n                return ((_response_config = response.config) === null || _response_config === void 0 ? void 0 : _response_config.id) || null;\n            } catch (error) {\n                console.error('❌ Failed to save config to backend:', error);\n                toast({\n                    title: \"Backend Error\",\n                    description: \"Failed to save configuration to backend\",\n                    variant: \"destructive\",\n                    duration: 3000\n                });\n                return null;\n            }\n        }\n    }[\"TradingProvider.useCallback[saveConfigToBackend]\"], [\n        state.targetPriceRows,\n        toast\n    ]);\n    const startBackendBot = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[startBackendBot]\": async (configId)=>{\n            try {\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.tradingApi.startBot(configId);\n                console.log('✅ Bot started on backend:', response);\n                toast({\n                    title: \"Bot Started\",\n                    description: \"Trading bot started successfully on backend\",\n                    duration: 3000\n                });\n                return true;\n            } catch (error) {\n                console.error('❌ Failed to start bot on backend:', error);\n                toast({\n                    title: \"Backend Error\",\n                    description: \"Failed to start bot on backend\",\n                    variant: \"destructive\",\n                    duration: 3000\n                });\n                return false;\n            }\n        }\n    }[\"TradingProvider.useCallback[startBackendBot]\"], [\n        toast\n    ]);\n    const stopBackendBot = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[stopBackendBot]\": async (configId)=>{\n            try {\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.tradingApi.stopBot(configId);\n                console.log('✅ Bot stopped on backend:', response);\n                toast({\n                    title: \"Bot Stopped\",\n                    description: \"Trading bot stopped successfully on backend\",\n                    duration: 3000\n                });\n                return true;\n            } catch (error) {\n                console.error('❌ Failed to stop bot on backend:', error);\n                toast({\n                    title: \"Backend Error\",\n                    description: \"Failed to stop bot on backend\",\n                    variant: \"destructive\",\n                    duration: 3000\n                });\n                return false;\n            }\n        }\n    }[\"TradingProvider.useCallback[stopBackendBot]\"], [\n        toast\n    ]);\n    const checkBackendStatus = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[checkBackendStatus]\": async ()=>{\n            const apiUrl = \"http://localhost:5000\";\n            if (!apiUrl) {\n                console.error('Error: NEXT_PUBLIC_API_URL is not defined. Backend connectivity check cannot be performed.');\n                dispatch({\n                    type: 'SET_BACKEND_STATUS',\n                    payload: 'offline'\n                });\n                return;\n            }\n            try {\n                const healthResponse = await fetch(\"\".concat(apiUrl, \"/health/\"));\n                if (!healthResponse.ok) {\n                    // Log more details if the response was received but not OK\n                    console.error(\"Backend health check failed with status: \".concat(healthResponse.status, \" \").concat(healthResponse.statusText));\n                    const responseText = await healthResponse.text().catch({\n                        \"TradingProvider.useCallback[checkBackendStatus]\": ()=>'Could not read response text.'\n                    }[\"TradingProvider.useCallback[checkBackendStatus]\"]);\n                    console.error('Backend health check response body:', responseText);\n                }\n                dispatch({\n                    type: 'SET_BACKEND_STATUS',\n                    payload: healthResponse.ok ? 'online' : 'offline'\n                });\n            } catch (error) {\n                dispatch({\n                    type: 'SET_BACKEND_STATUS',\n                    payload: 'offline'\n                });\n                console.error('Backend connectivity check failed. Error details:', error);\n                if (error.cause) {\n                    console.error('Fetch error cause:', error.cause);\n                }\n                // It's also useful to log the apiUrl to ensure it's what we expect\n                console.error('Attempted to fetch API URL:', \"\".concat(apiUrl, \"/health/\"));\n            }\n        }\n    }[\"TradingProvider.useCallback[checkBackendStatus]\"], [\n        dispatch\n    ]);\n    // Initialize backend status check (one-time only)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            // Initial check for backend status only\n            checkBackendStatus();\n        }\n    }[\"TradingProvider.useEffect\"], [\n        checkBackendStatus\n    ]);\n    // Save state to localStorage whenever it changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            saveStateToLocalStorage(state);\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state\n    ]);\n    // Effect to handle bot warm-up period (immediate execution)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            if (state.botSystemStatus === 'WarmingUp') {\n                console.log(\"Bot is Warming Up... Immediate execution enabled.\");\n                // Immediate transition to Running state - no delays\n                dispatch({\n                    type: 'SYSTEM_COMPLETE_WARMUP'\n                });\n                console.log(\"Bot is now Running immediately.\");\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus,\n        dispatch\n    ]);\n    // Effect to handle session runtime tracking\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            const currentSessionId = sessionManager.getCurrentSessionId();\n            if (currentSessionId) {\n                if (state.botSystemStatus === 'Running') {\n                    // Bot started running, start runtime tracking\n                    sessionManager.startSessionRuntime(currentSessionId);\n                    console.log('✅ Started runtime tracking for session:', currentSessionId);\n                } else if (state.botSystemStatus === 'Stopped') {\n                    // Bot stopped, stop runtime tracking\n                    sessionManager.stopSessionRuntime(currentSessionId);\n                    console.log('⏹️ Stopped runtime tracking for session:', currentSessionId);\n                }\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus\n    ]);\n    // Auto-create session when bot actually starts running (not during warmup)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            // Check if we need to create a session when bot actually starts running\n            if (state.botSystemStatus === 'Running' && !sessionManager.getCurrentSessionId()) {\n                // Only create if we have valid crypto configuration AND target prices set\n                // This prevents auto-creation for fresh windows\n                if (state.config.crypto1 && state.config.crypto2 && state.targetPriceRows.length > 0) {\n                    sessionManager.createNewSessionWithAutoName(state.config).then({\n                        \"TradingProvider.useEffect\": (sessionId)=>{\n                            sessionManager.setCurrentSession(sessionId);\n                            console.log('✅ Auto-created session when bot started:', sessionId);\n                        }\n                    }[\"TradingProvider.useEffect\"]).catch({\n                        \"TradingProvider.useEffect\": (error)=>{\n                            console.error('❌ Failed to auto-create session:', error);\n                        }\n                    }[\"TradingProvider.useEffect\"]);\n                }\n            }\n            // Handle runtime tracking based on bot status\n            const currentSessionId = sessionManager.getCurrentSessionId();\n            if (currentSessionId) {\n                if (state.botSystemStatus === 'Running') {\n                    // Start runtime tracking when bot becomes active\n                    sessionManager.startSessionRuntime(currentSessionId);\n                    console.log('⏱️ Started runtime tracking for session:', currentSessionId);\n                } else if (state.botSystemStatus === 'Stopped') {\n                    // Stop runtime tracking when bot stops\n                    sessionManager.stopSessionRuntime(currentSessionId);\n                    console.log('⏹️ Stopped runtime tracking for session:', currentSessionId);\n                }\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus,\n        state.config.crypto1,\n        state.config.crypto2\n    ]);\n    // Handle crypto pair changes during active trading\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            const currentSessionId = sessionManager.getCurrentSessionId();\n            if (currentSessionId) {\n                const currentSession = sessionManager.loadSession(currentSessionId);\n                // Check if crypto pair has changed from the current session\n                if (currentSession && (currentSession.config.crypto1 !== state.config.crypto1 || currentSession.config.crypto2 !== state.config.crypto2)) {\n                    console.log('🔄 Crypto pair changed during session, auto-saving and resetting...');\n                    // Auto-save current session if bot was running or has data\n                    if (state.botSystemStatus === 'Running' || state.targetPriceRows.length > 0 || state.orderHistory.length > 0) {\n                        // Stop the bot if it's running\n                        if (state.botSystemStatus === 'Running') {\n                            stopBackendBot(currentSessionId);\n                        }\n                        // Save current session with timestamp\n                        const timestamp = new Date().toLocaleString('en-US', {\n                            month: 'short',\n                            day: 'numeric',\n                            hour: '2-digit',\n                            minute: '2-digit',\n                            hour12: false\n                        });\n                        const savedName = \"\".concat(currentSession.name, \" (AutoSaved \").concat(timestamp, \")\");\n                        sessionManager.createNewSession(savedName, currentSession.config).then({\n                            \"TradingProvider.useEffect\": async (savedSessionId)=>{\n                                await sessionManager.saveSession(savedSessionId, currentSession.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, false);\n                                console.log('💾 AutoSaved session:', savedName);\n                            }\n                        }[\"TradingProvider.useEffect\"]);\n                    }\n                    // Reset for new crypto pair\n                    dispatch({\n                        type: 'RESET_FOR_NEW_CRYPTO'\n                    });\n                    // Only create new session for the new crypto pair if bot was actually running\n                    if (state.config.crypto1 && state.config.crypto2 && state.botSystemStatus === 'Running') {\n                        const currentBalances = {\n                            crypto1: state.crypto1Balance,\n                            crypto2: state.crypto2Balance,\n                            stablecoin: state.stablecoinBalance\n                        };\n                        sessionManager.createNewSessionWithAutoName(state.config, undefined, currentBalances).then({\n                            \"TradingProvider.useEffect\": (newSessionId)=>{\n                                sessionManager.setCurrentSession(newSessionId);\n                                console.log('🆕 Created new session for crypto pair:', state.config.crypto1, '/', state.config.crypto2, 'with balances:', currentBalances);\n                                toast({\n                                    title: \"Crypto Pair Changed\",\n                                    description: \"Previous session AutoSaved. New session created for \".concat(state.config.crypto1, \"/\").concat(state.config.crypto2),\n                                    duration: 5000\n                                });\n                            }\n                        }[\"TradingProvider.useEffect\"]);\n                    } else {\n                        // If bot wasn't running, just clear the current session without creating a new one\n                        sessionManager.clearCurrentSession();\n                        console.log('🔄 Crypto pair changed but bot wasn\\'t running - cleared current session');\n                    }\n                }\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.config.crypto1,\n        state.config.crypto2\n    ]);\n    // Initialize network monitoring and auto-save\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const networkMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.NetworkMonitor.getInstance();\n            const autoSaveManager = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.AutoSaveManager.getInstance();\n            const memoryMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.MemoryMonitor.getInstance();\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            // Set up network status listener\n            const unsubscribeNetwork = networkMonitor.addListener({\n                \"TradingProvider.useEffect.unsubscribeNetwork\": (isOnline, isInitial)=>{\n                    console.log(\"\\uD83C\\uDF10 Network status changed: \".concat(isOnline ? 'Online' : 'Offline'));\n                    // Handle offline state - stop bot and save session\n                    if (!isOnline && !isInitial) {\n                        // Stop the bot if it's running\n                        if (state.botSystemStatus === 'Running') {\n                            console.log('🔴 Internet lost - stopping bot and saving session');\n                            dispatch({\n                                type: 'SYSTEM_STOP_BOT'\n                            });\n                            // Auto-save current session\n                            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n                            const currentSessionId = sessionManager.getCurrentSessionId();\n                            if (currentSessionId) {\n                                const currentSession = sessionManager.loadSession(currentSessionId);\n                                if (currentSession) {\n                                    // Create offline backup session\n                                    const timestamp = new Date().toLocaleString('en-US', {\n                                        month: 'short',\n                                        day: 'numeric',\n                                        hour: '2-digit',\n                                        minute: '2-digit',\n                                        hour12: false\n                                    });\n                                    const offlineName = \"\".concat(currentSession.name, \" (Offline Backup \").concat(timestamp, \")\");\n                                    sessionManager.createNewSession(offlineName, currentSession.config).then({\n                                        \"TradingProvider.useEffect.unsubscribeNetwork\": async (backupSessionId)=>{\n                                            await sessionManager.saveSession(backupSessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, false);\n                                            console.log('💾 Created offline backup session:', offlineName);\n                                        }\n                                    }[\"TradingProvider.useEffect.unsubscribeNetwork\"]);\n                                }\n                            }\n                        }\n                        toast({\n                            title: \"Network Disconnected\",\n                            description: \"Bot stopped and session saved. Trading paused until connection restored.\",\n                            variant: \"destructive\",\n                            duration: 8000\n                        });\n                    } else if (isOnline && !isInitial) {\n                        toast({\n                            title: \"Network Reconnected\",\n                            description: \"Connection restored. You can resume trading.\",\n                            duration: 3000\n                        });\n                    }\n                }\n            }[\"TradingProvider.useEffect.unsubscribeNetwork\"]);\n            // Set up memory monitoring\n            const unsubscribeMemory = memoryMonitor.addListener({\n                \"TradingProvider.useEffect.unsubscribeMemory\": (memory)=>{\n                    const usedMB = memory.usedJSHeapSize / 1024 / 1024;\n                    if (usedMB > 150) {\n                        console.warn(\"\\uD83E\\uDDE0 High memory usage: \".concat(usedMB.toFixed(2), \"MB\"));\n                        toast({\n                            title: \"High Memory Usage\",\n                            description: \"Memory usage is high (\".concat(usedMB.toFixed(0), \"MB). Consider refreshing the page.\"),\n                            variant: \"destructive\",\n                            duration: 5000\n                        });\n                    }\n                }\n            }[\"TradingProvider.useEffect.unsubscribeMemory\"]);\n            // Set up auto-save\n            const saveFunction = {\n                \"TradingProvider.useEffect.saveFunction\": async ()=>{\n                    try {\n                        // Save to session manager if we have a current session\n                        const currentSessionId = sessionManager.getCurrentSessionId();\n                        if (currentSessionId) {\n                            await sessionManager.saveSession(currentSessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, state.botSystemStatus === 'Running');\n                        }\n                        // Also save to localStorage as backup\n                        saveStateToLocalStorage(state);\n                    } catch (error) {\n                        console.error('Auto-save failed:', error);\n                    }\n                }\n            }[\"TradingProvider.useEffect.saveFunction\"];\n            autoSaveManager.enable(saveFunction, 30000); // Auto-save every 30 seconds\n            // Add beforeunload listener to save session on browser close/refresh\n            const handleBeforeUnload = {\n                \"TradingProvider.useEffect.handleBeforeUnload\": (event)=>{\n                    // Save immediately before unload\n                    saveFunction();\n                    // If bot is running, show warning\n                    if (state.botSystemStatus === 'Running') {\n                        const message = 'Trading bot is currently running. Are you sure you want to leave?';\n                        event.returnValue = message;\n                        return message;\n                    }\n                }\n            }[\"TradingProvider.useEffect.handleBeforeUnload\"];\n            window.addEventListener('beforeunload', handleBeforeUnload);\n            // Cleanup function\n            return ({\n                \"TradingProvider.useEffect\": ()=>{\n                    unsubscribeNetwork();\n                    unsubscribeMemory();\n                    autoSaveManager.disable();\n                    window.removeEventListener('beforeunload', handleBeforeUnload);\n                }\n            })[\"TradingProvider.useEffect\"];\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state,\n        toast\n    ]);\n    // Force save when bot status changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const autoSaveManager = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.AutoSaveManager.getInstance();\n            autoSaveManager.saveNow();\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus\n    ]);\n    // Manual save session function\n    const saveCurrentSession = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[saveCurrentSession]\": async ()=>{\n            try {\n                const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n                const currentSessionId = sessionManager.getCurrentSessionId();\n                if (!currentSessionId) {\n                    // No current session, only create one if bot is running or has meaningful data\n                    if (state.config.crypto1 && state.config.crypto2 && (state.botSystemStatus === 'Running' || state.targetPriceRows.length > 0 || state.orderHistory.length > 0)) {\n                        const currentBalances = {\n                            crypto1: state.crypto1Balance,\n                            crypto2: state.crypto2Balance,\n                            stablecoin: state.stablecoinBalance\n                        };\n                        const sessionId = await sessionManager.createNewSessionWithAutoName(state.config, undefined, currentBalances);\n                        sessionManager.setCurrentSession(sessionId);\n                        await sessionManager.saveSession(sessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, state.botSystemStatus === 'Running');\n                        return true;\n                    }\n                    console.log('⚠️ No session to save - bot not running and no meaningful data');\n                    return false;\n                }\n                // Save existing session\n                return await sessionManager.saveSession(currentSessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, state.botSystemStatus === 'Running');\n            } catch (error) {\n                console.error('Failed to save current session:', error);\n                return false;\n            }\n        }\n    }[\"TradingProvider.useCallback[saveCurrentSession]\"], [\n        state\n    ]);\n    // Context value\n    const contextValue = {\n        ...state,\n        dispatch,\n        setTargetPrices,\n        getDisplayOrders,\n        checkBackendStatus,\n        fetchMarketPrice,\n        startBackendBot,\n        stopBackendBot,\n        saveConfigToBackend,\n        saveCurrentSession,\n        backendStatus: state.backendStatus,\n        botSystemStatus: state.botSystemStatus,\n        isBotActive: state.botSystemStatus === 'Running'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TradingContext.Provider, {\n        value: contextValue,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\contexts\\\\TradingContext.tsx\",\n        lineNumber: 1894,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TradingProvider, \"Dul/342NNfDBwKH7syILMVemn7Y=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__.useToast\n    ];\n});\n_c = TradingProvider;\n// Custom hook to use the trading context\nconst useTradingContext = ()=>{\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(TradingContext);\n    if (context === undefined) {\n        throw new Error('useTradingContext must be used within a TradingProvider');\n    }\n    return context;\n};\n_s1(useTradingContext, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"TradingProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/TradingContext.tsx\n"));

/***/ })

});