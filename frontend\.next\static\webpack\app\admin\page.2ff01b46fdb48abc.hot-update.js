"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./src/contexts/TradingContext.tsx":
/*!*****************************************!*\
  !*** ./src/contexts/TradingContext.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TradingProvider: () => (/* binding */ TradingProvider),\n/* harmony export */   useTradingContext: () => (/* binding */ useTradingContext)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/types */ \"(app-pages-browser)/./src/lib/types.tsx\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/session-manager */ \"(app-pages-browser)/./src/lib/session-manager.ts\");\n/* harmony import */ var _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/network-monitor */ \"(app-pages-browser)/./src/lib/network-monitor.ts\");\n/* __next_internal_client_entry_do_not_use__ TradingProvider,useTradingContext auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n// Define locally to avoid import issues\nconst DEFAULT_QUOTE_CURRENCIES = [\n    \"USDT\",\n    \"USDC\",\n    \"BTC\"\n];\n\n\n\n\n// Add this function to calculate the initial market price\nconst calculateInitialMarketPrice = (config)=>{\n    // Return 0 if either crypto is not selected\n    if (!config.crypto1 || !config.crypto2) {\n        return 0;\n    }\n    // Default fallback value for valid pairs\n    return calculateFallbackMarketPrice(config);\n};\n// Enhanced API function to get market price from Binance for any trading pair\nconst getMarketPriceFromBinance = async (config)=>{\n    try {\n        // Return 0 if either crypto is not selected\n        if (!config.crypto1 || !config.crypto2) {\n            return 0;\n        }\n        const symbol = \"\".concat(config.crypto1).concat(config.crypto2).toUpperCase();\n        // Use Binance API for real-time prices\n        const response = await fetch(\"https://api.binance.com/api/v3/ticker/price?symbol=\".concat(symbol), {\n            method: 'GET',\n            headers: {\n                'Content-Type': 'application/json'\n            }\n        });\n        if (response.ok) {\n            const data = await response.json();\n            const price = parseFloat(data.price);\n            if (price > 0) {\n                console.log(\"✅ Binance price: \".concat(config.crypto1, \"/\").concat(config.crypto2, \" = \").concat(price));\n                return price;\n            }\n        } else {\n            console.warn(\"Binance API error for \".concat(symbol, \":\"), response.status, response.statusText);\n        }\n        // If Binance fails, fallback to mock price\n        const mockPrice = calculateFallbackMarketPrice(config);\n        console.log(\"⚠️ Using fallback price: \".concat(config.crypto1, \"/\").concat(config.crypto2, \" = \").concat(mockPrice));\n        return mockPrice;\n    } catch (error) {\n        console.error('Error fetching price from Binance:', error);\n        return calculateFallbackMarketPrice(config);\n    }\n};\n// Helper function to map crypto symbols to CoinGecko IDs\nconst getCoinGeckoId = (symbol)=>{\n    const mapping = {\n        'BTC': 'bitcoin',\n        'ETH': 'ethereum',\n        'SOL': 'solana',\n        'ADA': 'cardano',\n        'DOT': 'polkadot',\n        'MATIC': 'matic-network',\n        'AVAX': 'avalanche-2',\n        'LINK': 'chainlink',\n        'UNI': 'uniswap',\n        'USDT': 'tether',\n        'USDC': 'usd-coin',\n        'BUSD': 'binance-usd',\n        'DAI': 'dai'\n    };\n    return mapping[symbol.toUpperCase()] || null;\n};\n// Helper function to get stablecoin exchange rates for real market data\nconst getStablecoinExchangeRate = async (crypto, stablecoin)=>{\n    try {\n        // For stablecoin-to-stablecoin, assume 1:1 rate\n        if (getCoinGeckoId(crypto) && getCoinGeckoId(stablecoin)) {\n            const cryptoId = getCoinGeckoId(crypto);\n            const stablecoinId = getCoinGeckoId(stablecoin);\n            if (cryptoId === stablecoinId) return 1.0; // Same currency\n            // Get real exchange rate from CoinGecko\n            const response = await fetch(\"https://api.coingecko.com/api/v3/simple/price?ids=\".concat(cryptoId, \"&vs_currencies=\").concat(stablecoinId));\n            if (response.ok) {\n                var _data_cryptoId;\n                const data = await response.json();\n                const rate = cryptoId && stablecoinId ? (_data_cryptoId = data[cryptoId]) === null || _data_cryptoId === void 0 ? void 0 : _data_cryptoId[stablecoinId] : null;\n                if (rate > 0) {\n                    console.log(\"\\uD83D\\uDCCA Stablecoin rate: \".concat(crypto, \"/\").concat(stablecoin, \" = \").concat(rate));\n                    return rate;\n                }\n            }\n        }\n        // Fallback: calculate via USD prices\n        const cryptoUSDPrice = getUSDPrice(crypto);\n        const stablecoinUSDPrice = getUSDPrice(stablecoin);\n        const rate = cryptoUSDPrice / stablecoinUSDPrice;\n        console.log(\"\\uD83D\\uDCCA Fallback stablecoin rate: \".concat(crypto, \"/\").concat(stablecoin, \" = \").concat(rate, \" (via USD)\"));\n        return rate;\n    } catch (error) {\n        console.error('Error fetching stablecoin exchange rate:', error);\n        // Final fallback\n        const cryptoUSDPrice = getUSDPrice(crypto);\n        const stablecoinUSDPrice = getUSDPrice(stablecoin);\n        return cryptoUSDPrice / stablecoinUSDPrice;\n    }\n};\n// Real-time price cache\nlet priceCache = {};\nlet lastPriceUpdate = 0;\nconst PRICE_CACHE_DURATION = 2000; // 2 seconds cache for real-time updates\n// Helper function to get real-time USD price from Binance API\nconst getRealTimeUSDPrice = async (crypto)=>{\n    const now = Date.now();\n    const cacheKey = crypto.toUpperCase();\n    // Return cached price if still valid (2 seconds)\n    if (priceCache[cacheKey] && now - lastPriceUpdate < PRICE_CACHE_DURATION) {\n        return priceCache[cacheKey];\n    }\n    try {\n        // Use Binance API to get USD price (via USDT as proxy)\n        const symbol = \"\".concat(crypto.toUpperCase(), \"USDT\");\n        const response = await fetch(\"https://api.binance.com/api/v3/ticker/price?symbol=\".concat(symbol), {\n            method: 'GET',\n            headers: {\n                'Content-Type': 'application/json'\n            }\n        });\n        if (response.ok) {\n            const data = await response.json();\n            const price = parseFloat(data.price);\n            if (price && price > 0) {\n                priceCache[cacheKey] = price;\n                lastPriceUpdate = now;\n                console.log(\"\\uD83D\\uDCCA Binance USD price for \".concat(crypto, \": $\").concat(price));\n                return price;\n            }\n        } else {\n            console.warn(\"Binance USD price API error for \".concat(symbol, \":\"), response.status);\n        }\n    } catch (error) {\n        console.warn(\"⚠️ Failed to fetch Binance USD price for \".concat(crypto, \":\"), error);\n    }\n    // Fallback to static prices if API fails\n    return getUSDPrice(crypto);\n};\n// Synchronous helper function to get USD price (uses cached values or static fallback)\nconst getUSDPrice = (crypto)=>{\n    const cacheKey = crypto.toUpperCase();\n    // Return cached price if available\n    if (priceCache[cacheKey]) {\n        return priceCache[cacheKey];\n    }\n    // Fallback to static prices\n    return getStaticUSDPrice(crypto);\n};\n// Static fallback prices (used when API is unavailable)\nconst getStaticUSDPrice = (crypto)=>{\n    const usdPrices = {\n        // Major cryptocurrencies - Updated to current market prices\n        'BTC': 106000,\n        'ETH': 2500,\n        'SOL': 180,\n        'ADA': 0.85,\n        'DOGE': 0.32,\n        'LINK': 22,\n        'MATIC': 0.42,\n        'DOT': 6.5,\n        'AVAX': 38,\n        'SHIB': 0.000022,\n        'XRP': 2.1,\n        'LTC': 95,\n        'BCH': 420,\n        // DeFi tokens\n        'UNI': 15,\n        'AAVE': 180,\n        'MKR': 1800,\n        'SNX': 3.5,\n        'COMP': 85,\n        'YFI': 8500,\n        'SUSHI': 2.1,\n        '1INCH': 0.65,\n        'CRV': 0.85,\n        'UMA': 3.2,\n        // Layer 1 blockchains\n        'ATOM': 12,\n        'NEAR': 6.5,\n        'ALGO': 0.35,\n        'ICP': 14,\n        'HBAR': 0.28,\n        'APT': 12.5,\n        'TON': 5.8,\n        'FTM': 0.95,\n        'ONE': 0.025,\n        // Other popular tokens\n        'FIL': 8.5,\n        'TRX': 0.25,\n        'ETC': 35,\n        'VET': 0.055,\n        'QNT': 125,\n        'LDO': 2.8,\n        'CRO': 0.18,\n        'LUNC': 0.00015,\n        // Gaming & Metaverse\n        'MANA': 0.85,\n        'SAND': 0.75,\n        'AXS': 8.5,\n        'ENJ': 0.45,\n        'CHZ': 0.12,\n        // Infrastructure & Utility\n        'THETA': 2.1,\n        'FLOW': 1.2,\n        'XTZ': 1.8,\n        'EOS': 1.1,\n        'GRT': 0.28,\n        'BAT': 0.35,\n        // Privacy coins\n        'ZEC': 45,\n        'DASH': 35,\n        // DEX tokens\n        'LRC': 0.45,\n        'ZRX': 0.65,\n        'KNC': 0.85,\n        // Other tokens\n        'REN': 0.15,\n        'BAND': 2.5,\n        'STORJ': 0.85,\n        'NMR': 25,\n        'ANT': 8.5,\n        'BNT': 0.95,\n        'MLN': 35,\n        'REP': 15,\n        // Smaller cap tokens\n        'IOTX': 0.065,\n        'ZIL': 0.045,\n        'ICX': 0.35,\n        'QTUM': 4.5,\n        'ONT': 0.45,\n        'WAVES': 3.2,\n        'LSK': 1.8,\n        'NANO': 1.5,\n        'SC': 0.008,\n        'DGB': 0.025,\n        'RVN': 0.035,\n        'BTT': 0.0000015,\n        'WIN': 0.00015,\n        'HOT': 0.0035,\n        'DENT': 0.0018,\n        'NPXS': 0.00085,\n        'FUN': 0.0085,\n        'CELR': 0.025,\n        // Stablecoins\n        'USDT': 1.0,\n        'USDC': 1.0,\n        'FDUSD': 1.0,\n        'BUSD': 1.0,\n        'DAI': 1.0\n    };\n    return usdPrices[crypto.toUpperCase()] || 100;\n};\n// Enhanced fallback function for market price calculation supporting all trading pairs\nconst calculateFallbackMarketPrice = (config)=>{\n    const crypto1USDPrice = getUSDPrice(config.crypto1);\n    const crypto2USDPrice = getUSDPrice(config.crypto2);\n    // Calculate the ratio: how many units of crypto2 = 1 unit of crypto1\n    const basePrice = crypto1USDPrice / crypto2USDPrice;\n    // Add small random fluctuation\n    const fluctuation = (Math.random() - 0.5) * 0.02; // ±1%\n    const finalPrice = basePrice * (1 + fluctuation);\n    console.log(\"\\uD83D\\uDCCA Fallback price calculation: \".concat(config.crypto1, \" ($\").concat(crypto1USDPrice, \") / \").concat(config.crypto2, \" ($\").concat(crypto2USDPrice, \") = \").concat(finalPrice.toFixed(6)));\n    return finalPrice;\n};\nconst initialBaseConfig = {\n    tradingMode: \"SimpleSpot\",\n    crypto1: \"\",\n    crypto2: \"\",\n    baseBid: 100,\n    multiplier: 1.005,\n    numDigits: 4,\n    slippagePercent: 0.2,\n    incomeSplitCrypto1Percent: 50,\n    incomeSplitCrypto2Percent: 50,\n    preferredStablecoin: _lib_types__WEBPACK_IMPORTED_MODULE_2__.AVAILABLE_STABLECOINS[0]\n};\nconst initialTradingState = {\n    config: initialBaseConfig,\n    targetPriceRows: [],\n    orderHistory: [],\n    appSettings: _lib_types__WEBPACK_IMPORTED_MODULE_2__.DEFAULT_APP_SETTINGS,\n    currentMarketPrice: calculateInitialMarketPrice(initialBaseConfig),\n    // isBotActive: false, // Remove\n    botSystemStatus: 'Stopped',\n    crypto1Balance: 10,\n    crypto2Balance: 100000,\n    stablecoinBalance: 0,\n    backendStatus: 'unknown'\n};\nconst lastActionTimestampPerCounter = new Map();\n// LocalStorage persistence functions\nconst STORAGE_KEY = 'pluto_trading_state';\nconst saveStateToLocalStorage = (state)=>{\n    try {\n        if (true) {\n            const stateToSave = {\n                config: state.config,\n                targetPriceRows: state.targetPriceRows,\n                orderHistory: state.orderHistory,\n                appSettings: state.appSettings,\n                currentMarketPrice: state.currentMarketPrice,\n                crypto1Balance: state.crypto1Balance,\n                crypto2Balance: state.crypto2Balance,\n                stablecoinBalance: state.stablecoinBalance,\n                botSystemStatus: state.botSystemStatus,\n                timestamp: Date.now()\n            };\n            localStorage.setItem(STORAGE_KEY, JSON.stringify(stateToSave));\n        }\n    } catch (error) {\n        console.error('Failed to save state to localStorage:', error);\n    }\n};\nconst loadStateFromLocalStorage = ()=>{\n    try {\n        if (true) {\n            const savedState = localStorage.getItem(STORAGE_KEY);\n            if (savedState) {\n                const parsed = JSON.parse(savedState);\n                // Check if state is not too old (24 hours)\n                if (parsed.timestamp && Date.now() - parsed.timestamp < 24 * 60 * 60 * 1000) {\n                    return parsed;\n                }\n            }\n        }\n    } catch (error) {\n        console.error('Failed to load state from localStorage:', error);\n    }\n    return null;\n};\nconst tradingReducer = (state, action)=>{\n    switch(action.type){\n        case 'SET_CONFIG':\n            const newConfig = {\n                ...state.config,\n                ...action.payload\n            };\n            // If trading pair changes, reset market price (it will be re-calculated by effect)\n            if (action.payload.crypto1 || action.payload.crypto2) {\n                return {\n                    ...state,\n                    config: newConfig,\n                    currentMarketPrice: calculateInitialMarketPrice(newConfig)\n                };\n            }\n            return {\n                ...state,\n                config: newConfig\n            };\n        case 'SET_TARGET_PRICE_ROWS':\n            return {\n                ...state,\n                targetPriceRows: action.payload.sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }))\n            };\n        case 'ADD_TARGET_PRICE_ROW':\n            {\n                const newRows = [\n                    ...state.targetPriceRows,\n                    action.payload\n                ].sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }));\n                return {\n                    ...state,\n                    targetPriceRows: newRows\n                };\n            }\n        case 'UPDATE_TARGET_PRICE_ROW':\n            {\n                const updatedRows = state.targetPriceRows.map((row)=>row.id === action.payload.id ? action.payload : row).sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }));\n                return {\n                    ...state,\n                    targetPriceRows: updatedRows\n                };\n            }\n        case 'REMOVE_TARGET_PRICE_ROW':\n            {\n                const filteredRows = state.targetPriceRows.filter((row)=>row.id !== action.payload).sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }));\n                return {\n                    ...state,\n                    targetPriceRows: filteredRows\n                };\n            }\n        case 'ADD_ORDER_HISTORY_ENTRY':\n            return {\n                ...state,\n                orderHistory: [\n                    action.payload,\n                    ...state.orderHistory\n                ]\n            };\n        case 'CLEAR_ORDER_HISTORY':\n            return {\n                ...state,\n                orderHistory: []\n            };\n        case 'SET_APP_SETTINGS':\n            return {\n                ...state,\n                appSettings: {\n                    ...state.appSettings,\n                    ...action.payload\n                }\n            };\n        case 'SET_MARKET_PRICE':\n            return {\n                ...state,\n                currentMarketPrice: action.payload\n            };\n        case 'FLUCTUATE_MARKET_PRICE':\n            {\n                if (state.currentMarketPrice <= 0) return state;\n                // More realistic fluctuation: smaller, more frequent changes\n                const fluctuationFactor = (Math.random() - 0.5) * 0.006; // Approx +/- 0.3% per update\n                const newPrice = state.currentMarketPrice * (1 + fluctuationFactor);\n                return {\n                    ...state,\n                    currentMarketPrice: newPrice > 0 ? newPrice : state.currentMarketPrice\n                };\n            }\n        // case 'SET_BOT_STATUS': // Removed\n        case 'UPDATE_BALANCES':\n            return {\n                ...state,\n                crypto1Balance: action.payload.crypto1 !== undefined ? action.payload.crypto1 : state.crypto1Balance,\n                crypto2Balance: action.payload.crypto2 !== undefined ? action.payload.crypto2 : state.crypto2Balance,\n                stablecoinBalance: action.payload.stablecoin !== undefined ? action.payload.stablecoin : state.stablecoinBalance\n            };\n        case 'SET_BALANCES':\n            return {\n                ...state,\n                crypto1Balance: action.payload.crypto1,\n                crypto2Balance: action.payload.crypto2,\n                stablecoinBalance: action.payload.stablecoin !== undefined ? action.payload.stablecoin : state.stablecoinBalance\n            };\n        case 'UPDATE_STABLECOIN_BALANCE':\n            return {\n                ...state,\n                stablecoinBalance: action.payload\n            };\n        case 'RESET_SESSION':\n            const configForReset = {\n                ...state.config\n            };\n            return {\n                ...initialTradingState,\n                config: configForReset,\n                appSettings: {\n                    ...state.appSettings\n                },\n                currentMarketPrice: calculateInitialMarketPrice(configForReset),\n                // Preserve current balances instead of resetting to initial values\n                crypto1Balance: state.crypto1Balance,\n                crypto2Balance: state.crypto2Balance,\n                stablecoinBalance: state.stablecoinBalance\n            };\n        case 'SET_BACKEND_STATUS':\n            return {\n                ...state,\n                backendStatus: action.payload\n            };\n        case 'SYSTEM_START_BOT_INITIATE':\n            // Continue from previous state instead of resetting\n            return {\n                ...state,\n                botSystemStatus: 'WarmingUp'\n            };\n        case 'SYSTEM_COMPLETE_WARMUP':\n            return {\n                ...state,\n                botSystemStatus: 'Running'\n            };\n        case 'SYSTEM_STOP_BOT':\n            return {\n                ...state,\n                botSystemStatus: 'Stopped'\n            };\n        case 'SYSTEM_RESET_BOT':\n            // Fresh Start: Clear all target price rows and order history completely\n            lastActionTimestampPerCounter.clear();\n            // Clear current session to force new session name generation\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            sessionManager.clearCurrentSession();\n            return {\n                ...state,\n                botSystemStatus: 'Stopped',\n                targetPriceRows: [],\n                orderHistory: []\n            };\n        case 'SET_TARGET_PRICE_ROWS':\n            return {\n                ...state,\n                targetPriceRows: action.payload\n            };\n        case 'RESET_FOR_NEW_CRYPTO':\n            return {\n                ...initialTradingState,\n                config: state.config,\n                backendStatus: state.backendStatus,\n                botSystemStatus: 'Stopped',\n                currentMarketPrice: 0,\n                // Preserve current balances instead of resetting to initial values\n                crypto1Balance: state.crypto1Balance,\n                crypto2Balance: state.crypto2Balance,\n                stablecoinBalance: state.stablecoinBalance\n            };\n        default:\n            return state;\n    }\n};\nconst TradingContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// SIMPLIFIED LOGIC - NO COMPLEX COOLDOWNS\nconst TradingProvider = (param)=>{\n    let { children } = param;\n    _s();\n    // Initialize state with localStorage data if available\n    const initializeState = ()=>{\n        // Check if this is a new session from URL parameter\n        if (true) {\n            const urlParams = new URLSearchParams(window.location.search);\n            const isNewSession = urlParams.get('newSession') === 'true';\n            if (isNewSession) {\n                console.log('🆕 New session requested - starting with completely fresh state');\n                // Note: URL parameter will be cleared in useEffect to avoid render-time state updates\n                return initialTradingState;\n            }\n        }\n        // Check if this is a new window/tab by checking if we have a current session\n        const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n        const currentSessionId = sessionManager.getCurrentSessionId();\n        // If no current session exists for this window, start fresh\n        if (!currentSessionId) {\n            console.log('🆕 New window detected - starting with fresh state');\n            return initialTradingState;\n        }\n        // If we have a session, try to load the session data first (priority over localStorage)\n        const currentSession = sessionManager.loadSession(currentSessionId);\n        if (currentSession) {\n            console.log('🔄 Loading session data with balances:', {\n                crypto1: currentSession.crypto1Balance,\n                crypto2: currentSession.crypto2Balance,\n                stablecoin: currentSession.stablecoinBalance\n            });\n            return {\n                ...initialTradingState,\n                config: currentSession.config,\n                targetPriceRows: currentSession.targetPriceRows,\n                orderHistory: currentSession.orderHistory,\n                currentMarketPrice: currentSession.currentMarketPrice,\n                crypto1Balance: currentSession.crypto1Balance,\n                crypto2Balance: currentSession.crypto2Balance,\n                stablecoinBalance: currentSession.stablecoinBalance,\n                botSystemStatus: currentSession.isActive ? 'Running' : 'Stopped'\n            };\n        }\n        // Fallback to localStorage if session loading fails\n        const savedState = loadStateFromLocalStorage();\n        if (savedState) {\n            return {\n                ...initialTradingState,\n                ...savedState\n            };\n        }\n        return initialTradingState;\n    };\n    const [state, dispatch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useReducer)(tradingReducer, initializeState());\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__.useToast)();\n    // Clear URL parameter after component mounts to avoid render-time state updates\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            if (true) {\n                const urlParams = new URLSearchParams(window.location.search);\n                const isNewSession = urlParams.get('newSession') === 'true';\n                if (isNewSession) {\n                    // Clear the URL parameter to avoid confusion\n                    const newUrl = window.location.pathname;\n                    window.history.replaceState({}, '', newUrl);\n                }\n            }\n        }\n    }[\"TradingProvider.useEffect\"], []); // Run only once on mount\n    const audioRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Removed processing locks and cooldowns for continuous trading\n    // Initialize fetchMarketPrice first\n    const fetchMarketPrice = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[fetchMarketPrice]\": async ()=>{\n            try {\n                // Don't fetch price if either crypto is not selected\n                if (!state.config.crypto1 || !state.config.crypto2) {\n                    dispatch({\n                        type: 'SET_MARKET_PRICE',\n                        payload: 0\n                    });\n                    return;\n                }\n                let price;\n                // For StablecoinSwap mode, use Binance USD prices for accurate market calculation\n                if (state.config.tradingMode === \"StablecoinSwap\") {\n                    try {\n                        const crypto1USDPrice = await getRealTimeUSDPrice(state.config.crypto1);\n                        const crypto2USDPrice = await getRealTimeUSDPrice(state.config.crypto2);\n                        price = crypto1USDPrice / crypto2USDPrice;\n                        console.log(\"\\uD83D\\uDCCA StablecoinSwap Market Price (Binance):\\n            - \".concat(state.config.crypto1, \" USD Price: $\").concat(crypto1USDPrice.toLocaleString(), \"\\n            - \").concat(state.config.crypto2, \" USD Price: $\").concat(crypto2USDPrice.toLocaleString(), \"\\n            - Market Price (\").concat(state.config.crypto1, \"/\").concat(state.config.crypto2, \"): \").concat(price.toFixed(6)));\n                    } catch (error) {\n                        // Fallback to cached/static prices\n                        const crypto1USDPrice = getUSDPrice(state.config.crypto1);\n                        const crypto2USDPrice = getUSDPrice(state.config.crypto2);\n                        price = crypto1USDPrice / crypto2USDPrice;\n                        console.log(\"\\uD83D\\uDCCA StablecoinSwap Market Price (Fallback):\\n            - \".concat(state.config.crypto1, \" USD Price: $\").concat(crypto1USDPrice.toLocaleString(), \"\\n            - \").concat(state.config.crypto2, \" USD Price: $\").concat(crypto2USDPrice.toLocaleString(), \"\\n            - Market Price (\").concat(state.config.crypto1, \"/\").concat(state.config.crypto2, \"): \").concat(price.toFixed(6)));\n                    }\n                } else {\n                    // For SimpleSpot mode, use direct Binance API\n                    price = await getMarketPriceFromBinance(state.config);\n                }\n                dispatch({\n                    type: 'SET_MARKET_PRICE',\n                    payload: price\n                });\n            } catch (error) {\n                console.error('Failed to fetch market price:', error);\n                sendAPIErrorNotification('Price API', \"Failed to fetch market price: \".concat(error)).catch(console.error);\n            }\n        }\n    }[\"TradingProvider.useCallback[fetchMarketPrice]\"], [\n        state.config,\n        dispatch\n    ]);\n    // Market price real-time updates from Binance\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            // Initial fetch\n            fetchMarketPrice();\n            // Set up real-time price updates every 2 seconds from Binance\n            const priceUpdateInterval = setInterval({\n                \"TradingProvider.useEffect.priceUpdateInterval\": async ()=>{\n                    const networkMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.NetworkMonitor.getInstance();\n                    if (networkMonitor.getStatus().isOnline) {\n                        try {\n                            await fetchMarketPrice();\n                            console.log('📊 Binance price updated');\n                        } catch (error) {\n                            console.warn('⚠️ Failed to update price from Binance:', error);\n                        }\n                    }\n                }\n            }[\"TradingProvider.useEffect.priceUpdateInterval\"], 2000); // Update every 2 seconds as requested\n            return ({\n                \"TradingProvider.useEffect\": ()=>{\n                    clearInterval(priceUpdateInterval);\n                }\n            })[\"TradingProvider.useEffect\"];\n        }\n    }[\"TradingProvider.useEffect\"], [\n        fetchMarketPrice,\n        state.config.crypto1,\n        state.config.crypto2,\n        state.config.tradingMode\n    ]);\n    // Audio initialization and user interaction detection\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            if (true) {\n                audioRef.current = new Audio();\n                // Add user interaction listener to enable audio\n                const enableAudio = {\n                    \"TradingProvider.useEffect.enableAudio\": ()=>{\n                        if (audioRef.current) {\n                            // Try to play a silent audio to enable audio context\n                            audioRef.current.volume = 0;\n                            audioRef.current.play().then({\n                                \"TradingProvider.useEffect.enableAudio\": ()=>{\n                                    if (audioRef.current) {\n                                        audioRef.current.volume = 1;\n                                    }\n                                    console.log('🔊 Audio context enabled after user interaction');\n                                }\n                            }[\"TradingProvider.useEffect.enableAudio\"]).catch({\n                                \"TradingProvider.useEffect.enableAudio\": ()=>{\n                                // Still blocked, but we tried\n                                }\n                            }[\"TradingProvider.useEffect.enableAudio\"]);\n                        }\n                        // Remove the listener after first interaction\n                        document.removeEventListener('click', enableAudio);\n                        document.removeEventListener('keydown', enableAudio);\n                    }\n                }[\"TradingProvider.useEffect.enableAudio\"];\n                // Listen for first user interaction\n                document.addEventListener('click', enableAudio);\n                document.addEventListener('keydown', enableAudio);\n                return ({\n                    \"TradingProvider.useEffect\": ()=>{\n                        document.removeEventListener('click', enableAudio);\n                        document.removeEventListener('keydown', enableAudio);\n                    }\n                })[\"TradingProvider.useEffect\"];\n            }\n        }\n    }[\"TradingProvider.useEffect\"], []);\n    const playSound = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[playSound]\": (soundKey)=>{\n            // Get current session's alarm settings, fallback to global app settings\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            const currentSessionId = sessionManager.getCurrentSessionId();\n            let alarmSettings = state.appSettings; // Default fallback\n            if (currentSessionId) {\n                const currentSession = sessionManager.loadSession(currentSessionId);\n                if (currentSession && currentSession.alarmSettings) {\n                    alarmSettings = currentSession.alarmSettings;\n                }\n            }\n            if (alarmSettings.soundAlertsEnabled && audioRef.current) {\n                let soundPath;\n                if (soundKey === 'soundOrderExecution' && alarmSettings.alertOnOrderExecution) {\n                    soundPath = alarmSettings.soundOrderExecution;\n                } else if (soundKey === 'soundError' && alarmSettings.alertOnError) {\n                    soundPath = alarmSettings.soundError;\n                }\n                if (soundPath) {\n                    try {\n                        // Stop any currently playing audio first\n                        audioRef.current.pause();\n                        audioRef.current.currentTime = 0;\n                        // Set the source and load the audio\n                        audioRef.current.src = soundPath;\n                        audioRef.current.load(); // Explicitly load the audio\n                        // Add error handler for audio loading\n                        audioRef.current.onerror = ({\n                            \"TradingProvider.useCallback[playSound]\": (e)=>{\n                                console.warn(\"⚠️ Audio loading failed for \".concat(soundPath, \":\"), e);\n                                // Try fallback sound\n                                if (soundPath !== '/sounds/chime2.wav' && audioRef.current) {\n                                    audioRef.current.src = '/sounds/chime2.wav';\n                                    audioRef.current.load();\n                                }\n                            }\n                        })[\"TradingProvider.useCallback[playSound]\"];\n                        // Play the sound and limit duration to 2 seconds\n                        const playPromise = audioRef.current.play();\n                        if (playPromise !== undefined) {\n                            playPromise.then({\n                                \"TradingProvider.useCallback[playSound]\": ()=>{\n                                    // Set a timeout to pause the audio after 2 seconds\n                                    setTimeout({\n                                        \"TradingProvider.useCallback[playSound]\": ()=>{\n                                            if (audioRef.current && !audioRef.current.paused) {\n                                                audioRef.current.pause();\n                                                audioRef.current.currentTime = 0; // Reset for next play\n                                            }\n                                        }\n                                    }[\"TradingProvider.useCallback[playSound]\"], 2000); // 2 seconds\n                                }\n                            }[\"TradingProvider.useCallback[playSound]\"]).catch({\n                                \"TradingProvider.useCallback[playSound]\": (err)=>{\n                                    // Handle different types of audio errors gracefully\n                                    const errorMessage = err.message || String(err);\n                                    if (errorMessage.includes('user didn\\'t interact') || errorMessage.includes('autoplay')) {\n                                        console.log('🔇 Audio autoplay blocked by browser - user interaction required');\n                                    // Could show a notification to user about enabling audio\n                                    } else if (errorMessage.includes('interrupted') || errorMessage.includes('supported source')) {\n                                    // These are expected errors, don't log them\n                                    } else {\n                                        console.warn(\"⚠️ Audio playback failed:\", errorMessage);\n                                    }\n                                }\n                            }[\"TradingProvider.useCallback[playSound]\"]);\n                        }\n                    } catch (error) {\n                        console.warn(\"⚠️ Audio setup failed for \".concat(soundPath, \":\"), error);\n                    }\n                }\n            }\n        }\n    }[\"TradingProvider.useCallback[playSound]\"], [\n        state.appSettings\n    ]);\n    // Enhanced Telegram notification function with error handling\n    const sendTelegramNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[sendTelegramNotification]\": async function(message) {\n            let isError = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n            try {\n                const telegramToken = localStorage.getItem('telegram_bot_token');\n                const telegramChatId = localStorage.getItem('telegram_chat_id');\n                if (!telegramToken || !telegramChatId) {\n                    console.log('Telegram not configured - skipping notification');\n                    return;\n                }\n                const response = await fetch(\"https://api.telegram.org/bot\".concat(telegramToken, \"/sendMessage\"), {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        chat_id: telegramChatId,\n                        text: message,\n                        parse_mode: 'HTML'\n                    })\n                });\n                if (!response.ok) {\n                    console.error('Failed to send Telegram notification:', response.statusText);\n                    // Don't retry error notifications to avoid infinite loops\n                    if (!isError) {\n                        await sendTelegramErrorNotification('Telegram API Error', \"Failed to send notification: \".concat(response.statusText));\n                    }\n                } else {\n                    console.log('✅ Telegram notification sent successfully');\n                }\n            } catch (error) {\n                console.error('Error sending Telegram notification:', error);\n                // Network disconnection detected\n                if (!isError) {\n                    await sendTelegramErrorNotification('Network Disconnection', \"Failed to connect to Telegram API: \".concat(error));\n                }\n            }\n        }\n    }[\"TradingProvider.useCallback[sendTelegramNotification]\"], []);\n    // Specific error notification functions\n    const sendTelegramErrorNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[sendTelegramErrorNotification]\": async (errorType, errorMessage)=>{\n            const timestamp = new Date().toLocaleString();\n            const message = \"⚠️ <b>ERROR ALERT</b>\\n\\n\" + \"\\uD83D\\uDD34 <b>Type:</b> \".concat(errorType, \"\\n\") + \"\\uD83D\\uDCDD <b>Message:</b> \".concat(errorMessage, \"\\n\") + \"⏰ <b>Time:</b> \".concat(timestamp, \"\\n\") + \"\\uD83E\\uDD16 <b>Bot:</b> \".concat(state.config.crypto1, \"/\").concat(state.config.crypto2, \" \").concat(state.config.tradingMode);\n            await sendTelegramNotification(message, true); // Mark as error to prevent recursion\n        }\n    }[\"TradingProvider.useCallback[sendTelegramErrorNotification]\"], [\n        state.config,\n        sendTelegramNotification\n    ]);\n    const sendLowBalanceNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[sendLowBalanceNotification]\": async (crypto, currentBalance, requiredAmount)=>{\n            const timestamp = new Date().toLocaleString();\n            const message = \"\\uD83D\\uDCB0 <b>LOW BALANCE ALERT</b>\\n\\n\" + \"\\uD83E\\uDE99 <b>Currency:</b> \".concat(crypto, \"\\n\") + \"\\uD83D\\uDCCA <b>Current Balance:</b> \".concat(currentBalance.toFixed(6), \" \").concat(crypto, \"\\n\") + \"⚡ <b>Required Amount:</b> \".concat(requiredAmount.toFixed(6), \" \").concat(crypto, \"\\n\") + \"\\uD83D\\uDCC9 <b>Shortage:</b> \".concat((requiredAmount - currentBalance).toFixed(6), \" \").concat(crypto, \"\\n\") + \"⏰ <b>Time:</b> \".concat(timestamp, \"\\n\") + \"\\uD83E\\uDD16 <b>Bot:</b> \".concat(state.config.crypto1, \"/\").concat(state.config.crypto2, \" \").concat(state.config.tradingMode);\n            await sendTelegramNotification(message);\n        }\n    }[\"TradingProvider.useCallback[sendLowBalanceNotification]\"], [\n        state.config,\n        sendTelegramNotification\n    ]);\n    const sendAPIErrorNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[sendAPIErrorNotification]\": async (apiName, errorDetails)=>{\n            const timestamp = new Date().toLocaleString();\n            const message = \"\\uD83D\\uDD0C <b>API ERROR ALERT</b>\\n\\n\" + \"\\uD83C\\uDF10 <b>API:</b> \".concat(apiName, \"\\n\") + \"❌ <b>Error:</b> \".concat(errorDetails, \"\\n\") + \"⏰ <b>Time:</b> \".concat(timestamp, \"\\n\") + \"\\uD83E\\uDD16 <b>Bot:</b> \".concat(state.config.crypto1, \"/\").concat(state.config.crypto2, \" \").concat(state.config.tradingMode);\n            await sendTelegramNotification(message);\n        }\n    }[\"TradingProvider.useCallback[sendAPIErrorNotification]\"], [\n        state.config,\n        sendTelegramNotification\n    ]);\n    // Effect to update market price when trading pair changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n        // When crypto1 or crypto2 (parts of state.config) change,\n        // the fetchMarketPrice useCallback gets a new reference.\n        // The useEffect above (which depends on fetchMarketPrice)\n        // will re-run, clear the old interval, make an initial fetch with the new config,\n        // and set up a new interval.\n        // The reducer for SET_CONFIG also sets an initial market price if crypto1/crypto2 changes.\n        // Thus, no explicit dispatch is needed here.\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.config.crypto1,\n        state.config.crypto2\n    ]); // Dependencies ensure this reacts to pair changes\n    const setTargetPrices = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[setTargetPrices]\": (prices)=>{\n            if (!prices || !Array.isArray(prices)) return;\n            // Sort prices from lowest to highest for proper counter assignment\n            const sortedPrices = [\n                ...prices\n            ].filter({\n                \"TradingProvider.useCallback[setTargetPrices].sortedPrices\": (price)=>!isNaN(price) && price > 0\n            }[\"TradingProvider.useCallback[setTargetPrices].sortedPrices\"]).sort({\n                \"TradingProvider.useCallback[setTargetPrices].sortedPrices\": (a, b)=>a - b\n            }[\"TradingProvider.useCallback[setTargetPrices].sortedPrices\"]);\n            const newRows = sortedPrices.map({\n                \"TradingProvider.useCallback[setTargetPrices].newRows\": (price, index)=>{\n                    const existingRow = state.targetPriceRows.find({\n                        \"TradingProvider.useCallback[setTargetPrices].newRows.existingRow\": (r)=>r.targetPrice === price\n                    }[\"TradingProvider.useCallback[setTargetPrices].newRows.existingRow\"]);\n                    if (existingRow) {\n                        // Update counter for existing row based on sorted position\n                        return {\n                            ...existingRow,\n                            counter: index + 1\n                        };\n                    }\n                    return {\n                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                        counter: index + 1,\n                        status: 'Free',\n                        orderLevel: 0,\n                        valueLevel: state.config.baseBid,\n                        targetPrice: price\n                    };\n                }\n            }[\"TradingProvider.useCallback[setTargetPrices].newRows\"]);\n            dispatch({\n                type: 'SET_TARGET_PRICE_ROWS',\n                payload: newRows\n            });\n        }\n    }[\"TradingProvider.useCallback[setTargetPrices]\"], [\n        state.targetPriceRows,\n        state.config.baseBid,\n        dispatch\n    ]);\n    // Core Trading Logic (Simulated) - CONTINUOUS TRADING VERSION\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            // Check network status first\n            const networkMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.NetworkMonitor.getInstance();\n            const isOnline = networkMonitor.getStatus().isOnline;\n            // Only check essential conditions AND network status\n            if (state.botSystemStatus !== 'Running' || state.targetPriceRows.length === 0 || state.currentMarketPrice <= 0 || !isOnline) {\n                if (!isOnline && state.botSystemStatus === 'Running') {\n                    console.log('🔴 Trading paused - network offline');\n                }\n                return;\n            }\n            // Execute trading logic immediately - no locks, no cooldowns, no delays\n            const { config, currentMarketPrice, targetPriceRows, crypto1Balance, crypto2Balance } = state;\n            const sortedRowsForLogic = [\n                ...targetPriceRows\n            ].sort({\n                \"TradingProvider.useEffect.sortedRowsForLogic\": (a, b)=>a.targetPrice - b.targetPrice\n            }[\"TradingProvider.useEffect.sortedRowsForLogic\"]);\n            // Use mutable variables for balance tracking within this cycle\n            let currentCrypto1Balance = crypto1Balance;\n            let currentCrypto2Balance = crypto2Balance;\n            let actionsTaken = 0;\n            console.log(\"\\uD83D\\uDE80 CONTINUOUS TRADING: Price $\".concat(currentMarketPrice.toFixed(2), \" | Targets: \").concat(sortedRowsForLogic.length, \" | Balance: $\").concat(currentCrypto2Balance, \" \").concat(config.crypto2));\n            // Show which targets are in range\n            const targetsInRange = sortedRowsForLogic.filter({\n                \"TradingProvider.useEffect.targetsInRange\": (row)=>{\n                    const diffPercent = Math.abs(currentMarketPrice - row.targetPrice) / currentMarketPrice * 100;\n                    return diffPercent <= config.slippagePercent;\n                }\n            }[\"TradingProvider.useEffect.targetsInRange\"]);\n            if (targetsInRange.length > 0) {\n                console.log(\"\\uD83C\\uDFAF TARGETS IN RANGE (\\xb1\".concat(config.slippagePercent, \"%):\"), targetsInRange.map({\n                    \"TradingProvider.useEffect\": (row)=>\"Counter \".concat(row.counter, \" (\").concat(row.status, \")\")\n                }[\"TradingProvider.useEffect\"]));\n            }\n            // CONTINUOUS TRADING LOGIC: Process all targets immediately\n            for(let i = 0; i < sortedRowsForLogic.length; i++){\n                const activeRow = sortedRowsForLogic[i];\n                const priceDiffPercent = Math.abs(currentMarketPrice - activeRow.targetPrice) / currentMarketPrice * 100;\n                // STEP 1: Check if TargetRowN is triggered (within slippage range)\n                if (priceDiffPercent <= config.slippagePercent) {\n                    if (config.tradingMode === \"SimpleSpot\") {\n                        // STEP 2: Evaluate Triggered TargetRowN's Status\n                        if (activeRow.status === \"Free\") {\n                            // STEP 2a: Execute BUY on TargetRowN\n                            const costCrypto2 = activeRow.valueLevel;\n                            if (currentCrypto2Balance >= costCrypto2) {\n                                const amountCrypto1Bought = costCrypto2 / currentMarketPrice;\n                                const updatedRow = {\n                                    ...activeRow,\n                                    status: \"Full\",\n                                    orderLevel: activeRow.orderLevel + 1,\n                                    valueLevel: config.baseBid * Math.pow(config.multiplier, activeRow.orderLevel + 1),\n                                    crypto1AmountHeld: amountCrypto1Bought,\n                                    originalCostCrypto2: costCrypto2,\n                                    crypto1Var: amountCrypto1Bought,\n                                    crypto2Var: -costCrypto2\n                                };\n                                dispatch({\n                                    type: 'UPDATE_TARGET_PRICE_ROW',\n                                    payload: updatedRow\n                                });\n                                dispatch({\n                                    type: 'UPDATE_BALANCES',\n                                    payload: {\n                                        crypto1: currentCrypto1Balance + amountCrypto1Bought,\n                                        crypto2: currentCrypto2Balance - costCrypto2\n                                    }\n                                });\n                                dispatch({\n                                    type: 'ADD_ORDER_HISTORY_ENTRY',\n                                    payload: {\n                                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                        timestamp: Date.now(),\n                                        pair: \"\".concat(config.crypto1, \"/\").concat(config.crypto2),\n                                        crypto1: config.crypto1,\n                                        orderType: \"BUY\",\n                                        amountCrypto1: amountCrypto1Bought,\n                                        avgPrice: currentMarketPrice,\n                                        valueCrypto2: costCrypto2,\n                                        price1: currentMarketPrice,\n                                        crypto1Symbol: config.crypto1 || '',\n                                        crypto2Symbol: config.crypto2 || ''\n                                    }\n                                });\n                                console.log(\"✅ BUY: Counter \".concat(activeRow.counter, \" bought \").concat(amountCrypto1Bought.toFixed(6), \" \").concat(config.crypto1, \" at $\").concat(currentMarketPrice.toFixed(2)));\n                                toast({\n                                    title: \"BUY Executed\",\n                                    description: \"Counter \".concat(activeRow.counter, \": \").concat(amountCrypto1Bought.toFixed(6), \" \").concat(config.crypto1),\n                                    duration: 2000\n                                });\n                                playSound('soundOrderExecution');\n                                // Send Telegram notification for BUY\n                                sendTelegramNotification(\"\\uD83D\\uDFE2 <b>BUY EXECUTED</b>\\n\" + \"\\uD83D\\uDCCA Counter: \".concat(activeRow.counter, \"\\n\") + \"\\uD83D\\uDCB0 Amount: \".concat(amountCrypto1Bought.toFixed(6), \" \").concat(config.crypto1, \"\\n\") + \"\\uD83D\\uDCB5 Price: $\".concat(currentMarketPrice.toFixed(2), \"\\n\") + \"\\uD83D\\uDCB8 Cost: $\".concat(costCrypto2.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\\uD83D\\uDCC8 Mode: Simple Spot\");\n                                actionsTaken++;\n                                // Update local balance for subsequent checks in this cycle\n                                currentCrypto2Balance -= costCrypto2;\n                                currentCrypto1Balance += amountCrypto1Bought;\n                            } else {\n                                // Insufficient balance - send Telegram notification\n                                console.log(\"❌ Insufficient \".concat(config.crypto2, \" balance: \").concat(currentCrypto2Balance.toFixed(6), \" < \").concat(costCrypto2.toFixed(6)));\n                                sendLowBalanceNotification(config.crypto2, currentCrypto2Balance, costCrypto2).catch(console.error);\n                            }\n                        }\n                        // STEP 3: Check and Process Inferior TargetRow (TargetRowN_minus_1) - ALWAYS, regardless of BUY\n                        const currentCounter = activeRow.counter;\n                        const inferiorRow = sortedRowsForLogic.find({\n                            \"TradingProvider.useEffect.inferiorRow\": (row)=>row.counter === currentCounter - 1\n                        }[\"TradingProvider.useEffect.inferiorRow\"]);\n                        if (inferiorRow && inferiorRow.status === \"Full\" && inferiorRow.crypto1AmountHeld && inferiorRow.originalCostCrypto2) {\n                            const amountCrypto1ToSell = inferiorRow.crypto1AmountHeld;\n                            const crypto2Received = amountCrypto1ToSell * currentMarketPrice;\n                            const realizedProfit = crypto2Received - inferiorRow.originalCostCrypto2;\n                            // Calculate Crypto1 profit/loss based on income split percentage\n                            const realizedProfitCrypto1 = currentMarketPrice > 0 ? realizedProfit * config.incomeSplitCrypto1Percent / 100 / currentMarketPrice : 0;\n                            const updatedInferiorRow = {\n                                ...inferiorRow,\n                                status: \"Free\",\n                                crypto1AmountHeld: undefined,\n                                originalCostCrypto2: undefined,\n                                valueLevel: config.baseBid * Math.pow(config.multiplier, inferiorRow.orderLevel),\n                                crypto1Var: -amountCrypto1ToSell,\n                                crypto2Var: crypto2Received\n                            };\n                            dispatch({\n                                type: 'UPDATE_TARGET_PRICE_ROW',\n                                payload: updatedInferiorRow\n                            });\n                            dispatch({\n                                type: 'UPDATE_BALANCES',\n                                payload: {\n                                    crypto1: currentCrypto1Balance - amountCrypto1ToSell,\n                                    crypto2: currentCrypto2Balance + crypto2Received\n                                }\n                            });\n                            dispatch({\n                                type: 'ADD_ORDER_HISTORY_ENTRY',\n                                payload: {\n                                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                    timestamp: Date.now(),\n                                    pair: \"\".concat(config.crypto1, \"/\").concat(config.crypto2),\n                                    crypto1: config.crypto1,\n                                    orderType: \"SELL\",\n                                    amountCrypto1: amountCrypto1ToSell,\n                                    avgPrice: currentMarketPrice,\n                                    valueCrypto2: crypto2Received,\n                                    price1: currentMarketPrice,\n                                    crypto1Symbol: config.crypto1 || '',\n                                    crypto2Symbol: config.crypto2 || '',\n                                    realizedProfitLossCrypto2: realizedProfit,\n                                    realizedProfitLossCrypto1: realizedProfitCrypto1\n                                }\n                            });\n                            console.log(\"✅ SELL: Counter \".concat(currentCounter - 1, \" sold \").concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \". Profit: $\").concat(realizedProfit.toFixed(2)));\n                            toast({\n                                title: \"SELL Executed\",\n                                description: \"Counter \".concat(currentCounter - 1, \": Profit $\").concat(realizedProfit.toFixed(2)),\n                                duration: 2000\n                            });\n                            playSound('soundOrderExecution');\n                            // Send Telegram notification for SELL\n                            const profitEmoji = realizedProfit > 0 ? '📈' : realizedProfit < 0 ? '📉' : '➖';\n                            sendTelegramNotification(\"\\uD83D\\uDD34 <b>SELL EXECUTED</b>\\n\" + \"\\uD83D\\uDCCA Counter: \".concat(currentCounter - 1, \"\\n\") + \"\\uD83D\\uDCB0 Amount: \".concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \"\\n\") + \"\\uD83D\\uDCB5 Price: $\".concat(currentMarketPrice.toFixed(2), \"\\n\") + \"\\uD83D\\uDCB8 Received: $\".concat(crypto2Received.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\".concat(profitEmoji, \" Profit: $\").concat(realizedProfit.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\\uD83D\\uDCC8 Mode: Simple Spot\");\n                            actionsTaken++;\n                            // Update local balance for subsequent checks in this cycle\n                            currentCrypto1Balance -= amountCrypto1ToSell;\n                            currentCrypto2Balance += crypto2Received;\n                        }\n                    } else if (config.tradingMode === \"StablecoinSwap\") {\n                        // STEP 2: Evaluate Triggered TargetRowN's Status (Stablecoin Swap Mode)\n                        if (activeRow.status === \"Free\") {\n                            // STEP 2a: Execute Two-Step \"Buy Crypto1 via Stablecoin\" on TargetRowN\n                            const amountCrypto2ToUse = activeRow.valueLevel; // Value = BaseBid * (Multiplier ^ Level)\n                            if (currentCrypto2Balance >= amountCrypto2ToUse) {\n                                // Step 1: Sell Crypto2 for PreferredStablecoin\n                                // Use current fluctuating market price for more realistic P/L\n                                const crypto2USDPrice = getUSDPrice(config.crypto2 || 'USDT');\n                                const stablecoinUSDPrice = getUSDPrice(config.preferredStablecoin || 'USDT');\n                                const crypto2StablecoinPrice = crypto2USDPrice / stablecoinUSDPrice;\n                                const stablecoinObtained = amountCrypto2ToUse * crypto2StablecoinPrice;\n                                // Step 2: Buy Crypto1 with Stablecoin\n                                // Use current fluctuating market price (this is the key for P/L calculation)\n                                const crypto1USDPrice = getUSDPrice(config.crypto1 || 'BTC');\n                                // Apply current market price fluctuation to Crypto1 price\n                                const fluctuatedCrypto1Price = crypto1USDPrice * (state.currentMarketPrice / (crypto1USDPrice / crypto2USDPrice));\n                                const crypto1StablecoinPrice = fluctuatedCrypto1Price / stablecoinUSDPrice;\n                                const crypto1Bought = stablecoinObtained / crypto1StablecoinPrice;\n                                // Update Row N: Free → Full, Level++, Value recalculated\n                                const newLevel = activeRow.orderLevel + 1;\n                                const newValue = config.baseBid * Math.pow(config.multiplier, newLevel);\n                                const updatedRow = {\n                                    ...activeRow,\n                                    status: \"Full\",\n                                    orderLevel: newLevel,\n                                    valueLevel: newValue,\n                                    crypto1AmountHeld: crypto1Bought,\n                                    originalCostCrypto2: amountCrypto2ToUse,\n                                    crypto1Var: crypto1Bought,\n                                    crypto2Var: -amountCrypto2ToUse\n                                };\n                                console.log(\"\\uD83D\\uDCB0 StablecoinSwap BUY - Setting originalCostCrypto2:\", {\n                                    counter: activeRow.counter,\n                                    amountCrypto2ToUse: amountCrypto2ToUse,\n                                    crypto1Bought: crypto1Bought,\n                                    originalCostCrypto2: amountCrypto2ToUse,\n                                    newLevel: newLevel,\n                                    crypto1StablecoinPrice: crypto1StablecoinPrice,\n                                    fluctuatedCrypto1Price: fluctuatedCrypto1Price,\n                                    currentMarketPrice: state.currentMarketPrice\n                                });\n                                dispatch({\n                                    type: 'UPDATE_TARGET_PRICE_ROW',\n                                    payload: updatedRow\n                                });\n                                dispatch({\n                                    type: 'UPDATE_BALANCES',\n                                    payload: {\n                                        crypto1: currentCrypto1Balance + crypto1Bought,\n                                        crypto2: currentCrypto2Balance - amountCrypto2ToUse\n                                    }\n                                });\n                                // Add history entries for both steps of the stablecoin swap\n                                // Calculate P/L for the crypto2 sell based on price fluctuation\n                                const baseCrypto2Price = getUSDPrice(config.crypto2) / getUSDPrice(config.preferredStablecoin);\n                                const estimatedProfitCrypto2 = amountCrypto2ToUse * (crypto2StablecoinPrice - baseCrypto2Price);\n                                const estimatedProfitCrypto1 = estimatedProfitCrypto2 / crypto1StablecoinPrice;\n                                console.log(\"\\uD83D\\uDCCA StablecoinSwap Step A SELL P/L:\", {\n                                    crypto2: config.crypto2,\n                                    amountSold: amountCrypto2ToUse,\n                                    currentPrice: crypto2StablecoinPrice,\n                                    basePrice: baseCrypto2Price,\n                                    estimatedProfitCrypto2: estimatedProfitCrypto2,\n                                    estimatedProfitCrypto1: estimatedProfitCrypto1\n                                });\n                                dispatch({\n                                    type: 'ADD_ORDER_HISTORY_ENTRY',\n                                    payload: {\n                                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                        timestamp: Date.now(),\n                                        pair: \"\".concat(config.crypto2, \"/\").concat(config.preferredStablecoin),\n                                        crypto1: config.crypto2,\n                                        orderType: \"SELL\",\n                                        amountCrypto1: amountCrypto2ToUse,\n                                        avgPrice: crypto2StablecoinPrice,\n                                        valueCrypto2: stablecoinObtained,\n                                        price1: crypto2StablecoinPrice,\n                                        crypto1Symbol: config.crypto2 || '',\n                                        crypto2Symbol: config.preferredStablecoin || '',\n                                        realizedProfitLossCrypto2: estimatedProfitCrypto2,\n                                        realizedProfitLossCrypto1: estimatedProfitCrypto1\n                                    }\n                                });\n                                dispatch({\n                                    type: 'ADD_ORDER_HISTORY_ENTRY',\n                                    payload: {\n                                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                        timestamp: Date.now(),\n                                        pair: \"\".concat(config.crypto1, \"/\").concat(config.preferredStablecoin),\n                                        crypto1: config.crypto1,\n                                        orderType: \"BUY\",\n                                        amountCrypto1: crypto1Bought,\n                                        avgPrice: crypto1StablecoinPrice,\n                                        valueCrypto2: stablecoinObtained,\n                                        price1: crypto1StablecoinPrice,\n                                        crypto1Symbol: config.crypto1 || '',\n                                        crypto2Symbol: config.preferredStablecoin || ''\n                                    }\n                                });\n                                console.log(\"✅ STABLECOIN BUY: Counter \".concat(activeRow.counter, \" | Step 1: Sold \").concat(amountCrypto2ToUse, \" \").concat(config.crypto2, \" → \").concat(stablecoinObtained.toFixed(2), \" \").concat(config.preferredStablecoin, \" | Step 2: Bought \").concat(crypto1Bought.toFixed(6), \" \").concat(config.crypto1, \" | Level: \").concat(activeRow.orderLevel, \" → \").concat(newLevel));\n                                toast({\n                                    title: \"BUY Executed (Stablecoin)\",\n                                    description: \"Counter \".concat(activeRow.counter, \": \").concat(crypto1Bought.toFixed(6), \" \").concat(config.crypto1, \" via \").concat(config.preferredStablecoin),\n                                    duration: 2000\n                                });\n                                playSound('soundOrderExecution');\n                                // Send Telegram notification for Stablecoin BUY\n                                sendTelegramNotification(\"\\uD83D\\uDFE2 <b>BUY EXECUTED (Stablecoin Swap)</b>\\n\" + \"\\uD83D\\uDCCA Counter: \".concat(activeRow.counter, \"\\n\") + \"\\uD83D\\uDD04 Step 1: Sold \".concat(amountCrypto2ToUse.toFixed(2), \" \").concat(config.crypto2, \" → \").concat(stablecoinObtained.toFixed(2), \" \").concat(config.preferredStablecoin, \"\\n\") + \"\\uD83D\\uDD04 Step 2: Bought \".concat(crypto1Bought.toFixed(6), \" \").concat(config.crypto1, \"\\n\") + \"\\uD83D\\uDCCA Level: \".concat(activeRow.orderLevel, \" → \").concat(newLevel, \"\\n\") + \"\\uD83D\\uDCC8 Mode: Stablecoin Swap\");\n                                actionsTaken++;\n                                // Update local balance for subsequent checks in this cycle\n                                currentCrypto2Balance -= amountCrypto2ToUse;\n                                currentCrypto1Balance += crypto1Bought;\n                            } else {\n                                // Insufficient balance - send Telegram notification\n                                console.log(\"❌ Insufficient \".concat(config.crypto2, \" balance for stablecoin swap: \").concat(currentCrypto2Balance.toFixed(6), \" < \").concat(amountCrypto2ToUse.toFixed(6)));\n                                sendLowBalanceNotification(config.crypto2, currentCrypto2Balance, amountCrypto2ToUse).catch(console.error);\n                            }\n                        }\n                        // STEP 3: Check and Process Inferior TargetRow (TargetRowN_minus_1) - ALWAYS, regardless of BUY\n                        const currentCounter = activeRow.counter;\n                        const inferiorRow = sortedRowsForLogic.find({\n                            \"TradingProvider.useEffect.inferiorRow\": (row)=>row.counter === currentCounter - 1\n                        }[\"TradingProvider.useEffect.inferiorRow\"]);\n                        console.log(\"\\uD83D\\uDD0D StablecoinSwap SELL Check:\", {\n                            currentCounter: currentCounter,\n                            inferiorRowFound: !!inferiorRow,\n                            inferiorRowStatus: inferiorRow === null || inferiorRow === void 0 ? void 0 : inferiorRow.status,\n                            inferiorRowCrypto1Held: inferiorRow === null || inferiorRow === void 0 ? void 0 : inferiorRow.crypto1AmountHeld,\n                            inferiorRowOriginalCost: inferiorRow === null || inferiorRow === void 0 ? void 0 : inferiorRow.originalCostCrypto2,\n                            canExecuteSell: !!(inferiorRow && inferiorRow.status === \"Full\" && inferiorRow.crypto1AmountHeld && inferiorRow.originalCostCrypto2)\n                        });\n                        if (inferiorRow && inferiorRow.status === \"Full\" && inferiorRow.crypto1AmountHeld && inferiorRow.originalCostCrypto2) {\n                            // Execute Two-Step \"Sell Crypto1 & Reacquire Crypto2 via Stablecoin\" for TargetRowN_minus_1\n                            const amountCrypto1ToSell = inferiorRow.crypto1AmountHeld;\n                            // Step A: Sell Crypto1 for PreferredStablecoin\n                            // Use current fluctuating market price for realistic P/L\n                            const crypto1USDPrice = getUSDPrice(config.crypto1 || 'BTC');\n                            const crypto2USDPrice = getUSDPrice(config.crypto2 || 'USDT');\n                            const stablecoinUSDPrice = getUSDPrice(config.preferredStablecoin || 'USDT');\n                            // Apply current market price fluctuation to Crypto1 price\n                            const fluctuatedCrypto1Price = crypto1USDPrice * (state.currentMarketPrice / (crypto1USDPrice / crypto2USDPrice));\n                            const crypto1StablecoinPrice = fluctuatedCrypto1Price / stablecoinUSDPrice;\n                            const stablecoinFromC1Sell = amountCrypto1ToSell * crypto1StablecoinPrice;\n                            // Step B: Buy Crypto2 with Stablecoin\n                            const crypto2StablecoinPrice = crypto2USDPrice / stablecoinUSDPrice;\n                            const crypto2Reacquired = stablecoinFromC1Sell / crypto2StablecoinPrice;\n                            // Calculate realized profit in Crypto2\n                            // We compare the value of crypto2 we got back vs what we originally spent\n                            const originalCost = inferiorRow.originalCostCrypto2 || 0;\n                            const realizedProfitInCrypto2 = crypto2Reacquired - originalCost;\n                            // Calculate Crypto1 profit/loss - this should be the profit in terms of Crypto1\n                            // For StablecoinSwap, we need to calculate how much Crypto1 profit this represents\n                            const realizedProfitCrypto1 = crypto1StablecoinPrice > 0 ? realizedProfitInCrypto2 / crypto1StablecoinPrice : 0;\n                            // IMPORTANT: If P/L is exactly 0, it might indicate a price calculation issue\n                            if (realizedProfitInCrypto2 === 0) {\n                                console.warn(\"⚠️ P/L is exactly 0 - this might indicate an issue:\\n                - Are prices changing between BUY and SELL?\\n                - Is the market price fluctuation working?\\n                - Current market price: \".concat(state.currentMarketPrice));\n                            }\n                            console.log(\"\\uD83D\\uDCB0 StablecoinSwap P/L Calculation DETAILED:\\n              - Inferior Row Counter: \".concat(inferiorRow.counter, \"\\n              - Original Cost (Crypto2): \").concat(originalCost, \"\\n              - Crypto2 Reacquired: \").concat(crypto2Reacquired, \"\\n              - Realized P/L (Crypto2): \").concat(realizedProfitInCrypto2, \"\\n              - Crypto1 Stablecoin Price: \").concat(crypto1StablecoinPrice, \"\\n              - Realized P/L (Crypto1): \").concat(realizedProfitCrypto1, \"\\n              - Is Profitable: \").concat(realizedProfitInCrypto2 > 0 ? 'YES' : 'NO', \"\\n              - Calculation: \").concat(crypto2Reacquired, \" - \").concat(originalCost, \" = \").concat(realizedProfitInCrypto2));\n                            // Update Row N-1: Full → Free, Level UNCHANGED, Vars cleared\n                            const updatedInferiorRow = {\n                                ...inferiorRow,\n                                status: \"Free\",\n                                // orderLevel: REMAINS UNCHANGED per specification\n                                crypto1AmountHeld: undefined,\n                                originalCostCrypto2: undefined,\n                                valueLevel: config.baseBid * Math.pow(config.multiplier, inferiorRow.orderLevel),\n                                crypto1Var: 0,\n                                crypto2Var: 0\n                            };\n                            dispatch({\n                                type: 'UPDATE_TARGET_PRICE_ROW',\n                                payload: updatedInferiorRow\n                            });\n                            dispatch({\n                                type: 'UPDATE_BALANCES',\n                                payload: {\n                                    crypto1: currentCrypto1Balance - amountCrypto1ToSell,\n                                    crypto2: currentCrypto2Balance + crypto2Reacquired\n                                }\n                            });\n                            // Add history entries for both steps of the N-1 stablecoin swap\n                            // Step A: SELL Crypto1 for Stablecoin (with profit/loss data for analytics)\n                            console.log(\"\\uD83D\\uDCCA Adding StablecoinSwap SELL order to history with P/L:\", {\n                                realizedProfitLossCrypto2: realizedProfitInCrypto2,\n                                realizedProfitLossCrypto1: realizedProfitCrypto1,\n                                pair: \"\".concat(config.crypto1, \"/\").concat(config.preferredStablecoin),\n                                orderType: \"SELL\",\n                                amountCrypto1: amountCrypto1ToSell,\n                                avgPrice: crypto1StablecoinPrice,\n                                valueCrypto2: stablecoinFromC1Sell,\n                                price1: crypto1StablecoinPrice,\n                                crypto1Symbol: config.crypto1,\n                                crypto2Symbol: config.preferredStablecoin\n                            });\n                            dispatch({\n                                type: 'ADD_ORDER_HISTORY_ENTRY',\n                                payload: {\n                                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                    timestamp: Date.now(),\n                                    pair: \"\".concat(config.crypto1, \"/\").concat(config.preferredStablecoin),\n                                    crypto1: config.crypto1,\n                                    orderType: \"SELL\",\n                                    amountCrypto1: amountCrypto1ToSell,\n                                    avgPrice: crypto1StablecoinPrice,\n                                    valueCrypto2: stablecoinFromC1Sell,\n                                    price1: crypto1StablecoinPrice,\n                                    crypto1Symbol: config.crypto1 || '',\n                                    crypto2Symbol: config.preferredStablecoin || '',\n                                    realizedProfitLossCrypto2: realizedProfitInCrypto2,\n                                    realizedProfitLossCrypto1: realizedProfitCrypto1\n                                }\n                            });\n                            // Step B: BUY Crypto2 with Stablecoin (no profit/loss as it's just conversion)\n                            dispatch({\n                                type: 'ADD_ORDER_HISTORY_ENTRY',\n                                payload: {\n                                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                    timestamp: Date.now(),\n                                    pair: \"\".concat(config.crypto2, \"/\").concat(config.preferredStablecoin),\n                                    crypto1: config.crypto2,\n                                    orderType: \"BUY\",\n                                    amountCrypto1: crypto2Reacquired,\n                                    avgPrice: crypto2StablecoinPrice,\n                                    valueCrypto2: stablecoinFromC1Sell,\n                                    price1: crypto2StablecoinPrice,\n                                    crypto1Symbol: config.crypto2 || '',\n                                    crypto2Symbol: config.preferredStablecoin || ''\n                                }\n                            });\n                            console.log(\"✅ STABLECOIN SELL: Counter \".concat(currentCounter - 1, \" | Step A: Sold \").concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \" → \").concat(stablecoinFromC1Sell.toFixed(2), \" \").concat(config.preferredStablecoin, \" | Step B: Bought \").concat(crypto2Reacquired.toFixed(2), \" \").concat(config.crypto2, \" | Profit: \").concat(realizedProfitInCrypto2.toFixed(2), \" \").concat(config.crypto2, \" | Level: \").concat(inferiorRow.orderLevel, \" (unchanged)\"));\n                            toast({\n                                title: \"SELL Executed (Stablecoin)\",\n                                description: \"Counter \".concat(currentCounter - 1, \": Profit \").concat(realizedProfitInCrypto2.toFixed(2), \" \").concat(config.crypto2, \" via \").concat(config.preferredStablecoin),\n                                duration: 2000\n                            });\n                            playSound('soundOrderExecution');\n                            // Send Telegram notification for Stablecoin SELL\n                            const profitEmoji = realizedProfitInCrypto2 > 0 ? '📈' : realizedProfitInCrypto2 < 0 ? '📉' : '➖';\n                            sendTelegramNotification(\"\\uD83D\\uDD34 <b>SELL EXECUTED (Stablecoin Swap)</b>\\n\" + \"\\uD83D\\uDCCA Counter: \".concat(currentCounter - 1, \"\\n\") + \"\\uD83D\\uDD04 Step A: Sold \".concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \" → \").concat(stablecoinFromC1Sell.toFixed(2), \" \").concat(config.preferredStablecoin, \"\\n\") + \"\\uD83D\\uDD04 Step B: Bought \".concat(crypto2Reacquired.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\".concat(profitEmoji, \" Profit: \").concat(realizedProfitInCrypto2.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\\uD83D\\uDCCA Level: \".concat(inferiorRow.orderLevel, \" (unchanged)\\n\") + \"\\uD83D\\uDCC8 Mode: Stablecoin Swap\");\n                            actionsTaken++;\n                            // Update local balance for subsequent checks in this cycle\n                            currentCrypto1Balance -= amountCrypto1ToSell;\n                            currentCrypto2Balance += crypto2Reacquired;\n                        }\n                    }\n                }\n            }\n            if (actionsTaken > 0) {\n                console.log(\"\\uD83C\\uDFAF CYCLE COMPLETE: \".concat(actionsTaken, \" actions taken at price $\").concat(currentMarketPrice.toFixed(2)));\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus,\n        state.currentMarketPrice,\n        state.targetPriceRows,\n        state.config,\n        state.crypto1Balance,\n        state.crypto2Balance,\n        state.stablecoinBalance,\n        dispatch,\n        toast,\n        playSound,\n        sendTelegramNotification\n    ]);\n    const getDisplayOrders = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[getDisplayOrders]\": ()=>{\n            if (!state.targetPriceRows || !Array.isArray(state.targetPriceRows)) {\n                return [];\n            }\n            return state.targetPriceRows.map({\n                \"TradingProvider.useCallback[getDisplayOrders]\": (row)=>{\n                    const currentPrice = state.currentMarketPrice || 0;\n                    const targetPrice = row.targetPrice || 0;\n                    const percentFromActualPrice = currentPrice && targetPrice ? (currentPrice / targetPrice - 1) * 100 : 0;\n                    let incomeCrypto1;\n                    let incomeCrypto2;\n                    if (row.status === \"Full\" && row.crypto1AmountHeld && row.originalCostCrypto2) {\n                        const totalUnrealizedProfitInCrypto2 = currentPrice * row.crypto1AmountHeld - row.originalCostCrypto2;\n                        incomeCrypto2 = totalUnrealizedProfitInCrypto2 * state.config.incomeSplitCrypto2Percent / 100;\n                        if (currentPrice > 0) {\n                            incomeCrypto1 = totalUnrealizedProfitInCrypto2 * state.config.incomeSplitCrypto1Percent / 100 / currentPrice;\n                        }\n                    }\n                    return {\n                        ...row,\n                        currentPrice,\n                        priceDifference: targetPrice - currentPrice,\n                        priceDifferencePercent: currentPrice > 0 ? (targetPrice - currentPrice) / currentPrice * 100 : 0,\n                        potentialProfitCrypto1: state.config.incomeSplitCrypto1Percent / 100 * row.valueLevel / (targetPrice || 1),\n                        potentialProfitCrypto2: state.config.incomeSplitCrypto2Percent / 100 * row.valueLevel,\n                        percentFromActualPrice,\n                        incomeCrypto1,\n                        incomeCrypto2\n                    };\n                }\n            }[\"TradingProvider.useCallback[getDisplayOrders]\"]).sort({\n                \"TradingProvider.useCallback[getDisplayOrders]\": (a, b)=>b.targetPrice - a.targetPrice\n            }[\"TradingProvider.useCallback[getDisplayOrders]\"]);\n        }\n    }[\"TradingProvider.useCallback[getDisplayOrders]\"], [\n        state.targetPriceRows,\n        state.currentMarketPrice,\n        state.config.incomeSplitCrypto1Percent,\n        state.config.incomeSplitCrypto2Percent,\n        state.config.baseBid,\n        state.config.multiplier\n    ]);\n    // Backend Integration Functions\n    const saveConfigToBackend = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[saveConfigToBackend]\": async (config)=>{\n            try {\n                var _response_config;\n                const configData = {\n                    name: \"\".concat(config.crypto1, \"/\").concat(config.crypto2, \" \").concat(config.tradingMode),\n                    tradingMode: config.tradingMode,\n                    crypto1: config.crypto1,\n                    crypto2: config.crypto2,\n                    baseBid: config.baseBid,\n                    multiplier: config.multiplier,\n                    numDigits: config.numDigits,\n                    slippagePercent: config.slippagePercent,\n                    incomeSplitCrypto1Percent: config.incomeSplitCrypto1Percent,\n                    incomeSplitCrypto2Percent: config.incomeSplitCrypto2Percent,\n                    preferredStablecoin: config.preferredStablecoin,\n                    targetPrices: state.targetPriceRows.map({\n                        \"TradingProvider.useCallback[saveConfigToBackend]\": (row)=>row.targetPrice\n                    }[\"TradingProvider.useCallback[saveConfigToBackend]\"])\n                };\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.tradingApi.saveConfig(configData);\n                console.log('✅ Config saved to backend:', response);\n                return ((_response_config = response.config) === null || _response_config === void 0 ? void 0 : _response_config.id) || null;\n            } catch (error) {\n                console.error('❌ Failed to save config to backend:', error);\n                toast({\n                    title: \"Backend Error\",\n                    description: \"Failed to save configuration to backend\",\n                    variant: \"destructive\",\n                    duration: 3000\n                });\n                return null;\n            }\n        }\n    }[\"TradingProvider.useCallback[saveConfigToBackend]\"], [\n        state.targetPriceRows,\n        toast\n    ]);\n    const startBackendBot = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[startBackendBot]\": async (configId)=>{\n            try {\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.tradingApi.startBot(configId);\n                console.log('✅ Bot started on backend:', response);\n                toast({\n                    title: \"Bot Started\",\n                    description: \"Trading bot started successfully on backend\",\n                    duration: 3000\n                });\n                return true;\n            } catch (error) {\n                console.error('❌ Failed to start bot on backend:', error);\n                toast({\n                    title: \"Backend Error\",\n                    description: \"Failed to start bot on backend\",\n                    variant: \"destructive\",\n                    duration: 3000\n                });\n                return false;\n            }\n        }\n    }[\"TradingProvider.useCallback[startBackendBot]\"], [\n        toast\n    ]);\n    const stopBackendBot = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[stopBackendBot]\": async (configId)=>{\n            try {\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.tradingApi.stopBot(configId);\n                console.log('✅ Bot stopped on backend:', response);\n                toast({\n                    title: \"Bot Stopped\",\n                    description: \"Trading bot stopped successfully on backend\",\n                    duration: 3000\n                });\n                return true;\n            } catch (error) {\n                console.error('❌ Failed to stop bot on backend:', error);\n                toast({\n                    title: \"Backend Error\",\n                    description: \"Failed to stop bot on backend\",\n                    variant: \"destructive\",\n                    duration: 3000\n                });\n                return false;\n            }\n        }\n    }[\"TradingProvider.useCallback[stopBackendBot]\"], [\n        toast\n    ]);\n    const checkBackendStatus = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[checkBackendStatus]\": async ()=>{\n            const apiUrl = \"http://localhost:5000\";\n            if (!apiUrl) {\n                console.error('Error: NEXT_PUBLIC_API_URL is not defined. Backend connectivity check cannot be performed.');\n                dispatch({\n                    type: 'SET_BACKEND_STATUS',\n                    payload: 'offline'\n                });\n                return;\n            }\n            try {\n                const healthResponse = await fetch(\"\".concat(apiUrl, \"/health/\"));\n                if (!healthResponse.ok) {\n                    // Log more details if the response was received but not OK\n                    console.error(\"Backend health check failed with status: \".concat(healthResponse.status, \" \").concat(healthResponse.statusText));\n                    const responseText = await healthResponse.text().catch({\n                        \"TradingProvider.useCallback[checkBackendStatus]\": ()=>'Could not read response text.'\n                    }[\"TradingProvider.useCallback[checkBackendStatus]\"]);\n                    console.error('Backend health check response body:', responseText);\n                }\n                dispatch({\n                    type: 'SET_BACKEND_STATUS',\n                    payload: healthResponse.ok ? 'online' : 'offline'\n                });\n            } catch (error) {\n                dispatch({\n                    type: 'SET_BACKEND_STATUS',\n                    payload: 'offline'\n                });\n                console.error('Backend connectivity check failed. Error details:', error);\n                if (error.cause) {\n                    console.error('Fetch error cause:', error.cause);\n                }\n                // It's also useful to log the apiUrl to ensure it's what we expect\n                console.error('Attempted to fetch API URL:', \"\".concat(apiUrl, \"/health/\"));\n            }\n        }\n    }[\"TradingProvider.useCallback[checkBackendStatus]\"], [\n        dispatch\n    ]);\n    // Initialize backend status check (one-time only)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            // Initial check for backend status only\n            checkBackendStatus();\n        }\n    }[\"TradingProvider.useEffect\"], [\n        checkBackendStatus\n    ]);\n    // Save state to localStorage whenever it changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            saveStateToLocalStorage(state);\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state\n    ]);\n    // Effect to handle bot warm-up period (immediate execution)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            if (state.botSystemStatus === 'WarmingUp') {\n                console.log(\"Bot is Warming Up... Immediate execution enabled.\");\n                // Immediate transition to Running state - no delays\n                dispatch({\n                    type: 'SYSTEM_COMPLETE_WARMUP'\n                });\n                console.log(\"Bot is now Running immediately.\");\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus,\n        dispatch\n    ]);\n    // Effect to handle session runtime tracking\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            const currentSessionId = sessionManager.getCurrentSessionId();\n            if (currentSessionId) {\n                if (state.botSystemStatus === 'Running') {\n                    // Bot started running, start runtime tracking\n                    sessionManager.startSessionRuntime(currentSessionId);\n                    console.log('✅ Started runtime tracking for session:', currentSessionId);\n                } else if (state.botSystemStatus === 'Stopped') {\n                    // Bot stopped, stop runtime tracking\n                    sessionManager.stopSessionRuntime(currentSessionId);\n                    console.log('⏹️ Stopped runtime tracking for session:', currentSessionId);\n                }\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus\n    ]);\n    // Auto-create session when bot actually starts running (not during warmup)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            // Check if we need to create a session when bot actually starts running\n            if (state.botSystemStatus === 'Running' && !sessionManager.getCurrentSessionId()) {\n                // Only create if we have valid crypto configuration AND target prices set\n                // This prevents auto-creation for fresh windows\n                if (state.config.crypto1 && state.config.crypto2 && state.targetPriceRows.length > 0) {\n                    sessionManager.createNewSessionWithAutoName(state.config).then({\n                        \"TradingProvider.useEffect\": (sessionId)=>{\n                            sessionManager.setCurrentSession(sessionId);\n                            console.log('✅ Auto-created session when bot started:', sessionId);\n                        }\n                    }[\"TradingProvider.useEffect\"]).catch({\n                        \"TradingProvider.useEffect\": (error)=>{\n                            console.error('❌ Failed to auto-create session:', error);\n                        }\n                    }[\"TradingProvider.useEffect\"]);\n                }\n            }\n            // Handle runtime tracking based on bot status\n            const currentSessionId = sessionManager.getCurrentSessionId();\n            if (currentSessionId) {\n                if (state.botSystemStatus === 'Running') {\n                    // Start runtime tracking when bot becomes active\n                    sessionManager.startSessionRuntime(currentSessionId);\n                    console.log('⏱️ Started runtime tracking for session:', currentSessionId);\n                } else if (state.botSystemStatus === 'Stopped') {\n                    // Stop runtime tracking when bot stops\n                    sessionManager.stopSessionRuntime(currentSessionId);\n                    console.log('⏹️ Stopped runtime tracking for session:', currentSessionId);\n                }\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus,\n        state.config.crypto1,\n        state.config.crypto2\n    ]);\n    // Handle crypto pair changes during active trading\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            const currentSessionId = sessionManager.getCurrentSessionId();\n            if (currentSessionId) {\n                const currentSession = sessionManager.loadSession(currentSessionId);\n                // Check if crypto pair has changed from the current session\n                if (currentSession && (currentSession.config.crypto1 !== state.config.crypto1 || currentSession.config.crypto2 !== state.config.crypto2)) {\n                    console.log('🔄 Crypto pair changed during session, auto-saving and resetting...');\n                    // Auto-save current session if bot was running or has data\n                    if (state.botSystemStatus === 'Running' || state.targetPriceRows.length > 0 || state.orderHistory.length > 0) {\n                        // Stop the bot if it's running\n                        if (state.botSystemStatus === 'Running') {\n                            stopBackendBot(currentSessionId);\n                        }\n                        // Save current session with timestamp\n                        const timestamp = new Date().toLocaleString('en-US', {\n                            month: 'short',\n                            day: 'numeric',\n                            hour: '2-digit',\n                            minute: '2-digit',\n                            hour12: false\n                        });\n                        const savedName = \"\".concat(currentSession.name, \" (AutoSaved \").concat(timestamp, \")\");\n                        sessionManager.createNewSession(savedName, currentSession.config).then({\n                            \"TradingProvider.useEffect\": async (savedSessionId)=>{\n                                await sessionManager.saveSession(savedSessionId, currentSession.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, false);\n                                console.log('💾 AutoSaved session:', savedName);\n                            }\n                        }[\"TradingProvider.useEffect\"]);\n                    }\n                    // Reset for new crypto pair\n                    dispatch({\n                        type: 'RESET_FOR_NEW_CRYPTO'\n                    });\n                    // Only create new session for the new crypto pair if bot was actually running\n                    if (state.config.crypto1 && state.config.crypto2 && state.botSystemStatus === 'Running') {\n                        const currentBalances = {\n                            crypto1: state.crypto1Balance,\n                            crypto2: state.crypto2Balance,\n                            stablecoin: state.stablecoinBalance\n                        };\n                        sessionManager.createNewSessionWithAutoName(state.config, undefined, currentBalances).then({\n                            \"TradingProvider.useEffect\": (newSessionId)=>{\n                                sessionManager.setCurrentSession(newSessionId);\n                                console.log('🆕 Created new session for crypto pair:', state.config.crypto1, '/', state.config.crypto2, 'with balances:', currentBalances);\n                                toast({\n                                    title: \"Crypto Pair Changed\",\n                                    description: \"Previous session AutoSaved. New session created for \".concat(state.config.crypto1, \"/\").concat(state.config.crypto2),\n                                    duration: 5000\n                                });\n                            }\n                        }[\"TradingProvider.useEffect\"]);\n                    } else {\n                        // If bot wasn't running, just clear the current session without creating a new one\n                        sessionManager.clearCurrentSession();\n                        console.log('🔄 Crypto pair changed but bot wasn\\'t running - cleared current session');\n                    }\n                }\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.config.crypto1,\n        state.config.crypto2\n    ]);\n    // Initialize network monitoring and auto-save\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const networkMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.NetworkMonitor.getInstance();\n            const autoSaveManager = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.AutoSaveManager.getInstance();\n            const memoryMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.MemoryMonitor.getInstance();\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            // Set up network status listener\n            const unsubscribeNetwork = networkMonitor.addListener({\n                \"TradingProvider.useEffect.unsubscribeNetwork\": (isOnline, isInitial)=>{\n                    console.log(\"\\uD83C\\uDF10 Network status changed: \".concat(isOnline ? 'Online' : 'Offline'));\n                    // Handle offline state - stop bot and save session\n                    if (!isOnline && !isInitial) {\n                        // Stop the bot if it's running\n                        if (state.botSystemStatus === 'Running') {\n                            console.log('🔴 Internet lost - stopping bot and saving session');\n                            dispatch({\n                                type: 'SYSTEM_STOP_BOT'\n                            });\n                            // Auto-save current session\n                            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n                            const currentSessionId = sessionManager.getCurrentSessionId();\n                            if (currentSessionId) {\n                                const currentSession = sessionManager.loadSession(currentSessionId);\n                                if (currentSession) {\n                                    // Create offline backup session\n                                    const timestamp = new Date().toLocaleString('en-US', {\n                                        month: 'short',\n                                        day: 'numeric',\n                                        hour: '2-digit',\n                                        minute: '2-digit',\n                                        hour12: false\n                                    });\n                                    const offlineName = \"\".concat(currentSession.name, \" (Offline Backup \").concat(timestamp, \")\");\n                                    sessionManager.createNewSession(offlineName, currentSession.config).then({\n                                        \"TradingProvider.useEffect.unsubscribeNetwork\": async (backupSessionId)=>{\n                                            await sessionManager.saveSession(backupSessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, false);\n                                            console.log('💾 Created offline backup session:', offlineName);\n                                        }\n                                    }[\"TradingProvider.useEffect.unsubscribeNetwork\"]);\n                                }\n                            }\n                        }\n                        toast({\n                            title: \"Network Disconnected\",\n                            description: \"Bot stopped and session saved. Trading paused until connection restored.\",\n                            variant: \"destructive\",\n                            duration: 8000\n                        });\n                    } else if (isOnline && !isInitial) {\n                        toast({\n                            title: \"Network Reconnected\",\n                            description: \"Connection restored. You can resume trading.\",\n                            duration: 3000\n                        });\n                    }\n                }\n            }[\"TradingProvider.useEffect.unsubscribeNetwork\"]);\n            // Set up memory monitoring\n            const unsubscribeMemory = memoryMonitor.addListener({\n                \"TradingProvider.useEffect.unsubscribeMemory\": (memory)=>{\n                    const usedMB = memory.usedJSHeapSize / 1024 / 1024;\n                    if (usedMB > 150) {\n                        console.warn(\"\\uD83E\\uDDE0 High memory usage: \".concat(usedMB.toFixed(2), \"MB\"));\n                        toast({\n                            title: \"High Memory Usage\",\n                            description: \"Memory usage is high (\".concat(usedMB.toFixed(0), \"MB). Consider refreshing the page.\"),\n                            variant: \"destructive\",\n                            duration: 5000\n                        });\n                    }\n                }\n            }[\"TradingProvider.useEffect.unsubscribeMemory\"]);\n            // Set up auto-save\n            const saveFunction = {\n                \"TradingProvider.useEffect.saveFunction\": async ()=>{\n                    try {\n                        // Save to session manager if we have a current session\n                        const currentSessionId = sessionManager.getCurrentSessionId();\n                        if (currentSessionId) {\n                            await sessionManager.saveSession(currentSessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, state.botSystemStatus === 'Running');\n                        }\n                        // Also save to localStorage as backup\n                        saveStateToLocalStorage(state);\n                    } catch (error) {\n                        console.error('Auto-save failed:', error);\n                    }\n                }\n            }[\"TradingProvider.useEffect.saveFunction\"];\n            autoSaveManager.enable(saveFunction, 30000); // Auto-save every 30 seconds\n            // Add beforeunload listener to save session on browser close/refresh\n            const handleBeforeUnload = {\n                \"TradingProvider.useEffect.handleBeforeUnload\": (event)=>{\n                    // Save immediately before unload\n                    saveFunction();\n                    // If bot is running, show warning\n                    if (state.botSystemStatus === 'Running') {\n                        const message = 'Trading bot is currently running. Are you sure you want to leave?';\n                        event.returnValue = message;\n                        return message;\n                    }\n                }\n            }[\"TradingProvider.useEffect.handleBeforeUnload\"];\n            window.addEventListener('beforeunload', handleBeforeUnload);\n            // Cleanup function\n            return ({\n                \"TradingProvider.useEffect\": ()=>{\n                    unsubscribeNetwork();\n                    unsubscribeMemory();\n                    autoSaveManager.disable();\n                    window.removeEventListener('beforeunload', handleBeforeUnload);\n                }\n            })[\"TradingProvider.useEffect\"];\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state,\n        toast\n    ]);\n    // Force save when bot status changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const autoSaveManager = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.AutoSaveManager.getInstance();\n            autoSaveManager.saveNow();\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus\n    ]);\n    // Manual save session function\n    const saveCurrentSession = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[saveCurrentSession]\": async ()=>{\n            try {\n                const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n                const currentSessionId = sessionManager.getCurrentSessionId();\n                if (!currentSessionId) {\n                    // No current session, only create one if bot is running or has meaningful data\n                    if (state.config.crypto1 && state.config.crypto2 && (state.botSystemStatus === 'Running' || state.targetPriceRows.length > 0 || state.orderHistory.length > 0)) {\n                        const currentBalances = {\n                            crypto1: state.crypto1Balance,\n                            crypto2: state.crypto2Balance,\n                            stablecoin: state.stablecoinBalance\n                        };\n                        const sessionId = await sessionManager.createNewSessionWithAutoName(state.config, undefined, currentBalances);\n                        sessionManager.setCurrentSession(sessionId);\n                        await sessionManager.saveSession(sessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, state.botSystemStatus === 'Running');\n                        return true;\n                    }\n                    console.log('⚠️ No session to save - bot not running and no meaningful data');\n                    return false;\n                }\n                // Save existing session\n                return await sessionManager.saveSession(currentSessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, state.botSystemStatus === 'Running');\n            } catch (error) {\n                console.error('Failed to save current session:', error);\n                return false;\n            }\n        }\n    }[\"TradingProvider.useCallback[saveCurrentSession]\"], [\n        state\n    ]);\n    // Context value\n    const contextValue = {\n        ...state,\n        dispatch,\n        setTargetPrices,\n        getDisplayOrders,\n        checkBackendStatus,\n        fetchMarketPrice,\n        startBackendBot,\n        stopBackendBot,\n        saveConfigToBackend,\n        saveCurrentSession,\n        backendStatus: state.backendStatus,\n        botSystemStatus: state.botSystemStatus,\n        isBotActive: state.botSystemStatus === 'Running'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TradingContext.Provider, {\n        value: contextValue,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\contexts\\\\TradingContext.tsx\",\n        lineNumber: 1916,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TradingProvider, \"Dul/342NNfDBwKH7syILMVemn7Y=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__.useToast\n    ];\n});\n_c = TradingProvider;\n// Custom hook to use the trading context\nconst useTradingContext = ()=>{\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(TradingContext);\n    if (context === undefined) {\n        throw new Error('useTradingContext must be used within a TradingProvider');\n    }\n    return context;\n};\n_s1(useTradingContext, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"TradingProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/TradingContext.tsx\n"));

/***/ })

});