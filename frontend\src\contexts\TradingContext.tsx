"use client";
import type { ReactNode } from 'react';
import React, { createContext, useContext, useReducer, useEffect, useCallback, useRef } from 'react';
import type { TradingConfig, TargetPriceRow, OrderHistoryEntry, AppSettings, OrderStatus, DisplayOrderRow } from '@/lib/types';
import { DEFAULT_APP_SETTINGS, AVAILABLE_CRYPTOS, AVAILABLE_QUOTES_SIMPLE, AVAILABLE_STABLECOINS } from '@/lib/types';
import { v4 as uuidv4 } from 'uuid';

// Define locally to avoid import issues
const DEFAULT_QUOTE_CURRENCIES = ["USDT", "USDC", "BTC"];
import { useToast } from "@/hooks/use-toast";
import { tradingApi } from '@/lib/api';
import { SessionManager } from '@/lib/session-manager';
import { NetworkMonitor, AutoSaveManager, MemoryMonitor } from '@/lib/network-monitor';

// Define Bot System Status
export type BotSystemStatus = 'Stopped' | 'WarmingUp' | 'Running';

// Static fallback prices (used when API is unavailable)
const getStaticUSDPrice = (crypto: string): number => {
  const usdPrices: { [key: string]: number } = {
    // Major cryptocurrencies - Updated to current market prices
    'BTC': 106000,  // Updated to match your example
    'ETH': 2500,    // Updated to match your example
    'SOL': 180,     // Updated to current price
    'ADA': 0.85,   // Updated
    'DOGE': 0.32,  // Updated
    'LINK': 22,    // Updated
    'MATIC': 0.42, // Updated
    'DOT': 6.5,    // Updated
    'AVAX': 38,    // Updated
    'SHIB': 0.000022, // Updated
    'XRP': 2.1,    // Updated
    'LTC': 95,     // Updated
    'BCH': 420,    // Updated
    // Stablecoins
    'USDT': 1.0,
    'USDC': 1.0,
    'FDUSD': 1.0,
    'BUSD': 1.0,
    'DAI': 1.0
  };
  return usdPrices[crypto.toUpperCase()] || 100;
};

// Static fallback function for market price calculation (no fluctuations)
const calculateStaticMarketPrice = (config: TradingConfig): number => {
  const crypto1USDPrice = getStaticUSDPrice(config.crypto1);
  const crypto2USDPrice = getStaticUSDPrice(config.crypto2);

  // Calculate the ratio: how many units of crypto2 = 1 unit of crypto1
  const basePrice = crypto1USDPrice / crypto2USDPrice;

  console.log(`📊 Static price calculation: ${config.crypto1} ($${crypto1USDPrice}) / ${config.crypto2} ($${crypto2USDPrice}) = ${basePrice.toFixed(6)}`);

  return basePrice;
};

// Update TargetPriceRow type
// (Assuming TargetPriceRow is defined in @/lib/types, this is conceptual)
// export interface TargetPriceRow {
//   // ... existing fields
//   lastActionTimestamp?: number;
// }

// Define action types
type Action =
  | { type: 'SET_CONFIG'; payload: Partial<TradingConfig> }
  | { type: 'SET_TARGET_PRICE_ROWS'; payload: TargetPriceRow[] }
  | { type: 'ADD_TARGET_PRICE_ROW'; payload: TargetPriceRow }
  | { type: 'UPDATE_TARGET_PRICE_ROW'; payload: TargetPriceRow }
  | { type: 'REMOVE_TARGET_PRICE_ROW'; payload: string }
  | { type: 'ADD_ORDER_HISTORY_ENTRY'; payload: OrderHistoryEntry }
  | { type: 'UPDATE_ORDER_HISTORY_ENTRY'; payload: OrderHistoryEntry }
  | { type: 'REMOVE_ORDER_HISTORY_ENTRY'; payload: string }
  | { type: 'SET_APP_SETTINGS'; payload: Partial<AppSettings> }
  | { type: 'SET_BACKEND_STATUS'; payload: 'online' | 'offline' }
  | { type: 'SET_MARKET_PRICE'; payload: number }
  | { type: 'SET_BALANCES'; payload: { crypto1: number; crypto2: number } }
  | { type: 'SYSTEM_START_BOT_INITIATE' }
  | { type: 'SYSTEM_COMPLETE_WARMUP' }
  | { type: 'SYSTEM_STOP_BOT' }
  | { type: 'SET_TARGET_PRICE_ROWS'; payload: TargetPriceRow[] };

// Define state type
interface TradingState {
  config: TradingConfig;
  targetPriceRows: TargetPriceRow[];
  orderHistory: OrderHistoryEntry[];
  appSettings: AppSettings;
  backendStatus: 'online' | 'offline' | 'unknown';
  currentMarketPrice: number;
  crypto1Balance: number;
  crypto2Balance: number;
  // isBotActive: boolean; // To be replaced by botSystemStatus
  botSystemStatus: BotSystemStatus; // New state for bot lifecycle
  stablecoinBalance: number; // For Stablecoin Swap mode
}

// Add this function to calculate the initial market price
const calculateInitialMarketPrice = (config: TradingConfig): number => {
  // Return 0 if either crypto is not selected
  if (!config.crypto1 || !config.crypto2) {
    return 0;
  }
  // Default fallback value for valid pairs
  return calculateStaticMarketPrice(config);
};

// Get real market price with multiple fallback strategies
const getRealBinancePrice = async (config: TradingConfig): Promise<number> => {
  try {
    // Return 0 if either crypto is not selected
    if (!config.crypto1 || !config.crypto2) {
      return 0;
    }

    const pair = `${config.crypto1}/${config.crypto2}`;

    // Strategy 1: Try backend endpoint (if available)
    try {
      const symbol = pair.replace('/', '-'); // Convert BTC/USDT to BTC-USDT for API
      const response = await tradingApi.getMarketPrice(symbol);
      if (response && response.price > 0) {
        console.log(`✅ Backend Binance price: ${config.crypto1}/${config.crypto2} = ${response.price}`);
        return response.price;
      }
    } catch (error) {
      console.warn(`⚠️ Backend API failed for ${pair}, trying alternative:`, error);
    }

    // Strategy 2: Try CoinGecko API as reliable fallback
    try {
      const crypto1Id = getCoinGeckoId(config.crypto1);
      const crypto2Id = getCoinGeckoId(config.crypto2);

      if (crypto1Id && crypto2Id) {
        const response = await fetch(`https://api.coingecko.com/api/v3/simple/price?ids=${crypto1Id}&vs_currencies=${crypto2Id}`, {
          method: 'GET',
          headers: { 'Accept': 'application/json' }
        });

        if (response.ok) {
          const data = await response.json();
          const price = data[crypto1Id]?.[crypto2Id];
          if (price > 0) {
            console.log(`✅ CoinGecko price: ${config.crypto1}/${config.crypto2} = ${price}`);
            return price;
          }
        }
      }
    } catch (error) {
      console.warn(`⚠️ CoinGecko API failed:`, error);
    }

    // If all APIs fail, throw error to use static fallback
    throw new Error('All price APIs failed');

  } catch (error) {
    console.error('❌ Error fetching real price:', error);
    throw error;
  }
};

// Helper function to map crypto symbols to CoinGecko IDs
const getCoinGeckoId = (symbol: string): string | null => {
  const mapping: { [key: string]: string } = {
    'BTC': 'bitcoin',
    'ETH': 'ethereum',
    'SOL': 'solana',
    'ADA': 'cardano',
    'DOT': 'polkadot',
    'MATIC': 'matic-network',
    'AVAX': 'avalanche-2',
    'LINK': 'chainlink',
    'UNI': 'uniswap',
    'USDT': 'tether',
    'USDC': 'usd-coin',
    'BUSD': 'binance-usd',
    'DAI': 'dai'
  };
  return mapping[symbol.toUpperCase()] || null;
};

// Helper function to get stablecoin exchange rates for real market data
const getStablecoinExchangeRate = async (crypto: string, stablecoin: string): Promise<number> => {
  try {
    // For stablecoin-to-stablecoin, assume 1:1 rate
    if (getCoinGeckoId(crypto) && getCoinGeckoId(stablecoin)) {
      const cryptoId = getCoinGeckoId(crypto);
      const stablecoinId = getCoinGeckoId(stablecoin);

      if (cryptoId === stablecoinId) return 1.0; // Same currency

      // Get real exchange rate from CoinGecko
      const response = await fetch(`https://api.coingecko.com/api/v3/simple/price?ids=${cryptoId}&vs_currencies=${stablecoinId}`);
      if (response.ok) {
        const data = await response.json();
        const rate = cryptoId && stablecoinId ? data[cryptoId]?.[stablecoinId] : null;
        if (rate > 0) {
          console.log(`📊 Stablecoin rate: ${crypto}/${stablecoin} = ${rate}`);
          return rate;
        }
      }
    }

    // Fallback: calculate via USD prices
    const cryptoUSDPrice = getUSDPrice(crypto);
    const stablecoinUSDPrice = getUSDPrice(stablecoin);
    const rate = cryptoUSDPrice / stablecoinUSDPrice;

    console.log(`📊 Fallback stablecoin rate: ${crypto}/${stablecoin} = ${rate} (via USD)`);
    return rate;

  } catch (error) {
    console.error('Error fetching stablecoin exchange rate:', error);
    // Final fallback
    const cryptoUSDPrice = getUSDPrice(crypto);
    const stablecoinUSDPrice = getUSDPrice(stablecoin);
    return cryptoUSDPrice / stablecoinUSDPrice;
  }
};

// Real-time price cache
let priceCache: { [key: string]: number } = {};
let lastPriceUpdate = 0;
const PRICE_CACHE_DURATION = 2000; // 2 seconds cache for real-time updates

// Get real USD price with multiple fallback strategies
const getRealBinanceUSDPrice = async (crypto: string): Promise<number> => {
  const now = Date.now();
  const cacheKey = crypto.toUpperCase();

  // Return cached price if still valid (2 seconds)
  if (priceCache[cacheKey] && (now - lastPriceUpdate) < PRICE_CACHE_DURATION) {
    return priceCache[cacheKey];
  }

  try {
    // Strategy 1: Try backend API to get real Binance USD price (via USDT)
    try {
      const pair = `${crypto.toUpperCase()}-USDT`;
      const response = await tradingApi.getMarketPrice(pair);
      if (response && response.price > 0) {
        priceCache[cacheKey] = response.price;
        lastPriceUpdate = now;
        console.log(`📊 Backend USD price for ${crypto}: $${response.price}`);
        return response.price;
      }
    } catch (error) {
      console.warn(`⚠️ Backend USD price failed for ${crypto}, trying CoinGecko:`, error);
    }

    // Strategy 2: Try CoinGecko API as reliable fallback
    try {
      const coinGeckoId = getCoinGeckoId(crypto);
      if (coinGeckoId) {
        const response = await fetch(`https://api.coingecko.com/api/v3/simple/price?ids=${coinGeckoId}&vs_currencies=usd`, {
          method: 'GET',
          headers: { 'Accept': 'application/json' }
        });

        if (response.ok) {
          const data = await response.json();
          const price = data[coinGeckoId]?.usd;
          if (price && price > 0) {
            priceCache[cacheKey] = price;
            lastPriceUpdate = now;
            console.log(`📊 CoinGecko USD price for ${crypto}: $${price}`);
            return price;
          }
        }
      }
    } catch (error) {
      console.warn(`⚠️ CoinGecko USD price failed for ${crypto}:`, error);
    }

    // If all APIs fail, throw error to use static fallback
    throw new Error(`All USD price APIs failed for ${crypto}`);
  } catch (error) {
    console.error(`❌ Failed to get USD price for ${crypto}:`, error);
    throw error;
  }
};

// Synchronous helper function to get USD price (uses cached values or static fallback)
const getUSDPrice = (crypto: string): number => {
  const cacheKey = crypto.toUpperCase();

  // Return cached price if available
  if (priceCache[cacheKey]) {
    return priceCache[cacheKey];
  }

  // Fallback to static prices
  return getStaticUSDPrice(crypto);
};





type TradingAction =
  | { type: 'SET_CONFIG'; payload: Partial<TradingConfig> }
  | { type: 'SET_TARGET_PRICE_ROWS'; payload: TargetPriceRow[] }
  | { type: 'ADD_TARGET_PRICE_ROW'; payload: TargetPriceRow }
  | { type: 'UPDATE_TARGET_PRICE_ROW'; payload: TargetPriceRow }
  | { type: 'REMOVE_TARGET_PRICE_ROW'; payload: string }
  | { type: 'ADD_ORDER_HISTORY_ENTRY'; payload: OrderHistoryEntry }
  | { type: 'SET_APP_SETTINGS'; payload: Partial<AppSettings> }
  | { type: 'SET_MARKET_PRICE'; payload: number }
  // | { type: 'SET_BOT_STATUS'; payload: boolean } // Removed
  | { type: 'UPDATE_BALANCES'; payload: { crypto1?: number; crypto2?: number; stablecoin?: number } }
  | { type: 'SET_BALANCES'; payload: { crypto1: number; crypto2: number; stablecoin?: number } }
  | { type: 'UPDATE_STABLECOIN_BALANCE'; payload: number }
  | { type: 'RESET_SESSION' }
  | { type: 'CLEAR_ORDER_HISTORY' }
  | { type: 'SET_BACKEND_STATUS'; payload: 'online' | 'offline' | 'unknown' }
  | { type: 'SYSTEM_START_BOT_INITIATE' }
  | { type: 'SYSTEM_COMPLETE_WARMUP' }
  | { type: 'SYSTEM_STOP_BOT' }
  | { type: 'SYSTEM_RESET_BOT' }
  | { type: 'SET_TARGET_PRICE_ROWS'; payload: TargetPriceRow[] }
  | { type: 'RESET_FOR_NEW_CRYPTO' };

const initialBaseConfig: TradingConfig = {
  tradingMode: "SimpleSpot",
  crypto1: "", // Start with empty crypto1
  crypto2: "", // Start with empty crypto2
  baseBid: 100,
  multiplier: 1.005,
  numDigits: 4,
  slippagePercent: 0.2, // Default 0.2% slippage range
  incomeSplitCrypto1Percent: 50,
  incomeSplitCrypto2Percent: 50,
  preferredStablecoin: AVAILABLE_STABLECOINS[0],
};

const initialTradingState: TradingState = {
  config: initialBaseConfig,
  targetPriceRows: [],
  orderHistory: [],
  appSettings: DEFAULT_APP_SETTINGS,
  currentMarketPrice: calculateInitialMarketPrice(initialBaseConfig),
  // isBotActive: false, // Remove
  botSystemStatus: 'Stopped', // Initialize as Stopped
  crypto1Balance: 10, // Mock initial balance
  crypto2Balance: 100000, // Increased balance for BTC/USDT trading (was 10000)
  stablecoinBalance: 0,
  backendStatus: 'unknown',
};

const lastActionTimestampPerCounter = new Map<number, number>();

// LocalStorage persistence functions
const STORAGE_KEY = 'pluto_trading_state';

const saveStateToLocalStorage = (state: TradingState) => {
  try {
    if (typeof window !== 'undefined') {
      const stateToSave = {
        config: state.config,
        targetPriceRows: state.targetPriceRows,
        orderHistory: state.orderHistory,
        appSettings: state.appSettings,
        currentMarketPrice: state.currentMarketPrice,
        crypto1Balance: state.crypto1Balance,
        crypto2Balance: state.crypto2Balance,
        stablecoinBalance: state.stablecoinBalance,
        botSystemStatus: state.botSystemStatus,
        timestamp: Date.now()
      };
      localStorage.setItem(STORAGE_KEY, JSON.stringify(stateToSave));
    }
  } catch (error) {
    console.error('Failed to save state to localStorage:', error);
  }
};

const loadStateFromLocalStorage = (): Partial<TradingState> | null => {
  try {
    if (typeof window !== 'undefined') {
      const savedState = localStorage.getItem(STORAGE_KEY);
      if (savedState) {
        const parsed = JSON.parse(savedState);
        // Check if state is not too old (24 hours)
        if (parsed.timestamp && (Date.now() - parsed.timestamp) < 24 * 60 * 60 * 1000) {
          return parsed;
        }
      }
    }
  } catch (error) {
    console.error('Failed to load state from localStorage:', error);
  }
  return null;
};

const tradingReducer = (state: TradingState, action: TradingAction): TradingState => {
  switch (action.type) {
    case 'SET_CONFIG':
      const newConfig = { ...state.config, ...action.payload };
      // If trading pair changes, reset market price (it will be re-calculated by effect)
      if (action.payload.crypto1 || action.payload.crypto2) {
        return { ...state, config: newConfig, currentMarketPrice: calculateInitialMarketPrice(newConfig) };
      }
      return { ...state, config: newConfig };
    case 'SET_TARGET_PRICE_ROWS':
      return { ...state, targetPriceRows: action.payload.sort((a: TargetPriceRow, b: TargetPriceRow) => a.targetPrice - b.targetPrice).map((row: TargetPriceRow, index: number) => ({...row, counter: index + 1})) };
    case 'ADD_TARGET_PRICE_ROW': {
      const newRows = [...state.targetPriceRows, action.payload].sort((a: TargetPriceRow, b: TargetPriceRow) => a.targetPrice - b.targetPrice).map((row: TargetPriceRow, index: number) => ({...row, counter: index + 1}));
      return { ...state, targetPriceRows: newRows };
    }
    case 'UPDATE_TARGET_PRICE_ROW': {
      const updatedRows = state.targetPriceRows.map((row: TargetPriceRow) =>
        row.id === action.payload.id ? action.payload : row
      ).sort((a: TargetPriceRow, b: TargetPriceRow) => a.targetPrice - b.targetPrice).map((row: TargetPriceRow, index: number) => ({...row, counter: index + 1}));
      return { ...state, targetPriceRows: updatedRows };
    }
    case 'REMOVE_TARGET_PRICE_ROW': {
      const filteredRows = state.targetPriceRows.filter((row: TargetPriceRow) => row.id !== action.payload).sort((a: TargetPriceRow, b: TargetPriceRow) => a.targetPrice - b.targetPrice).map((row: TargetPriceRow, index: number) => ({...row, counter: index + 1}));
      return { ...state, targetPriceRows: filteredRows };
    }
    case 'ADD_ORDER_HISTORY_ENTRY':
      return { ...state, orderHistory: [action.payload, ...state.orderHistory] };
    case 'CLEAR_ORDER_HISTORY':
      return { ...state, orderHistory: [] };
    case 'SET_APP_SETTINGS':
      return { ...state, appSettings: { ...state.appSettings, ...action.payload } };
    case 'SET_MARKET_PRICE':
      return { ...state, currentMarketPrice: action.payload };
    // case 'SET_BOT_STATUS': // Removed
    case 'UPDATE_BALANCES':
      return {
        ...state,
        crypto1Balance: action.payload.crypto1 !== undefined ? action.payload.crypto1 : state.crypto1Balance,
        crypto2Balance: action.payload.crypto2 !== undefined ? action.payload.crypto2 : state.crypto2Balance,
        stablecoinBalance: action.payload.stablecoin !== undefined ? action.payload.stablecoin : state.stablecoinBalance,
      };
    case 'SET_BALANCES':
      return {
        ...state,
        crypto1Balance: action.payload.crypto1,
        crypto2Balance: action.payload.crypto2,
        stablecoinBalance: action.payload.stablecoin !== undefined ? action.payload.stablecoin : state.stablecoinBalance,
      };
    case 'UPDATE_STABLECOIN_BALANCE':
      return {
        ...state,
        stablecoinBalance: action.payload,
      };
    case 'RESET_SESSION':
      const configForReset = { ...state.config };
      return {
        ...initialTradingState,
        config: configForReset,
        appSettings: { ...state.appSettings },
        currentMarketPrice: calculateInitialMarketPrice(configForReset),
        // Preserve current balances instead of resetting to initial values
        crypto1Balance: state.crypto1Balance,
        crypto2Balance: state.crypto2Balance,
        stablecoinBalance: state.stablecoinBalance,
      };
    case 'SET_BACKEND_STATUS':
      return { ...state, backendStatus: action.payload };
    case 'SYSTEM_START_BOT_INITIATE':
      // Continue from previous state instead of resetting
      return {
        ...state,
        botSystemStatus: 'WarmingUp',
        // Keep existing targetPriceRows and orderHistory - don't reset
      };
    case 'SYSTEM_COMPLETE_WARMUP':
      return { ...state, botSystemStatus: 'Running' };
    case 'SYSTEM_STOP_BOT':
      return { ...state, botSystemStatus: 'Stopped' };
    case 'SYSTEM_RESET_BOT':
      // Fresh Start: Clear all target price rows and order history completely
      lastActionTimestampPerCounter.clear();
      // Clear current session to force new session name generation
      const sessionManager = SessionManager.getInstance();
      sessionManager.clearCurrentSession();
      return {
        ...state,
        botSystemStatus: 'Stopped',
        targetPriceRows: [], // Completely clear all target price rows
        orderHistory: [],
      };
    case 'SET_TARGET_PRICE_ROWS':
      return { ...state, targetPriceRows: action.payload };
    case 'RESET_FOR_NEW_CRYPTO':
      return {
        ...initialTradingState,
        config: state.config, // Keep the new crypto configuration
        backendStatus: state.backendStatus, // Preserve backend status
        botSystemStatus: 'Stopped', // Ensure bot is stopped
        currentMarketPrice: 0, // Reset market price for new crypto
        // Preserve current balances instead of resetting to initial values
        crypto1Balance: state.crypto1Balance,
        crypto2Balance: state.crypto2Balance,
        stablecoinBalance: state.stablecoinBalance,
      };
    default:
      return state;
  }
};

interface TradingContextType extends Omit<TradingState, 'backendStatus' | 'botSystemStatus' | 'crypto1Balance' | 'crypto2Balance' | 'stablecoinBalance'> {
  dispatch: React.Dispatch<TradingAction>;
  setTargetPrices: (prices: number[]) => void;
  getDisplayOrders: () => DisplayOrderRow[];
  backendStatus: 'online' | 'offline' | 'unknown';
  fetchMarketPrice: () => Promise<void>;
  checkBackendStatus: () => Promise<void>;
  botSystemStatus: BotSystemStatus;
  isBotActive: boolean; // Derived from botSystemStatus
  startBackendBot: (configId: string) => Promise<boolean>;
  stopBackendBot: (configId: string) => Promise<boolean>;
  saveConfigToBackend: (config: TradingConfig) => Promise<string | null>;
  saveCurrentSession: () => Promise<boolean>;
  // Keep balance values for internal trading logic but don't expose them
  crypto1Balance: number;
  crypto2Balance: number;
  stablecoinBalance: number;
}

const TradingContext = createContext<TradingContextType | undefined>(undefined);

// SIMPLIFIED LOGIC - NO COMPLEX COOLDOWNS

export const TradingProvider = ({ children }: { children: ReactNode }) => {
  // Initialize state with localStorage data if available
  const initializeState = () => {
    // Check if this is a new session from URL parameter
    if (typeof window !== 'undefined') {
      const urlParams = new URLSearchParams(window.location.search);
      const isNewSession = urlParams.get('newSession') === 'true';

      if (isNewSession) {
        console.log('🆕 New session requested - starting with completely fresh state');
        // Note: URL parameter will be cleared in useEffect to avoid render-time state updates
        return initialTradingState;
      }
    }

    // Check if this is a new window/tab by checking if we have a current session
    const sessionManager = SessionManager.getInstance();
    const currentSessionId = sessionManager.getCurrentSessionId();

    // If no current session exists for this window, start fresh
    if (!currentSessionId) {
      console.log('🆕 New window detected - starting with fresh state');
      return initialTradingState;
    }

    // If we have a session, try to load the session data first (priority over localStorage)
    const currentSession = sessionManager.loadSession(currentSessionId);
    if (currentSession) {
      console.log('🔄 Loading session data with balances:', {
        crypto1: currentSession.crypto1Balance,
        crypto2: currentSession.crypto2Balance,
        stablecoin: currentSession.stablecoinBalance
      });
      return {
        ...initialTradingState,
        config: currentSession.config,
        targetPriceRows: currentSession.targetPriceRows,
        orderHistory: currentSession.orderHistory,
        currentMarketPrice: currentSession.currentMarketPrice,
        crypto1Balance: currentSession.crypto1Balance,
        crypto2Balance: currentSession.crypto2Balance,
        stablecoinBalance: currentSession.stablecoinBalance,
        botSystemStatus: (currentSession.isActive ? 'Running' : 'Stopped') as BotSystemStatus
      };
    }

    // Fallback to localStorage if session loading fails
    const savedState = loadStateFromLocalStorage();
    if (savedState) {
      return { ...initialTradingState, ...savedState };
    }
    return initialTradingState;
  };

  const [state, dispatch] = useReducer(tradingReducer, initializeState());
  const { toast } = useToast();

  // Clear URL parameter after component mounts to avoid render-time state updates
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const urlParams = new URLSearchParams(window.location.search);
      const isNewSession = urlParams.get('newSession') === 'true';

      if (isNewSession) {
        // Clear the URL parameter to avoid confusion
        const newUrl = window.location.pathname;
        window.history.replaceState({}, '', newUrl);
      }
    }
  }, []); // Run only once on mount
  const audioRef = useRef<HTMLAudioElement | null>(null);
  // Removed processing locks and cooldowns for continuous trading

  // Initialize fetchMarketPrice to get real Binance prices with fallback
  const fetchMarketPrice = useCallback(async (): Promise<void> => {
    try {
      // Don't fetch price if either crypto is not selected
      if (!state.config.crypto1 || !state.config.crypto2) {
        dispatch({ type: 'SET_MARKET_PRICE', payload: 0 });
        return;
      }

      let price: number;

      try {
        // Try to get real Binance prices with timeout protection
        const timeoutPromise = new Promise<never>((_, reject) => {
          setTimeout(() => reject(new Error('Price fetch timeout')), 5000); // 5 second timeout
        });

        if (state.config.tradingMode === "StablecoinSwap") {
          // For StablecoinSwap mode, try real Binance USD prices with timeout
          const pricePromise = Promise.all([
            getRealBinanceUSDPrice(state.config.crypto1),
            getRealBinanceUSDPrice(state.config.crypto2)
          ]).then(([crypto1USDPrice, crypto2USDPrice]) => {
            const calculatedPrice = crypto1USDPrice / crypto2USDPrice;
            console.log(`📊 StablecoinSwap Real Binance Price:
              - ${state.config.crypto1} USD Price: $${crypto1USDPrice.toLocaleString()}
              - ${state.config.crypto2} USD Price: $${crypto2USDPrice.toLocaleString()}
              - Market Price (${state.config.crypto1}/${state.config.crypto2}): ${calculatedPrice.toFixed(6)}`);
            return calculatedPrice;
          });

          price = await Promise.race([pricePromise, timeoutPromise]);
        } else {
          // For SimpleSpot mode, try real Binance API via backend with timeout
          const pricePromise = getRealBinancePrice(state.config);
          price = await Promise.race([pricePromise, timeoutPromise]);
        }

        dispatch({ type: 'SET_MARKET_PRICE', payload: price });
        console.log(`✅ Successfully fetched real Binance price: ${price}`);
      } catch (error) {
        console.warn('⚠️ Real Binance API failed, using static price:', error);
        // Use static fallback if real API fails or times out
        const fallbackPrice = calculateStaticMarketPrice(state.config);
        dispatch({ type: 'SET_MARKET_PRICE', payload: fallbackPrice });
      }
    } catch (error) {
      console.error('❌ Critical error in fetchMarketPrice:', error);
      const fallbackPrice = calculateStaticMarketPrice(state.config);
      dispatch({ type: 'SET_MARKET_PRICE', payload: fallbackPrice });
    }
  }, [state.config, dispatch]);

  // Market price real-time updates from Binance
  useEffect(() => {
    // Initial fetch
    fetchMarketPrice();

    // Set up real-time price updates every 2 seconds from Binance
    const priceUpdateInterval = setInterval(async () => {
      const networkMonitor = NetworkMonitor.getInstance();
      if (networkMonitor.getStatus().isOnline) {
        try {
          await fetchMarketPrice();
          console.log('📊 Binance price updated');
        } catch (error) {
          console.warn('⚠️ Failed to update price from Binance:', error);
        }
      }
    }, 2000); // Update every 2 seconds as requested

    return () => {
      clearInterval(priceUpdateInterval);
    };
  }, [fetchMarketPrice, state.config.crypto1, state.config.crypto2, state.config.tradingMode]);

  // Audio initialization and user interaction detection
  useEffect(() => {
    if (typeof window !== "undefined") {
      audioRef.current = new Audio();

      // Add user interaction listener to enable audio
      const enableAudio = () => {
        if (audioRef.current) {
          // Try to play a silent audio to enable audio context
          audioRef.current.volume = 0;
          audioRef.current.play().then(() => {
            if (audioRef.current) {
              audioRef.current.volume = 1;
            }
            console.log('🔊 Audio context enabled after user interaction');
          }).catch(() => {
            // Still blocked, but we tried
          });
        }
        // Remove the listener after first interaction
        document.removeEventListener('click', enableAudio);
        document.removeEventListener('keydown', enableAudio);
      };

      // Listen for first user interaction
      document.addEventListener('click', enableAudio);
      document.addEventListener('keydown', enableAudio);

      return () => {
        document.removeEventListener('click', enableAudio);
        document.removeEventListener('keydown', enableAudio);
      };
    }
  }, []);

  const playSound = useCallback((soundKey: 'soundOrderExecution' | 'soundError') => {
    // Get current session's alarm settings, fallback to global app settings
    const sessionManager = SessionManager.getInstance();
    const currentSessionId = sessionManager.getCurrentSessionId();
    let alarmSettings = state.appSettings; // Default fallback

    if (currentSessionId) {
      const currentSession = sessionManager.loadSession(currentSessionId);
      if (currentSession && currentSession.alarmSettings) {
        alarmSettings = currentSession.alarmSettings;
      }
    }

    if (alarmSettings.soundAlertsEnabled && audioRef.current) {
      let soundPath: string | undefined;
      if (soundKey === 'soundOrderExecution' && alarmSettings.alertOnOrderExecution) {
        soundPath = alarmSettings.soundOrderExecution;
      } else if (soundKey === 'soundError' && alarmSettings.alertOnError) {
        soundPath = alarmSettings.soundError;
      }

      if (soundPath) {
        try {
          // Stop any currently playing audio first
          audioRef.current.pause();
          audioRef.current.currentTime = 0;

          // Set the source and load the audio
          audioRef.current.src = soundPath;
          audioRef.current.load(); // Explicitly load the audio

          // Add error handler for audio loading
          audioRef.current.onerror = (e) => {
            console.warn(`⚠️ Audio loading failed for ${soundPath}:`, e);
            // Try fallback sound
            if (soundPath !== '/sounds/chime2.wav' && audioRef.current) {
              audioRef.current.src = '/sounds/chime2.wav';
              audioRef.current.load();
            }
          };

          // Play the sound and limit duration to 2 seconds
          const playPromise = audioRef.current.play();

          if (playPromise !== undefined) {
            playPromise.then(() => {
              // Set a timeout to pause the audio after 2 seconds
              setTimeout(() => {
                if (audioRef.current && !audioRef.current.paused) {
                  audioRef.current.pause();
                  audioRef.current.currentTime = 0; // Reset for next play
                }
              }, 2000); // 2 seconds
            }).catch(err => {
              // Handle different types of audio errors gracefully
              const errorMessage = err.message || String(err);

              if (errorMessage.includes('user didn\'t interact') || errorMessage.includes('autoplay')) {
                console.log('🔇 Audio autoplay blocked by browser - user interaction required');
                // Could show a notification to user about enabling audio
              } else if (errorMessage.includes('interrupted') || errorMessage.includes('supported source')) {
                // These are expected errors, don't log them
              } else {
                console.warn("⚠️ Audio playback failed:", errorMessage);
              }
            });
          }
        } catch (error) {
          console.warn(`⚠️ Audio setup failed for ${soundPath}:`, error);
        }
      }
    }
  }, [state.appSettings]);

  // Enhanced Telegram notification function with error handling
  const sendTelegramNotification = useCallback(async (message: string, isError: boolean = false) => {
    try {
      const telegramToken = localStorage.getItem('telegram_bot_token');
      const telegramChatId = localStorage.getItem('telegram_chat_id');

      if (!telegramToken || !telegramChatId) {
        console.log('Telegram not configured - skipping notification');
        return;
      }

      const response = await fetch(`https://api.telegram.org/bot${telegramToken}/sendMessage`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          chat_id: telegramChatId,
          text: message,
          parse_mode: 'HTML'
        })
      });

      if (!response.ok) {
        console.error('Failed to send Telegram notification:', response.statusText);
        // Don't retry error notifications to avoid infinite loops
        if (!isError) {
          await sendTelegramErrorNotification('Telegram API Error', `Failed to send notification: ${response.statusText}`);
        }
      } else {
        console.log('✅ Telegram notification sent successfully');
      }
    } catch (error) {
      console.error('Error sending Telegram notification:', error);
      // Network disconnection detected
      if (!isError) {
        await sendTelegramErrorNotification('Network Disconnection', `Failed to connect to Telegram API: ${error}`);
      }
    }
  }, []);

  // Specific error notification functions
  const sendTelegramErrorNotification = useCallback(async (errorType: string, errorMessage: string) => {
    const timestamp = new Date().toLocaleString();
    const message = `⚠️ <b>ERROR ALERT</b>\n\n` +
                   `🔴 <b>Type:</b> ${errorType}\n` +
                   `📝 <b>Message:</b> ${errorMessage}\n` +
                   `⏰ <b>Time:</b> ${timestamp}\n` +
                   `🤖 <b>Bot:</b> ${state.config.crypto1}/${state.config.crypto2} ${state.config.tradingMode}`;

    await sendTelegramNotification(message, true); // Mark as error to prevent recursion
  }, [state.config, sendTelegramNotification]);

  const sendLowBalanceNotification = useCallback(async (crypto: string, currentBalance: number, requiredAmount: number) => {
    const timestamp = new Date().toLocaleString();
    const message = `💰 <b>LOW BALANCE ALERT</b>\n\n` +
                   `🪙 <b>Currency:</b> ${crypto}\n` +
                   `📊 <b>Current Balance:</b> ${currentBalance.toFixed(6)} ${crypto}\n` +
                   `⚡ <b>Required Amount:</b> ${requiredAmount.toFixed(6)} ${crypto}\n` +
                   `📉 <b>Shortage:</b> ${(requiredAmount - currentBalance).toFixed(6)} ${crypto}\n` +
                   `⏰ <b>Time:</b> ${timestamp}\n` +
                   `🤖 <b>Bot:</b> ${state.config.crypto1}/${state.config.crypto2} ${state.config.tradingMode}`;

    await sendTelegramNotification(message);
  }, [state.config, sendTelegramNotification]);

  const sendAPIErrorNotification = useCallback(async (apiName: string, errorDetails: string) => {
    const timestamp = new Date().toLocaleString();
    const message = `🔌 <b>API ERROR ALERT</b>\n\n` +
                   `🌐 <b>API:</b> ${apiName}\n` +
                   `❌ <b>Error:</b> ${errorDetails}\n` +
                   `⏰ <b>Time:</b> ${timestamp}\n` +
                   `🤖 <b>Bot:</b> ${state.config.crypto1}/${state.config.crypto2} ${state.config.tradingMode}`;

    await sendTelegramNotification(message);
  }, [state.config, sendTelegramNotification]);

  // Effect to update market price when trading pair changes
  useEffect(() => {
    // When crypto1 or crypto2 (parts of state.config) change,
    // the fetchMarketPrice useCallback gets a new reference.
    // The useEffect above (which depends on fetchMarketPrice)
    // will re-run, clear the old interval, make an initial fetch with the new config,
    // and set up a new interval.
    // The reducer for SET_CONFIG also sets an initial market price if crypto1/crypto2 changes.
    // Thus, no explicit dispatch is needed here.
  }, [state.config.crypto1, state.config.crypto2]); // Dependencies ensure this reacts to pair changes

  const setTargetPrices = useCallback((prices: number[]) => {
    if (!prices || !Array.isArray(prices)) return;

    // Sort prices from lowest to highest for proper counter assignment
    const sortedPrices = [...prices].filter((price: number) => !isNaN(price) && price > 0).sort((a, b) => a - b);

    const newRows: TargetPriceRow[] = sortedPrices.map((price: number, index: number) => {
        const existingRow = state.targetPriceRows.find((r: TargetPriceRow) => r.targetPrice === price);
        if (existingRow) {
          // Update counter for existing row based on sorted position
          return { ...existingRow, counter: index + 1 };
        }

        return {
          id: uuidv4(),
          counter: index + 1, // Counter starts from 1, lowest price gets counter 1
          status: 'Free' as OrderStatus,
          orderLevel: 0,
          valueLevel: state.config.baseBid, // Use baseBid from config
          targetPrice: price,
        };
      });
    dispatch({ type: 'SET_TARGET_PRICE_ROWS', payload: newRows });
  }, [state.targetPriceRows, state.config.baseBid, dispatch]);

  // Core Trading Logic (Simulated) - CONTINUOUS TRADING VERSION
  useEffect(() => {
    // Check network status first
    const networkMonitor = NetworkMonitor.getInstance();
    const isOnline = networkMonitor.getStatus().isOnline;

    // Only check essential conditions AND network status
    if (state.botSystemStatus !== 'Running' ||
        state.targetPriceRows.length === 0 ||
        state.currentMarketPrice <= 0 ||
        !isOnline) {

      if (!isOnline && state.botSystemStatus === 'Running') {
        console.log('🔴 Trading paused - network offline');
      }
      return;
    }

    // Execute trading logic immediately - no locks, no cooldowns, no delays
    const { config, currentMarketPrice, targetPriceRows, crypto1Balance, crypto2Balance } = state;
    const sortedRowsForLogic = [...targetPriceRows].sort((a: TargetPriceRow, b: TargetPriceRow) => a.targetPrice - b.targetPrice);

    // Use mutable variables for balance tracking within this cycle
    let currentCrypto1Balance = crypto1Balance;
    let currentCrypto2Balance = crypto2Balance;
    let actionsTaken = 0;

    console.log(`🚀 CONTINUOUS TRADING: Price $${currentMarketPrice.toFixed(2)} | Targets: ${sortedRowsForLogic.length} | Balance: $${currentCrypto2Balance} ${config.crypto2}`);

    // Show which targets are in range
    const targetsInRange = sortedRowsForLogic.filter(row => {
      const diffPercent = (Math.abs(currentMarketPrice - row.targetPrice) / currentMarketPrice) * 100;
      return diffPercent <= config.slippagePercent;
    });

    if (targetsInRange.length > 0) {
      console.log(`🎯 TARGETS IN RANGE (±${config.slippagePercent}%):`, targetsInRange.map(row => `Counter ${row.counter} (${row.status})`));
    }

    // CONTINUOUS TRADING LOGIC: Process all targets immediately
    for (let i = 0; i < sortedRowsForLogic.length; i++) {
      const activeRow = sortedRowsForLogic[i];
      const priceDiffPercent = Math.abs(currentMarketPrice - activeRow.targetPrice) / currentMarketPrice * 100;

      // STEP 1: Check if TargetRowN is triggered (within slippage range)
      if (priceDiffPercent <= config.slippagePercent) {

        if (config.tradingMode === "SimpleSpot") {
          // STEP 2: Evaluate Triggered TargetRowN's Status
          if (activeRow.status === "Free") {
            // STEP 2a: Execute BUY on TargetRowN
            const costCrypto2 = activeRow.valueLevel;

            if (currentCrypto2Balance >= costCrypto2) {
              const amountCrypto1Bought = costCrypto2 / currentMarketPrice;
              const updatedRow: TargetPriceRow = {
                ...activeRow,
                status: "Full",
                orderLevel: activeRow.orderLevel + 1,
                valueLevel: config.baseBid * Math.pow(config.multiplier, activeRow.orderLevel + 1),
                crypto1AmountHeld: amountCrypto1Bought,
                originalCostCrypto2: costCrypto2,
                crypto1Var: amountCrypto1Bought,
                crypto2Var: -costCrypto2,
              };
              dispatch({ type: 'UPDATE_TARGET_PRICE_ROW', payload: updatedRow });
              dispatch({ type: 'UPDATE_BALANCES', payload: { crypto1: currentCrypto1Balance + amountCrypto1Bought, crypto2: currentCrypto2Balance - costCrypto2 } });
              dispatch({
                type: 'ADD_ORDER_HISTORY_ENTRY',
                payload: {
                  id: uuidv4(),
                  timestamp: Date.now(),
                  pair: `${config.crypto1}/${config.crypto2}`,
                  crypto1: config.crypto1,
                  orderType: "BUY",
                  amountCrypto1: amountCrypto1Bought,
                  avgPrice: currentMarketPrice,
                  valueCrypto2: costCrypto2,
                  price1: currentMarketPrice,
                  crypto1Symbol: config.crypto1 || '',
                  crypto2Symbol: config.crypto2 || ''
                }
              });
              console.log(`✅ BUY: Counter ${activeRow.counter} bought ${amountCrypto1Bought.toFixed(6)} ${config.crypto1} at $${currentMarketPrice.toFixed(2)}`);
              toast({ title: "BUY Executed", description: `Counter ${activeRow.counter}: ${amountCrypto1Bought.toFixed(6)} ${config.crypto1}`, duration: 2000 });
              playSound('soundOrderExecution');

              // Send Telegram notification for BUY
              sendTelegramNotification(
                `🟢 <b>BUY EXECUTED</b>\n` +
                `📊 Counter: ${activeRow.counter}\n` +
                `💰 Amount: ${amountCrypto1Bought.toFixed(6)} ${config.crypto1}\n` +
                `💵 Price: $${currentMarketPrice.toFixed(2)}\n` +
                `💸 Cost: $${costCrypto2.toFixed(2)} ${config.crypto2}\n` +
                `📈 Mode: Simple Spot`
              );

              actionsTaken++;

              // Update local balance for subsequent checks in this cycle
              currentCrypto2Balance -= costCrypto2;
              currentCrypto1Balance += amountCrypto1Bought;
            } else {
              // Insufficient balance - send Telegram notification
              console.log(`❌ Insufficient ${config.crypto2} balance: ${currentCrypto2Balance.toFixed(6)} < ${costCrypto2.toFixed(6)}`);
              sendLowBalanceNotification(config.crypto2, currentCrypto2Balance, costCrypto2).catch(console.error);
            }
          }

          // STEP 3: Check and Process Inferior TargetRow (TargetRowN_minus_1) - ALWAYS, regardless of BUY
          const currentCounter = activeRow.counter;
          const inferiorRow = sortedRowsForLogic.find(row => row.counter === currentCounter - 1);

          if (inferiorRow && inferiorRow.status === "Full" && inferiorRow.crypto1AmountHeld && inferiorRow.originalCostCrypto2) {
            const amountCrypto1ToSell = inferiorRow.crypto1AmountHeld;
            const crypto2Received = amountCrypto1ToSell * currentMarketPrice;
            const realizedProfit = crypto2Received - inferiorRow.originalCostCrypto2;

            // Calculate Crypto1 profit/loss based on income split percentage
            const realizedProfitCrypto1 = currentMarketPrice > 0 ? (realizedProfit * config.incomeSplitCrypto1Percent / 100) / currentMarketPrice : 0;

            const updatedInferiorRow: TargetPriceRow = {
              ...inferiorRow,
              status: "Free",
              crypto1AmountHeld: undefined,
              originalCostCrypto2: undefined,
              valueLevel: config.baseBid * Math.pow(config.multiplier, inferiorRow.orderLevel),
              crypto1Var: -amountCrypto1ToSell,
              crypto2Var: crypto2Received,
            };
            dispatch({ type: 'UPDATE_TARGET_PRICE_ROW', payload: updatedInferiorRow });
            dispatch({ type: 'UPDATE_BALANCES', payload: { crypto1: currentCrypto1Balance - amountCrypto1ToSell, crypto2: currentCrypto2Balance + crypto2Received } });
            dispatch({
              type: 'ADD_ORDER_HISTORY_ENTRY',
              payload: {
                id: uuidv4(),
                timestamp: Date.now(),
                pair: `${config.crypto1}/${config.crypto2}`,
                crypto1: config.crypto1,
                orderType: "SELL",
                amountCrypto1: amountCrypto1ToSell,
                avgPrice: currentMarketPrice,
                valueCrypto2: crypto2Received,
                price1: currentMarketPrice,
                crypto1Symbol: config.crypto1 || '',
                crypto2Symbol: config.crypto2 || '',
                realizedProfitLossCrypto2: realizedProfit,
                realizedProfitLossCrypto1: realizedProfitCrypto1
              }
            });
            console.log(`✅ SELL: Counter ${currentCounter - 1} sold ${amountCrypto1ToSell.toFixed(6)} ${config.crypto1}. Profit: $${realizedProfit.toFixed(2)}`);
            toast({ title: "SELL Executed", description: `Counter ${currentCounter - 1}: Profit $${realizedProfit.toFixed(2)}`, duration: 2000 });
            playSound('soundOrderExecution');

            // Send Telegram notification for SELL
            const profitEmoji = realizedProfit > 0 ? '📈' : realizedProfit < 0 ? '📉' : '➖';
            sendTelegramNotification(
              `🔴 <b>SELL EXECUTED</b>\n` +
              `📊 Counter: ${currentCounter - 1}\n` +
              `💰 Amount: ${amountCrypto1ToSell.toFixed(6)} ${config.crypto1}\n` +
              `💵 Price: $${currentMarketPrice.toFixed(2)}\n` +
              `💸 Received: $${crypto2Received.toFixed(2)} ${config.crypto2}\n` +
              `${profitEmoji} Profit: $${realizedProfit.toFixed(2)} ${config.crypto2}\n` +
              `📈 Mode: Simple Spot`
            );

            actionsTaken++;

            // Update local balance for subsequent checks in this cycle
            currentCrypto1Balance -= amountCrypto1ToSell;
            currentCrypto2Balance += crypto2Received;
          }
        } else if (config.tradingMode === "StablecoinSwap") {
          // STEP 2: Evaluate Triggered TargetRowN's Status (Stablecoin Swap Mode)
          if (activeRow.status === "Free") {
            // STEP 2a: Execute Two-Step "Buy Crypto1 via Stablecoin" on TargetRowN
            const amountCrypto2ToUse = activeRow.valueLevel; // Value = BaseBid * (Multiplier ^ Level)

            if (currentCrypto2Balance >= amountCrypto2ToUse) {
              // Step 1: Sell Crypto2 for PreferredStablecoin
              // Use current fluctuating market price for more realistic P/L
              const crypto2USDPrice = getUSDPrice(config.crypto2 || 'USDT');
              const stablecoinUSDPrice = getUSDPrice(config.preferredStablecoin || 'USDT');
              const crypto2StablecoinPrice = crypto2USDPrice / stablecoinUSDPrice;
              const stablecoinObtained = amountCrypto2ToUse * crypto2StablecoinPrice;

              // Step 2: Buy Crypto1 with Stablecoin
              // Use current fluctuating market price (this is the key for P/L calculation)
              const crypto1USDPrice = getUSDPrice(config.crypto1 || 'BTC');
              // Apply current market price fluctuation to Crypto1 price
              const fluctuatedCrypto1Price = crypto1USDPrice * (state.currentMarketPrice / (crypto1USDPrice / crypto2USDPrice));
              const crypto1StablecoinPrice = fluctuatedCrypto1Price / stablecoinUSDPrice;
              const crypto1Bought = stablecoinObtained / crypto1StablecoinPrice;

              // Update Row N: Free → Full, Level++, Value recalculated
              const newLevel = activeRow.orderLevel + 1;
              const newValue = config.baseBid * Math.pow(config.multiplier, newLevel);

              const updatedRow: TargetPriceRow = {
                ...activeRow,
                status: "Full", // Free → Full
                orderLevel: newLevel, // Level = Level + 1
                valueLevel: newValue, // Value = BaseBid * (Multiplier ^ Level)
                crypto1AmountHeld: crypto1Bought, // Store amount held for N-1 logic
                originalCostCrypto2: amountCrypto2ToUse, // Store original cost for profit calculation
                crypto1Var: crypto1Bought, // Positive amount of Crypto1 acquired
                crypto2Var: -amountCrypto2ToUse, // Negative amount of Crypto2 sold
              };

              console.log(`💰 StablecoinSwap BUY - Setting originalCostCrypto2:`, {
                counter: activeRow.counter,
                amountCrypto2ToUse: amountCrypto2ToUse,
                crypto1Bought: crypto1Bought,
                originalCostCrypto2: amountCrypto2ToUse,
                newLevel: newLevel,
                crypto1StablecoinPrice: crypto1StablecoinPrice,
                fluctuatedCrypto1Price: fluctuatedCrypto1Price,
                currentMarketPrice: state.currentMarketPrice
              });

              dispatch({ type: 'UPDATE_TARGET_PRICE_ROW', payload: updatedRow });
              dispatch({ type: 'UPDATE_BALANCES', payload: { crypto1: currentCrypto1Balance + crypto1Bought, crypto2: currentCrypto2Balance - amountCrypto2ToUse } });

              // Add history entries for both steps of the stablecoin swap
              // Calculate P/L for the crypto2 sell based on price fluctuation
              const baseCrypto2Price = getUSDPrice(config.crypto2) / getUSDPrice(config.preferredStablecoin);
              const estimatedProfitCrypto2 = amountCrypto2ToUse * (crypto2StablecoinPrice - baseCrypto2Price);
              const estimatedProfitCrypto1 = estimatedProfitCrypto2 / crypto1StablecoinPrice;

              console.log(`📊 StablecoinSwap Step A SELL P/L:`, {
                crypto2: config.crypto2,
                amountSold: amountCrypto2ToUse,
                currentPrice: crypto2StablecoinPrice,
                basePrice: baseCrypto2Price,
                estimatedProfitCrypto2: estimatedProfitCrypto2,
                estimatedProfitCrypto1: estimatedProfitCrypto1
              });

              dispatch({
                type: 'ADD_ORDER_HISTORY_ENTRY',
                payload: {
                  id: uuidv4(),
                  timestamp: Date.now(),
                  pair: `${config.crypto2}/${config.preferredStablecoin}`,
                  crypto1: config.crypto2,
                  orderType: "SELL",
                  amountCrypto1: amountCrypto2ToUse,
                  avgPrice: crypto2StablecoinPrice,
                  valueCrypto2: stablecoinObtained,
                  price1: crypto2StablecoinPrice,
                  crypto1Symbol: config.crypto2 || '',
                  crypto2Symbol: config.preferredStablecoin || ''
                }
              });

              dispatch({
                type: 'ADD_ORDER_HISTORY_ENTRY',
                payload: {
                  id: uuidv4(),
                  timestamp: Date.now(),
                  pair: `${config.crypto1}/${config.preferredStablecoin}`,
                  crypto1: config.crypto1,
                  orderType: "BUY",
                  amountCrypto1: crypto1Bought,
                  avgPrice: crypto1StablecoinPrice,
                  valueCrypto2: stablecoinObtained,
                  price1: crypto1StablecoinPrice,
                  crypto1Symbol: config.crypto1 || '',
                  crypto2Symbol: config.preferredStablecoin || ''
                }
              });

              console.log(`✅ STABLECOIN BUY: Counter ${activeRow.counter} | Step 1: Sold ${amountCrypto2ToUse} ${config.crypto2} → ${stablecoinObtained.toFixed(2)} ${config.preferredStablecoin} | Step 2: Bought ${crypto1Bought.toFixed(6)} ${config.crypto1} | Level: ${activeRow.orderLevel} → ${newLevel}`);
              toast({ title: "BUY Executed (Stablecoin)", description: `Counter ${activeRow.counter}: ${crypto1Bought.toFixed(6)} ${config.crypto1} via ${config.preferredStablecoin}`, duration: 2000 });
              playSound('soundOrderExecution');

              // Send Telegram notification for Stablecoin BUY
              sendTelegramNotification(
                `🟢 <b>BUY EXECUTED (Stablecoin Swap)</b>\n` +
                `📊 Counter: ${activeRow.counter}\n` +
                `🔄 Step 1: Sold ${amountCrypto2ToUse.toFixed(2)} ${config.crypto2} → ${stablecoinObtained.toFixed(2)} ${config.preferredStablecoin}\n` +
                `🔄 Step 2: Bought ${crypto1Bought.toFixed(6)} ${config.crypto1}\n` +
                `📊 Level: ${activeRow.orderLevel} → ${newLevel}\n` +
                `📈 Mode: Stablecoin Swap`
              );

              actionsTaken++;

              // Update local balance for subsequent checks in this cycle
              currentCrypto2Balance -= amountCrypto2ToUse;
              currentCrypto1Balance += crypto1Bought;
            } else {
              // Insufficient balance - send Telegram notification
              console.log(`❌ Insufficient ${config.crypto2} balance for stablecoin swap: ${currentCrypto2Balance.toFixed(6)} < ${amountCrypto2ToUse.toFixed(6)}`);
              sendLowBalanceNotification(config.crypto2, currentCrypto2Balance, amountCrypto2ToUse).catch(console.error);
            }
          }

          // STEP 3: Check and Process Inferior TargetRow (TargetRowN_minus_1) - ALWAYS, regardless of BUY
          const currentCounter = activeRow.counter;
          const inferiorRow = sortedRowsForLogic.find(row => row.counter === currentCounter - 1);

          console.log(`🔍 StablecoinSwap SELL Check:`, {
            currentCounter: currentCounter,
            inferiorRowFound: !!inferiorRow,
            inferiorRowStatus: inferiorRow?.status,
            inferiorRowCrypto1Held: inferiorRow?.crypto1AmountHeld,
            inferiorRowOriginalCost: inferiorRow?.originalCostCrypto2,
            canExecuteSell: !!(inferiorRow && inferiorRow.status === "Full" && inferiorRow.crypto1AmountHeld && inferiorRow.originalCostCrypto2)
          });

          if (inferiorRow && inferiorRow.status === "Full" && inferiorRow.crypto1AmountHeld && inferiorRow.originalCostCrypto2) {
            // Execute Two-Step "Sell Crypto1 & Reacquire Crypto2 via Stablecoin" for TargetRowN_minus_1
            const amountCrypto1ToSell = inferiorRow.crypto1AmountHeld;

            // Step A: Sell Crypto1 for PreferredStablecoin
            // Use current fluctuating market price for realistic P/L
            const crypto1USDPrice = getUSDPrice(config.crypto1 || 'BTC');
            const crypto2USDPrice = getUSDPrice(config.crypto2 || 'USDT');
            const stablecoinUSDPrice = getUSDPrice(config.preferredStablecoin || 'USDT');

            // Apply current market price fluctuation to Crypto1 price
            const fluctuatedCrypto1Price = crypto1USDPrice * (state.currentMarketPrice / (crypto1USDPrice / crypto2USDPrice));
            const crypto1StablecoinPrice = fluctuatedCrypto1Price / stablecoinUSDPrice;
            const stablecoinFromC1Sell = amountCrypto1ToSell * crypto1StablecoinPrice;

            // Step B: Buy Crypto2 with Stablecoin
            const crypto2StablecoinPrice = crypto2USDPrice / stablecoinUSDPrice;
            const crypto2Reacquired = stablecoinFromC1Sell / crypto2StablecoinPrice;

            // Calculate realized profit in Crypto2
            // We compare the value of crypto2 we got back vs what we originally spent
            const originalCost = inferiorRow.originalCostCrypto2 || 0;
            const realizedProfitInCrypto2 = crypto2Reacquired - originalCost;

            // Calculate Crypto1 profit/loss - this should be the profit in terms of Crypto1
            // For StablecoinSwap, we need to calculate how much Crypto1 profit this represents
            const realizedProfitCrypto1 = crypto1StablecoinPrice > 0 ? realizedProfitInCrypto2 / crypto1StablecoinPrice : 0;

            // IMPORTANT: If P/L is exactly 0, it might indicate a price calculation issue
            if (realizedProfitInCrypto2 === 0) {
              console.warn(`⚠️ P/L is exactly 0 - this might indicate an issue:
                - Are prices changing between BUY and SELL?
                - Is the market price fluctuation working?
                - Current market price: ${state.currentMarketPrice}`);
            }

            console.log(`💰 StablecoinSwap P/L Calculation DETAILED:
              - Inferior Row Counter: ${inferiorRow.counter}
              - Original Cost (Crypto2): ${originalCost}
              - Crypto2 Reacquired: ${crypto2Reacquired}
              - Realized P/L (Crypto2): ${realizedProfitInCrypto2}
              - Crypto1 Stablecoin Price: ${crypto1StablecoinPrice}
              - Realized P/L (Crypto1): ${realizedProfitCrypto1}
              - Is Profitable: ${realizedProfitInCrypto2 > 0 ? 'YES' : 'NO'}
              - Calculation: ${crypto2Reacquired} - ${originalCost} = ${realizedProfitInCrypto2}`);

            // Update Row N-1: Full → Free, Level UNCHANGED, Vars cleared
            const updatedInferiorRow: TargetPriceRow = {
              ...inferiorRow,
              status: "Free", // Full → Free
              // orderLevel: REMAINS UNCHANGED per specification
              crypto1AmountHeld: undefined, // Clear held amount
              originalCostCrypto2: undefined, // Clear original cost
              valueLevel: config.baseBid * Math.pow(config.multiplier, inferiorRow.orderLevel), // Recalculate value based on current level
              crypto1Var: 0, // Clear to 0.00 (Free status)
              crypto2Var: 0, // Clear to 0.00 (Free status)
            };

            dispatch({ type: 'UPDATE_TARGET_PRICE_ROW', payload: updatedInferiorRow });
            dispatch({ type: 'UPDATE_BALANCES', payload: { crypto1: currentCrypto1Balance - amountCrypto1ToSell, crypto2: currentCrypto2Balance + crypto2Reacquired } });

            // Add history entries for both steps of the N-1 stablecoin swap
            // Step A: SELL Crypto1 for Stablecoin (with profit/loss data for analytics)
            console.log(`📊 Adding StablecoinSwap SELL order to history with P/L:`, {
              realizedProfitLossCrypto2: realizedProfitInCrypto2,
              realizedProfitLossCrypto1: realizedProfitCrypto1,
              pair: `${config.crypto1}/${config.preferredStablecoin}`,
              orderType: "SELL",
              amountCrypto1: amountCrypto1ToSell,
              avgPrice: crypto1StablecoinPrice,
              valueCrypto2: stablecoinFromC1Sell,
              price1: crypto1StablecoinPrice,
              crypto1Symbol: config.crypto1,
              crypto2Symbol: config.preferredStablecoin
            });

            dispatch({
              type: 'ADD_ORDER_HISTORY_ENTRY',
              payload: {
                id: uuidv4(),
                timestamp: Date.now(),
                pair: `${config.crypto1}/${config.preferredStablecoin}`,
                crypto1: config.crypto1,
                orderType: "SELL",
                amountCrypto1: amountCrypto1ToSell,
                avgPrice: crypto1StablecoinPrice,
                valueCrypto2: stablecoinFromC1Sell,
                price1: crypto1StablecoinPrice,
                crypto1Symbol: config.crypto1 || '',
                crypto2Symbol: config.preferredStablecoin || ''
              }
            });

            // Step B: BUY Crypto2 with Stablecoin (no profit/loss as it's just conversion)
            dispatch({
              type: 'ADD_ORDER_HISTORY_ENTRY',
              payload: {
                id: uuidv4(),
                timestamp: Date.now(),
                pair: `${config.crypto2}/${config.preferredStablecoin}`,
                crypto1: config.crypto2,
                orderType: "BUY",
                amountCrypto1: crypto2Reacquired,
                avgPrice: crypto2StablecoinPrice,
                valueCrypto2: stablecoinFromC1Sell,
                price1: crypto2StablecoinPrice,
                crypto1Symbol: config.crypto2 || '',
                crypto2Symbol: config.preferredStablecoin || ''
              }
            });

            console.log(`✅ STABLECOIN SELL: Counter ${currentCounter - 1} | Step A: Sold ${amountCrypto1ToSell.toFixed(6)} ${config.crypto1} → ${stablecoinFromC1Sell.toFixed(2)} ${config.preferredStablecoin} | Step B: Bought ${crypto2Reacquired.toFixed(2)} ${config.crypto2} | Profit: ${realizedProfitInCrypto2.toFixed(2)} ${config.crypto2} | Level: ${inferiorRow.orderLevel} (unchanged)`);
            toast({ title: "SELL Executed (Stablecoin)", description: `Counter ${currentCounter - 1}: Profit ${realizedProfitInCrypto2.toFixed(2)} ${config.crypto2} via ${config.preferredStablecoin}`, duration: 2000 });
            playSound('soundOrderExecution');

            // Send Telegram notification for Stablecoin SELL
            const profitEmoji = realizedProfitInCrypto2 > 0 ? '📈' : realizedProfitInCrypto2 < 0 ? '📉' : '➖';
            sendTelegramNotification(
              `🔴 <b>SELL EXECUTED (Stablecoin Swap)</b>\n` +
              `📊 Counter: ${currentCounter - 1}\n` +
              `🔄 Step A: Sold ${amountCrypto1ToSell.toFixed(6)} ${config.crypto1} → ${stablecoinFromC1Sell.toFixed(2)} ${config.preferredStablecoin}\n` +
              `🔄 Step B: Bought ${crypto2Reacquired.toFixed(2)} ${config.crypto2}\n` +
              `${profitEmoji} Profit: ${realizedProfitInCrypto2.toFixed(2)} ${config.crypto2}\n` +
              `📊 Level: ${inferiorRow.orderLevel} (unchanged)\n` +
              `📈 Mode: Stablecoin Swap`
            );

            actionsTaken++;

            // Update local balance for subsequent checks in this cycle
            currentCrypto1Balance -= amountCrypto1ToSell;
            currentCrypto2Balance += crypto2Reacquired;
          }
        }
      }
    }

    if (actionsTaken > 0) {
      console.log(`🎯 CYCLE COMPLETE: ${actionsTaken} actions taken at price $${currentMarketPrice.toFixed(2)}`);
    }

  }, [state.botSystemStatus, state.currentMarketPrice, state.targetPriceRows, state.config, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, dispatch, toast, playSound, sendTelegramNotification]);


  const getDisplayOrders = useCallback((): DisplayOrderRow[] => {
    if (!state.targetPriceRows || !Array.isArray(state.targetPriceRows)) {
      return [];
    }

    return state.targetPriceRows.map((row: TargetPriceRow) => {
      const currentPrice = state.currentMarketPrice || 0;
      const targetPrice = row.targetPrice || 0;
      const percentFromActualPrice = currentPrice && targetPrice
        ? ((currentPrice / targetPrice) - 1) * 100
        : 0;

      let incomeCrypto1: number | undefined;
      let incomeCrypto2: number | undefined;

      if (row.status === "Full" && row.crypto1AmountHeld && row.originalCostCrypto2) {
        const totalUnrealizedProfitInCrypto2 = (currentPrice * row.crypto1AmountHeld) - row.originalCostCrypto2;
        incomeCrypto2 = (totalUnrealizedProfitInCrypto2 * state.config.incomeSplitCrypto2Percent) / 100;
        if (currentPrice > 0) {
          incomeCrypto1 = (totalUnrealizedProfitInCrypto2 * state.config.incomeSplitCrypto1Percent / 100) / currentPrice;
        }
      }

      return {
        ...row,
        currentPrice,
        priceDifference: targetPrice - currentPrice,
        priceDifferencePercent: currentPrice > 0 ? ((targetPrice - currentPrice) / currentPrice) * 100 : 0,
        potentialProfitCrypto1: (state.config.incomeSplitCrypto1Percent / 100) * row.valueLevel / (targetPrice || 1),
        potentialProfitCrypto2: (state.config.incomeSplitCrypto2Percent / 100) * row.valueLevel,
        percentFromActualPrice,
        incomeCrypto1,
        incomeCrypto2,
      };
    }).sort((a: DisplayOrderRow, b: DisplayOrderRow) => b.targetPrice - a.targetPrice);
  }, [state.targetPriceRows, state.currentMarketPrice, state.config.incomeSplitCrypto1Percent, state.config.incomeSplitCrypto2Percent, state.config.baseBid, state.config.multiplier]);


  // Backend Integration Functions
  const saveConfigToBackend = useCallback(async (config: TradingConfig): Promise<string | null> => {
    try {
      const configData = {
        name: `${config.crypto1}/${config.crypto2} ${config.tradingMode}`,
        tradingMode: config.tradingMode,
        crypto1: config.crypto1,
        crypto2: config.crypto2,
        baseBid: config.baseBid,
        multiplier: config.multiplier,
        numDigits: config.numDigits,
        slippagePercent: config.slippagePercent,
        incomeSplitCrypto1Percent: config.incomeSplitCrypto1Percent,
        incomeSplitCrypto2Percent: config.incomeSplitCrypto2Percent,
        preferredStablecoin: config.preferredStablecoin,
        targetPrices: state.targetPriceRows.map(row => row.targetPrice)
      };

      const response = await tradingApi.saveConfig(configData);
      console.log('✅ Config saved to backend:', response);
      return response.config?.id || null;
    } catch (error) {
      console.error('❌ Failed to save config to backend:', error);
      toast({
        title: "Backend Error",
        description: "Failed to save configuration to backend",
        variant: "destructive",
        duration: 3000
      });
      return null;
    }
  }, [state.targetPriceRows, toast]);

  const startBackendBot = useCallback(async (configId: string): Promise<boolean> => {
    try {
      const response = await tradingApi.startBot(configId);
      console.log('✅ Bot started on backend:', response);
      toast({
        title: "Bot Started",
        description: "Trading bot started successfully on backend",
        duration: 3000
      });
      return true;
    } catch (error) {
      console.error('❌ Failed to start bot on backend:', error);
      toast({
        title: "Backend Error",
        description: "Failed to start bot on backend",
        variant: "destructive",
        duration: 3000
      });
      return false;
    }
  }, [toast]);

  const stopBackendBot = useCallback(async (configId: string): Promise<boolean> => {
    try {
      const response = await tradingApi.stopBot(configId);
      console.log('✅ Bot stopped on backend:', response);
      toast({
        title: "Bot Stopped",
        description: "Trading bot stopped successfully on backend",
        duration: 3000
      });
      return true;
    } catch (error) {
      console.error('❌ Failed to stop bot on backend:', error);
      toast({
        title: "Backend Error",
        description: "Failed to stop bot on backend",
        variant: "destructive",
        duration: 3000
      });
      return false;
    }
  }, [toast]);

  const checkBackendStatus = useCallback(async (): Promise<void> => {
    const apiUrl = process.env.NEXT_PUBLIC_API_URL;
    if (!apiUrl) {
      console.error('Error: NEXT_PUBLIC_API_URL is not defined. Backend connectivity check cannot be performed.');
      dispatch({ type: 'SET_BACKEND_STATUS', payload: 'offline' });
      return;
    }
    try {
      const healthResponse = await fetch(`${apiUrl}/health/`);
      if (!healthResponse.ok) {
        // Log more details if the response was received but not OK
        console.error(`Backend health check failed with status: ${healthResponse.status} ${healthResponse.statusText}`);
        const responseText = await healthResponse.text().catch(() => 'Could not read response text.');
        console.error('Backend health check response body:', responseText);
      }
      dispatch({ type: 'SET_BACKEND_STATUS', payload: healthResponse.ok ? 'online' : 'offline' });
    } catch (error: any) {
      dispatch({ type: 'SET_BACKEND_STATUS', payload: 'offline' });
      console.error('Backend connectivity check failed. Error details:', error);
      if (error.cause) {
        console.error('Fetch error cause:', error.cause);
      }
      // It's also useful to log the apiUrl to ensure it's what we expect
      console.error('Attempted to fetch API URL:', `${apiUrl}/health/`);
    }
  }, [dispatch]);

  // Initialize backend status check (one-time only)
  useEffect(() => {
    // Initial check for backend status only
    checkBackendStatus();
  }, [checkBackendStatus]);

  // Save state to localStorage whenever it changes
  useEffect(() => {
    saveStateToLocalStorage(state);
  }, [state]);


  // Effect to handle bot warm-up period (immediate execution)
  useEffect(() => {
    if (state.botSystemStatus === 'WarmingUp') {
      console.log("Bot is Warming Up... Immediate execution enabled.");
      // Immediate transition to Running state - no delays
      dispatch({ type: 'SYSTEM_COMPLETE_WARMUP' });
      console.log("Bot is now Running immediately.");
    }
  }, [state.botSystemStatus, dispatch]);

  // Effect to handle session runtime tracking
  useEffect(() => {
    const sessionManager = SessionManager.getInstance();
    const currentSessionId = sessionManager.getCurrentSessionId();

    if (currentSessionId) {
      if (state.botSystemStatus === 'Running') {
        // Bot started running, start runtime tracking
        sessionManager.startSessionRuntime(currentSessionId);
        console.log('✅ Started runtime tracking for session:', currentSessionId);
      } else if (state.botSystemStatus === 'Stopped') {
        // Bot stopped, stop runtime tracking
        sessionManager.stopSessionRuntime(currentSessionId);
        console.log('⏹️ Stopped runtime tracking for session:', currentSessionId);
      }
    }
  }, [state.botSystemStatus]);

  // Auto-create session when bot actually starts running (not during warmup)
  useEffect(() => {
    const sessionManager = SessionManager.getInstance();

    // Check if we need to create a session when bot actually starts running
    if (state.botSystemStatus === 'Running' && !sessionManager.getCurrentSessionId()) {
      // Only create if we have valid crypto configuration AND target prices set
      // This prevents auto-creation for fresh windows
      if (state.config.crypto1 && state.config.crypto2 && state.targetPriceRows.length > 0) {
        sessionManager.createNewSessionWithAutoName(state.config)
          .then((sessionId) => {
            sessionManager.setCurrentSession(sessionId);
            console.log('✅ Auto-created session when bot started:', sessionId);
          })
          .catch((error) => {
            console.error('❌ Failed to auto-create session:', error);
          });
      }
    }

    // Handle runtime tracking based on bot status
    const currentSessionId = sessionManager.getCurrentSessionId();
    if (currentSessionId) {
      if (state.botSystemStatus === 'Running') {
        // Start runtime tracking when bot becomes active
        sessionManager.startSessionRuntime(currentSessionId);
        console.log('⏱️ Started runtime tracking for session:', currentSessionId);
      } else if (state.botSystemStatus === 'Stopped') {
        // Stop runtime tracking when bot stops
        sessionManager.stopSessionRuntime(currentSessionId);
        console.log('⏹️ Stopped runtime tracking for session:', currentSessionId);
      }
    }
  }, [state.botSystemStatus, state.config.crypto1, state.config.crypto2]);

  // Handle crypto pair changes during active trading
  useEffect(() => {
    const sessionManager = SessionManager.getInstance();
    const currentSessionId = sessionManager.getCurrentSessionId();

    if (currentSessionId) {
      const currentSession = sessionManager.loadSession(currentSessionId);

      // Check if crypto pair has changed from the current session
      if (currentSession &&
          (currentSession.config.crypto1 !== state.config.crypto1 ||
           currentSession.config.crypto2 !== state.config.crypto2)) {

        console.log('🔄 Crypto pair changed during session, auto-saving and resetting...');

        // Auto-save current session if bot was running or has data
        if (state.botSystemStatus === 'Running' ||
            state.targetPriceRows.length > 0 ||
            state.orderHistory.length > 0) {

          // Stop the bot if it's running
          if (state.botSystemStatus === 'Running') {
            stopBackendBot(currentSessionId);
          }

          // Save current session with timestamp
          const timestamp = new Date().toLocaleString('en-US', {
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            hour12: false
          });
          const savedName = `${currentSession.name} (AutoSaved ${timestamp})`;

          sessionManager.createNewSession(savedName, currentSession.config)
            .then(async (savedSessionId) => {
              await sessionManager.saveSession(
                savedSessionId,
                currentSession.config,
                state.targetPriceRows,
                state.orderHistory,
                state.currentMarketPrice,
                state.crypto1Balance,
                state.crypto2Balance,
                state.stablecoinBalance,
                false
              );
              console.log('💾 AutoSaved session:', savedName);
            });
        }

        // Reset for new crypto pair
        dispatch({ type: 'RESET_FOR_NEW_CRYPTO' });

        // Only create new session for the new crypto pair if bot was actually running
        if (state.config.crypto1 && state.config.crypto2 && state.botSystemStatus === 'Running') {
          const currentBalances = {
            crypto1: state.crypto1Balance,
            crypto2: state.crypto2Balance,
            stablecoin: state.stablecoinBalance
          };
          sessionManager.createNewSessionWithAutoName(state.config, undefined, currentBalances)
            .then((newSessionId) => {
              sessionManager.setCurrentSession(newSessionId);
              console.log('🆕 Created new session for crypto pair:', state.config.crypto1, '/', state.config.crypto2, 'with balances:', currentBalances);

              toast({
                title: "Crypto Pair Changed",
                description: `Previous session AutoSaved. New session created for ${state.config.crypto1}/${state.config.crypto2}`,
                duration: 5000
              });
            });
        } else {
          // If bot wasn't running, just clear the current session without creating a new one
          sessionManager.clearCurrentSession();
          console.log('🔄 Crypto pair changed but bot wasn\'t running - cleared current session');
        }
      }
    }
  }, [state.config.crypto1, state.config.crypto2]);

  // Initialize network monitoring and auto-save
  useEffect(() => {
    const networkMonitor = NetworkMonitor.getInstance();
    const autoSaveManager = AutoSaveManager.getInstance();
    const memoryMonitor = MemoryMonitor.getInstance();
    const sessionManager = SessionManager.getInstance();

    // Set up network status listener
    const unsubscribeNetwork = networkMonitor.addListener((isOnline, isInitial) => {
      console.log(`🌐 Network status changed: ${isOnline ? 'Online' : 'Offline'}`);

      // Handle offline state - stop bot and save session
      if (!isOnline && !isInitial) {
        // Stop the bot if it's running
        if (state.botSystemStatus === 'Running') {
          console.log('🔴 Internet lost - stopping bot and saving session');
          dispatch({ type: 'SYSTEM_STOP_BOT' });

          // Auto-save current session
          const sessionManager = SessionManager.getInstance();
          const currentSessionId = sessionManager.getCurrentSessionId();
          if (currentSessionId) {
            const currentSession = sessionManager.loadSession(currentSessionId);
            if (currentSession) {
              // Create offline backup session
              const timestamp = new Date().toLocaleString('en-US', {
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                hour12: false
              });
              const offlineName = `${currentSession.name} (Offline Backup ${timestamp})`;

              sessionManager.createNewSession(offlineName, currentSession.config)
                .then(async (backupSessionId) => {
                  await sessionManager.saveSession(
                    backupSessionId,
                    state.config,
                    state.targetPriceRows,
                    state.orderHistory,
                    state.currentMarketPrice,
                    state.crypto1Balance,
                    state.crypto2Balance,
                    state.stablecoinBalance,
                    false
                  );
                  console.log('💾 Created offline backup session:', offlineName);
                });
            }
          }
        }

        toast({
          title: "Network Disconnected",
          description: "Bot stopped and session saved. Trading paused until connection restored.",
          variant: "destructive",
          duration: 8000
        });
      } else if (isOnline && !isInitial) {
        toast({
          title: "Network Reconnected",
          description: "Connection restored. You can resume trading.",
          duration: 3000
        });
      }
    });

    // Set up memory monitoring
    const unsubscribeMemory = memoryMonitor.addListener((memory) => {
      const usedMB = memory.usedJSHeapSize / 1024 / 1024;
      if (usedMB > 150) { // 150MB threshold
        console.warn(`🧠 High memory usage: ${usedMB.toFixed(2)}MB`);
        toast({
          title: "High Memory Usage",
          description: `Memory usage is high (${usedMB.toFixed(0)}MB). Consider refreshing the page.`,
          variant: "destructive",
          duration: 5000
        });
      }
    });

    // Set up auto-save
    const saveFunction = async () => {
      try {
        // Save to session manager if we have a current session
        const currentSessionId = sessionManager.getCurrentSessionId();
        if (currentSessionId) {
          await sessionManager.saveSession(
            currentSessionId,
            state.config,
            state.targetPriceRows,
            state.orderHistory,
            state.currentMarketPrice,
            state.crypto1Balance,
            state.crypto2Balance,
            state.stablecoinBalance,
            state.botSystemStatus === 'Running'
          );
        }

        // Also save to localStorage as backup
        saveStateToLocalStorage(state);
      } catch (error) {
        console.error('Auto-save failed:', error);
      }
    };

    autoSaveManager.enable(saveFunction, 30000); // Auto-save every 30 seconds

    // Add beforeunload listener to save session on browser close/refresh
    const handleBeforeUnload = (event: BeforeUnloadEvent) => {
      // Save immediately before unload
      saveFunction();

      // If bot is running, show warning
      if (state.botSystemStatus === 'Running') {
        const message = 'Trading bot is currently running. Are you sure you want to leave?';
        event.returnValue = message;
        return message;
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    // Cleanup function
    return () => {
      unsubscribeNetwork();
      unsubscribeMemory();
      autoSaveManager.disable();
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [state, toast]);

  // Force save when bot status changes
  useEffect(() => {
    const autoSaveManager = AutoSaveManager.getInstance();
    autoSaveManager.saveNow();
  }, [state.botSystemStatus]);

  // Manual save session function
  const saveCurrentSession = useCallback(async (): Promise<boolean> => {
    try {
      const sessionManager = SessionManager.getInstance();
      const currentSessionId = sessionManager.getCurrentSessionId();

      if (!currentSessionId) {
        // No current session, only create one if bot is running or has meaningful data
        if (state.config.crypto1 && state.config.crypto2 &&
            (state.botSystemStatus === 'Running' || state.targetPriceRows.length > 0 || state.orderHistory.length > 0)) {
          const currentBalances = {
            crypto1: state.crypto1Balance,
            crypto2: state.crypto2Balance,
            stablecoin: state.stablecoinBalance
          };
          const sessionId = await sessionManager.createNewSessionWithAutoName(state.config, undefined, currentBalances);
          sessionManager.setCurrentSession(sessionId);
          await sessionManager.saveSession(
            sessionId,
            state.config,
            state.targetPriceRows,
            state.orderHistory,
            state.currentMarketPrice,
            state.crypto1Balance,
            state.crypto2Balance,
            state.stablecoinBalance,
            state.botSystemStatus === 'Running'
          );
          return true;
        }
        console.log('⚠️ No session to save - bot not running and no meaningful data');
        return false;
      }

      // Save existing session
      return await sessionManager.saveSession(
        currentSessionId,
        state.config,
        state.targetPriceRows,
        state.orderHistory,
        state.currentMarketPrice,
        state.crypto1Balance,
        state.crypto2Balance,
        state.stablecoinBalance,
        state.botSystemStatus === 'Running'
      );
    } catch (error) {
      console.error('Failed to save current session:', error);
      return false;
    }
  }, [state]);

  // Context value
  const contextValue: TradingContextType = {
    ...state,
    dispatch,
    setTargetPrices,
    getDisplayOrders,
    checkBackendStatus,
    fetchMarketPrice,
    startBackendBot,
    stopBackendBot,
    saveConfigToBackend,
    saveCurrentSession,
    backendStatus: state.backendStatus as 'online' | 'offline' | 'unknown',
    botSystemStatus: state.botSystemStatus,
    isBotActive: state.botSystemStatus === 'Running',
  };

  return (
    <TradingContext.Provider value={contextValue}>
      {children}
    </TradingContext.Provider>
  );
};

// Custom hook to use the trading context
export const useTradingContext = (): TradingContextType => {
  const context = useContext(TradingContext);
  if (context === undefined) {
    throw new Error('useTradingContext must be used within a TradingProvider');
  }
  return context;
};

