"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/history/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/SessionAwareHistory.tsx":
/*!**********************************************************!*\
  !*** ./src/components/dashboard/SessionAwareHistory.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SessionAwareHistory)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/contexts/TradingContext */ \"(app-pages-browser)/./src/contexts/TradingContext.tsx\");\n/* harmony import */ var _lib_session_manager__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/session-manager */ \"(app-pages-browser)/./src/lib/session-manager.ts\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_Clock_FileSpreadsheet_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,FileSpreadsheet,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_FileSpreadsheet_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,FileSpreadsheet,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-spreadsheet.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_FileSpreadsheet_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,FileSpreadsheet,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/format.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction SessionAwareHistory() {\n    _s();\n    const { dispatch, orderHistory, config } = (0,_contexts_TradingContext__WEBPACK_IMPORTED_MODULE_7__.useTradingContext)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast)();\n    const [sessions, setSessions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedSessionId, setSelectedSessionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('current');\n    const [selectedSessionHistory, setSelectedSessionHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_8__.SessionManager.getInstance();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SessionAwareHistory.useEffect\": ()=>{\n            loadSessions();\n        }\n    }[\"SessionAwareHistory.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SessionAwareHistory.useEffect\": ()=>{\n            if (selectedSessionId === 'current') {\n                setSelectedSessionHistory(orderHistory);\n            } else {\n                const sessionHistory = sessionManager.getSessionHistory(selectedSessionId);\n                setSelectedSessionHistory(sessionHistory);\n            }\n        }\n    }[\"SessionAwareHistory.useEffect\"], [\n        selectedSessionId,\n        orderHistory\n    ]);\n    const loadSessions = ()=>{\n        const allSessions = sessionManager.getAllSessions();\n        const currentSessionId = sessionManager.getCurrentSessionId();\n        // Filter out the current session to avoid duplicates\n        const pastSessions = allSessions.filter((session)=>session.id !== currentSessionId);\n        setSessions(pastSessions.sort((a, b)=>b.lastModified - a.lastModified));\n    };\n    const handleClearHistory = ()=>{\n        if (selectedSessionId === 'current') {\n            dispatch({\n                type: 'CLEAR_ORDER_HISTORY'\n            });\n            toast({\n                title: \"History Cleared\",\n                description: \"Current session trade history has been cleared.\"\n            });\n        } else {\n            // For past sessions, we would need to implement session history clearing\n            toast({\n                title: \"Cannot Clear\",\n                description: \"Cannot clear history for past sessions. Use current session to clear history.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleExportHistory = ()=>{\n        if (selectedSessionHistory.length === 0) {\n            toast({\n                title: \"No Data to Export\",\n                description: \"There is no trade history to export for the selected session.\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        let csvContent;\n        let filename;\n        if (selectedSessionId === 'current') {\n            // Use existing export logic for current session\n            const headers = [\n                'Date',\n                'Time',\n                'Pair',\n                'Crypto',\n                'Order Type',\n                'Amount',\n                'Avg Price',\n                'Value',\n                'Price 1',\n                'Crypto 1',\n                'Price 2',\n                'Crypto 2',\n                'Profit/Loss (Crypto1)',\n                'Profit/Loss (Crypto2)'\n            ];\n            csvContent = [\n                headers.join(','),\n                ...selectedSessionHistory.map((entry)=>{\n                    var _entry_amountCrypto1, _entry_avgPrice, _entry_valueCrypto2, _entry_price1, _entry_price2, _entry_realizedProfitLossCrypto1, _entry_realizedProfitLossCrypto2;\n                    return [\n                        new Date(entry.timestamp).toISOString().split('T')[0],\n                        new Date(entry.timestamp).toTimeString().split(' ')[0],\n                        entry.pair,\n                        entry.crypto1Symbol,\n                        entry.orderType,\n                        ((_entry_amountCrypto1 = entry.amountCrypto1) === null || _entry_amountCrypto1 === void 0 ? void 0 : _entry_amountCrypto1.toFixed(config.numDigits)) || '',\n                        ((_entry_avgPrice = entry.avgPrice) === null || _entry_avgPrice === void 0 ? void 0 : _entry_avgPrice.toFixed(config.numDigits)) || '',\n                        ((_entry_valueCrypto2 = entry.valueCrypto2) === null || _entry_valueCrypto2 === void 0 ? void 0 : _entry_valueCrypto2.toFixed(config.numDigits)) || '',\n                        ((_entry_price1 = entry.price1) === null || _entry_price1 === void 0 ? void 0 : _entry_price1.toFixed(config.numDigits)) || '',\n                        entry.crypto1Symbol,\n                        ((_entry_price2 = entry.price2) === null || _entry_price2 === void 0 ? void 0 : _entry_price2.toFixed(config.numDigits)) || '',\n                        entry.crypto2Symbol,\n                        ((_entry_realizedProfitLossCrypto1 = entry.realizedProfitLossCrypto1) === null || _entry_realizedProfitLossCrypto1 === void 0 ? void 0 : _entry_realizedProfitLossCrypto1.toFixed(config.numDigits)) || '',\n                        ((_entry_realizedProfitLossCrypto2 = entry.realizedProfitLossCrypto2) === null || _entry_realizedProfitLossCrypto2 === void 0 ? void 0 : _entry_realizedProfitLossCrypto2.toFixed(config.numDigits)) || ''\n                    ].join(',');\n                })\n            ].join('\\n');\n            filename = \"current_session_history_\".concat(new Date().toISOString().split('T')[0], \".csv\");\n        } else {\n            // Use session manager export for past sessions\n            csvContent = sessionManager.exportSessionToCSV(selectedSessionId) || '';\n            const session = sessionManager.loadSession(selectedSessionId);\n            filename = \"\".concat((session === null || session === void 0 ? void 0 : session.name) || 'session', \"_\").concat(new Date().toISOString().split('T')[0], \".csv\");\n        }\n        if (!csvContent) {\n            toast({\n                title: \"Export Failed\",\n                description: \"Failed to generate CSV content.\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        // Create and download file\n        const blob = new Blob([\n            csvContent\n        ], {\n            type: 'text/csv;charset=utf-8;'\n        });\n        const link = document.createElement('a');\n        const url = URL.createObjectURL(blob);\n        link.setAttribute('href', url);\n        link.setAttribute('download', filename);\n        link.style.visibility = 'hidden';\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        toast({\n            title: \"Export Complete\",\n            description: \"Trade history has been exported to CSV file.\"\n        });\n    };\n    const getSelectedSessionInfo = ()=>{\n        if (selectedSessionId === 'current') {\n            return {\n                name: 'Current Session',\n                pair: \"\".concat(config.crypto1, \"/\").concat(config.crypto2),\n                totalTrades: orderHistory.length,\n                totalProfitLoss: orderHistory.filter((trade)=>trade.orderType === 'SELL' && trade.realizedProfitLossCrypto2 !== undefined).reduce((sum, trade)=>sum + (trade.realizedProfitLossCrypto2 || 0), 0),\n                lastModified: Date.now(),\n                isActive: true\n            };\n        }\n        return sessions.find((s)=>s.id === selectedSessionId);\n    };\n    const selectedSession = getSelectedSessionInfo();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"border-2 border-border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"text-xl font-bold text-primary\",\n                                children: \"Session History\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                children: \"View trading history for current and past sessions.\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4 items-start sm:items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm font-medium mb-2 block\",\n                                                children: \"Select Session:\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_4__.Select, {\n                                                value: selectedSessionId,\n                                                onValueChange: setSelectedSessionId,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_4__.SelectTrigger, {\n                                                        className: \"w-full sm:w-[300px]\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_4__.SelectValue, {\n                                                            placeholder: \"Select a session\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                            lineNumber: 171,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                        lineNumber: 170,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_4__.SelectContent, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_4__.SelectItem, {\n                                                                value: \"current\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                            variant: \"default\",\n                                                                            className: \"text-xs\",\n                                                                            children: \"Current\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                                            lineNumber: 176,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: [\n                                                                                \"Current Session (\",\n                                                                                config.crypto1 && config.crypto2 ? \"\".concat(config.crypto1, \"/\").concat(config.crypto2) : 'Crypto 1/Crypto 2 = 0',\n                                                                                \")\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                                            lineNumber: 177,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                                    lineNumber: 175,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                                lineNumber: 174,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            sessions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_6__.Separator, {\n                                                                        className: \"my-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                                        lineNumber: 182,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    sessions.map((session)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_4__.SelectItem, {\n                                                                            value: session.id,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center gap-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                                        variant: session.isActive ? \"default\" : \"secondary\",\n                                                                                        className: \"text-xs\",\n                                                                                        children: session.isActive ? \"Current\" : \"Past\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                                                        lineNumber: 186,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: session.name\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                                                        lineNumber: 189,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                                                lineNumber: 185,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, session.id, false, {\n                                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                                            lineNumber: 184,\n                                                                            columnNumber: 25\n                                                                        }, this))\n                                                                ]\n                                                            }, void 0, true)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                        lineNumber: 173,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                lineNumber: 169,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                onClick: handleClearHistory,\n                                                className: \"btn-outline-neo\",\n                                                disabled: selectedSessionId !== 'current',\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_FileSpreadsheet_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                        lineNumber: 207,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"Clear History\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                onClick: handleExportHistory,\n                                                className: \"btn-outline-neo\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_FileSpreadsheet_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                        lineNumber: 211,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"Export\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 11\n                            }, this),\n                            selectedSession && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-muted/50 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-muted-foreground\",\n                                                        children: \"Session:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                        lineNumber: 222,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium\",\n                                                        children: selectedSession.name\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                        lineNumber: 223,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-muted-foreground\",\n                                                        children: \"Pair:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                        lineNumber: 226,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium\",\n                                                        children: selectedSession.pair\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                        lineNumber: 227,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-muted-foreground\",\n                                                        children: \"Total Trades:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                        lineNumber: 230,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium\",\n                                                        children: selectedSession.totalTrades\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                        lineNumber: 231,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                lineNumber: 229,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-muted-foreground\",\n                                                        children: \"Total P/L:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                        lineNumber: 234,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium \".concat(selectedSession.totalProfitLoss >= 0 ? 'text-green-600' : 'text-red-600'),\n                                                        children: selectedSession.totalProfitLoss.toFixed(4)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                        lineNumber: 235,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 15\n                                    }, this),\n                                    selectedSessionId !== 'current' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-2 text-xs text-muted-foreground\",\n                                        children: [\n                                            \"Last modified: \",\n                                            (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_12__.format)(new Date(selectedSession.lastModified), 'MMM dd, yyyy HH:mm')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                lineNumber: 160,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"border-2 border-border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"text-lg font-bold text-primary\",\n                                children: [\n                                    \"Trade History - \",\n                                    (selectedSession === null || selectedSession === void 0 ? void 0 : selectedSession.name) || 'Unknown Session'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                lineNumber: 253,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                children: selectedSessionHistory.length === 0 ? \"No trades recorded for this session yet.\" : \"Showing \".concat(selectedSessionHistory.length, \" trades for the selected session.\")\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                        lineNumber: 252,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SessionHistoryTable, {\n                            history: selectedSessionHistory,\n                            config: config\n                        }, void 0, false, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                            lineNumber: 265,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                        lineNumber: 263,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                lineNumber: 251,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n        lineNumber: 158,\n        columnNumber: 5\n    }, this);\n}\n_s(SessionAwareHistory, \"pWp7ZU6XY5YR2imJ+wjTWvIkU9c=\", false, function() {\n    return [\n        _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_7__.useTradingContext,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast\n    ];\n});\n_c = SessionAwareHistory;\n// Custom history table component for session-specific history\nfunction SessionHistoryTable(param) {\n    let { history, config } = param;\n    const formatNum = (num)=>{\n        var _num_toFixed;\n        return (_num_toFixed = num === null || num === void 0 ? void 0 : num.toFixed(config.numDigits)) !== null && _num_toFixed !== void 0 ? _num_toFixed : '-';\n    };\n    const columns = [\n        {\n            key: \"date\",\n            label: \"Date\"\n        },\n        {\n            key: \"hour\",\n            label: \"Hour\"\n        },\n        {\n            key: \"pair\",\n            label: \"Couple\"\n        },\n        {\n            key: \"crypto\",\n            label: \"Crypto (\".concat(config.crypto1, \")\")\n        },\n        {\n            key: \"orderType\",\n            label: \"Order Type\"\n        },\n        {\n            key: \"amount\",\n            label: \"Amount\"\n        },\n        {\n            key: \"avgPrice\",\n            label: \"Avg Price\"\n        },\n        {\n            key: \"value\",\n            label: \"Value (\".concat(config.crypto2, \")\")\n        },\n        {\n            key: \"price1\",\n            label: \"Price 1\"\n        },\n        {\n            key: \"crypto1Symbol\",\n            label: \"Crypto (\".concat(config.crypto1, \")\")\n        },\n        {\n            key: \"price2\",\n            label: \"Price 2\"\n        },\n        {\n            key: \"crypto2Symbol\",\n            label: \"Crypto (\".concat(config.crypto2, \")\")\n        }\n    ];\n    if (history.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-8 text-muted-foreground\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_FileSpreadsheet_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    className: \"h-12 w-12 mx-auto mb-4 opacity-50\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                    lineNumber: 294,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: \"No trading history for this session yet.\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                    lineNumber: 295,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n            lineNumber: 293,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"border-2 border-border rounded-sm\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"overflow-x-auto\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                className: \"min-w-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                            className: \"bg-card hover:bg-card border-b\",\n                            children: columns.map((col)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"font-bold text-foreground whitespace-nowrap px-3 py-2 text-sm text-left\",\n                                    children: col.label\n                                }, col.key, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                            lineNumber: 305,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                        lineNumber: 304,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                        children: history.map((entry)=>{\n                            var _formatNum;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                className: \"hover:bg-card/80 border-b\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-3 py-2 text-xs\",\n                                        children: (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_12__.format)(new Date(entry.timestamp), 'yyyy-MM-dd')\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-3 py-2 text-xs\",\n                                        children: (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_12__.format)(new Date(entry.timestamp), 'HH:mm:ss')\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-3 py-2 text-xs\",\n                                        children: entry.pair\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                        lineNumber: 316,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-3 py-2 text-xs\",\n                                        children: entry.crypto1Symbol\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-3 py-2 text-xs font-semibold \".concat(entry.orderType === \"BUY\" ? \"text-green-400\" : \"text-destructive\"),\n                                        children: entry.orderType\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                        lineNumber: 318,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-3 py-2 text-xs\",\n                                        children: formatNum(entry.amountCrypto1)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                        lineNumber: 321,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-3 py-2 text-xs\",\n                                        children: formatNum(entry.avgPrice)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-3 py-2 text-xs\",\n                                        children: formatNum(entry.valueCrypto2)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                        lineNumber: 323,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-3 py-2 text-xs\",\n                                        children: formatNum(entry.price1)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                        lineNumber: 324,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-3 py-2 text-xs\",\n                                        children: entry.crypto1Symbol\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-3 py-2 text-xs\",\n                                        children: (_formatNum = formatNum(entry.price2)) !== null && _formatNum !== void 0 ? _formatNum : \"-\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-3 py-2 text-xs\",\n                                        children: entry.crypto2Symbol\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, entry.id, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 15\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                        lineNumber: 311,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                lineNumber: 303,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n            lineNumber: 302,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n        lineNumber: 301,\n        columnNumber: 5\n    }, this);\n}\n_c1 = SessionHistoryTable;\nvar _c, _c1;\n$RefreshReg$(_c, \"SessionAwareHistory\");\n$RefreshReg$(_c1, \"SessionHistoryTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/SessionAwareHistory.tsx\n"));

/***/ })

});