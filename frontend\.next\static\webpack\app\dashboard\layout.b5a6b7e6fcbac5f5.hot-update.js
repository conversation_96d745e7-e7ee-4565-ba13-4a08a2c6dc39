"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./src/contexts/TradingContext.tsx":
/*!*****************************************!*\
  !*** ./src/contexts/TradingContext.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TradingProvider: () => (/* binding */ TradingProvider),\n/* harmony export */   useTradingContext: () => (/* binding */ useTradingContext)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/types */ \"(app-pages-browser)/./src/lib/types.tsx\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/session-manager */ \"(app-pages-browser)/./src/lib/session-manager.ts\");\n/* harmony import */ var _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/network-monitor */ \"(app-pages-browser)/./src/lib/network-monitor.ts\");\n/* __next_internal_client_entry_do_not_use__ TradingProvider,useTradingContext auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n// Define locally to avoid import issues\nconst DEFAULT_QUOTE_CURRENCIES = [\n    \"USDT\",\n    \"USDC\",\n    \"BTC\"\n];\n\n\n\n\n// Add this function to calculate the initial market price\nconst calculateInitialMarketPrice = (config)=>{\n    // Return 0 if either crypto is not selected\n    if (!config.crypto1 || !config.crypto2) {\n        return 0;\n    }\n    // Default fallback value for valid pairs\n    return calculateFallbackMarketPrice(config);\n};\n// Get real market price from backend (which uses actual Binance API with credentials)\nconst getRealBinancePrice = async (config)=>{\n    try {\n        // Return 0 if either crypto is not selected\n        if (!config.crypto1 || !config.crypto2) {\n            return 0;\n        }\n        const pair = \"\".concat(config.crypto1, \"/\").concat(config.crypto2);\n        try {\n            // Use backend endpoint which connects to real Binance API with credentials\n            const symbol = pair.replace('/', '-'); // Convert BTC/USDT to BTC-USDT for API\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.tradingApi.getMarketPrice(symbol);\n            if (response && response.price > 0) {\n                console.log(\"✅ Real Binance price: \".concat(config.crypto1, \"/\").concat(config.crypto2, \" = \").concat(response.price));\n                return response.price;\n            }\n        } catch (error) {\n            console.error(\"❌ Backend Binance API failed for \".concat(pair, \":\"), error);\n            throw error; // Don't fall back to other APIs, we want real Binance prices only\n        }\n        throw new Error('Failed to get real Binance price');\n    } catch (error) {\n        console.error('❌ Error fetching real Binance price:', error);\n        throw error;\n    }\n};\n// Helper function to map crypto symbols to CoinGecko IDs\nconst getCoinGeckoId = (symbol)=>{\n    const mapping = {\n        'BTC': 'bitcoin',\n        'ETH': 'ethereum',\n        'SOL': 'solana',\n        'ADA': 'cardano',\n        'DOT': 'polkadot',\n        'MATIC': 'matic-network',\n        'AVAX': 'avalanche-2',\n        'LINK': 'chainlink',\n        'UNI': 'uniswap',\n        'USDT': 'tether',\n        'USDC': 'usd-coin',\n        'BUSD': 'binance-usd',\n        'DAI': 'dai'\n    };\n    return mapping[symbol.toUpperCase()] || null;\n};\n// Helper function to get stablecoin exchange rates for real market data\nconst getStablecoinExchangeRate = async (crypto, stablecoin)=>{\n    try {\n        // For stablecoin-to-stablecoin, assume 1:1 rate\n        if (getCoinGeckoId(crypto) && getCoinGeckoId(stablecoin)) {\n            const cryptoId = getCoinGeckoId(crypto);\n            const stablecoinId = getCoinGeckoId(stablecoin);\n            if (cryptoId === stablecoinId) return 1.0; // Same currency\n            // Get real exchange rate from CoinGecko\n            const response = await fetch(\"https://api.coingecko.com/api/v3/simple/price?ids=\".concat(cryptoId, \"&vs_currencies=\").concat(stablecoinId));\n            if (response.ok) {\n                var _data_cryptoId;\n                const data = await response.json();\n                const rate = cryptoId && stablecoinId ? (_data_cryptoId = data[cryptoId]) === null || _data_cryptoId === void 0 ? void 0 : _data_cryptoId[stablecoinId] : null;\n                if (rate > 0) {\n                    console.log(\"\\uD83D\\uDCCA Stablecoin rate: \".concat(crypto, \"/\").concat(stablecoin, \" = \").concat(rate));\n                    return rate;\n                }\n            }\n        }\n        // Fallback: calculate via USD prices\n        const cryptoUSDPrice = getUSDPrice(crypto);\n        const stablecoinUSDPrice = getUSDPrice(stablecoin);\n        const rate = cryptoUSDPrice / stablecoinUSDPrice;\n        console.log(\"\\uD83D\\uDCCA Fallback stablecoin rate: \".concat(crypto, \"/\").concat(stablecoin, \" = \").concat(rate, \" (via USD)\"));\n        return rate;\n    } catch (error) {\n        console.error('Error fetching stablecoin exchange rate:', error);\n        // Final fallback\n        const cryptoUSDPrice = getUSDPrice(crypto);\n        const stablecoinUSDPrice = getUSDPrice(stablecoin);\n        return cryptoUSDPrice / stablecoinUSDPrice;\n    }\n};\n// Real-time price cache\nlet priceCache = {};\nlet lastPriceUpdate = 0;\nconst PRICE_CACHE_DURATION = 2000; // 2 seconds cache for real-time updates\n// Get real USD price from Binance via backend\nconst getRealBinanceUSDPrice = async (crypto)=>{\n    const now = Date.now();\n    const cacheKey = crypto.toUpperCase();\n    // Return cached price if still valid (2 seconds)\n    if (priceCache[cacheKey] && now - lastPriceUpdate < PRICE_CACHE_DURATION) {\n        return priceCache[cacheKey];\n    }\n    try {\n        // Use backend API to get real Binance USD price (via USDT)\n        const pair = \"\".concat(crypto.toUpperCase(), \"-USDT\");\n        const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.tradingApi.getMarketPrice(pair);\n        if (response && response.price > 0) {\n            priceCache[cacheKey] = response.price;\n            lastPriceUpdate = now;\n            console.log(\"\\uD83D\\uDCCA Real Binance USD price for \".concat(crypto, \": $\").concat(response.price));\n            return response.price;\n        }\n    } catch (error) {\n        console.error(\"❌ Failed to get real Binance USD price for \".concat(crypto, \":\"), error);\n        throw error; // Don't fall back, we want real prices only\n    }\n    throw new Error(\"Failed to get real Binance USD price for \".concat(crypto));\n};\n// Synchronous helper function to get USD price (uses cached values or static fallback)\nconst getUSDPrice = (crypto)=>{\n    const cacheKey = crypto.toUpperCase();\n    // Return cached price if available\n    if (priceCache[cacheKey]) {\n        return priceCache[cacheKey];\n    }\n    // Fallback to static prices\n    return getStaticUSDPrice(crypto);\n};\n// Static fallback prices (used when API is unavailable)\nconst getStaticUSDPrice = (crypto)=>{\n    const usdPrices = {\n        // Major cryptocurrencies - Updated to current market prices\n        'BTC': 106000,\n        'ETH': 2500,\n        'SOL': 180,\n        'ADA': 0.85,\n        'DOGE': 0.32,\n        'LINK': 22,\n        'MATIC': 0.42,\n        'DOT': 6.5,\n        'AVAX': 38,\n        'SHIB': 0.000022,\n        'XRP': 2.1,\n        'LTC': 95,\n        'BCH': 420,\n        // DeFi tokens\n        'UNI': 15,\n        'AAVE': 180,\n        'MKR': 1800,\n        'SNX': 3.5,\n        'COMP': 85,\n        'YFI': 8500,\n        'SUSHI': 2.1,\n        '1INCH': 0.65,\n        'CRV': 0.85,\n        'UMA': 3.2,\n        // Layer 1 blockchains\n        'ATOM': 12,\n        'NEAR': 6.5,\n        'ALGO': 0.35,\n        'ICP': 14,\n        'HBAR': 0.28,\n        'APT': 12.5,\n        'TON': 5.8,\n        'FTM': 0.95,\n        'ONE': 0.025,\n        // Other popular tokens\n        'FIL': 8.5,\n        'TRX': 0.25,\n        'ETC': 35,\n        'VET': 0.055,\n        'QNT': 125,\n        'LDO': 2.8,\n        'CRO': 0.18,\n        'LUNC': 0.00015,\n        // Gaming & Metaverse\n        'MANA': 0.85,\n        'SAND': 0.75,\n        'AXS': 8.5,\n        'ENJ': 0.45,\n        'CHZ': 0.12,\n        // Infrastructure & Utility\n        'THETA': 2.1,\n        'FLOW': 1.2,\n        'XTZ': 1.8,\n        'EOS': 1.1,\n        'GRT': 0.28,\n        'BAT': 0.35,\n        // Privacy coins\n        'ZEC': 45,\n        'DASH': 35,\n        // DEX tokens\n        'LRC': 0.45,\n        'ZRX': 0.65,\n        'KNC': 0.85,\n        // Other tokens\n        'REN': 0.15,\n        'BAND': 2.5,\n        'STORJ': 0.85,\n        'NMR': 25,\n        'ANT': 8.5,\n        'BNT': 0.95,\n        'MLN': 35,\n        'REP': 15,\n        // Smaller cap tokens\n        'IOTX': 0.065,\n        'ZIL': 0.045,\n        'ICX': 0.35,\n        'QTUM': 4.5,\n        'ONT': 0.45,\n        'WAVES': 3.2,\n        'LSK': 1.8,\n        'NANO': 1.5,\n        'SC': 0.008,\n        'DGB': 0.025,\n        'RVN': 0.035,\n        'BTT': 0.0000015,\n        'WIN': 0.00015,\n        'HOT': 0.0035,\n        'DENT': 0.0018,\n        'NPXS': 0.00085,\n        'FUN': 0.0085,\n        'CELR': 0.025,\n        // Stablecoins\n        'USDT': 1.0,\n        'USDC': 1.0,\n        'FDUSD': 1.0,\n        'BUSD': 1.0,\n        'DAI': 1.0\n    };\n    return usdPrices[crypto.toUpperCase()] || 100;\n};\n// Static fallback function for market price calculation (no fluctuations)\nconst calculateStaticMarketPrice = (config)=>{\n    const crypto1USDPrice = getStaticUSDPrice(config.crypto1);\n    const crypto2USDPrice = getStaticUSDPrice(config.crypto2);\n    // Calculate the ratio: how many units of crypto2 = 1 unit of crypto1\n    const basePrice = crypto1USDPrice / crypto2USDPrice;\n    console.log(\"\\uD83D\\uDCCA Static price calculation: \".concat(config.crypto1, \" ($\").concat(crypto1USDPrice, \") / \").concat(config.crypto2, \" ($\").concat(crypto2USDPrice, \") = \").concat(basePrice.toFixed(6)));\n    return basePrice;\n};\nconst initialBaseConfig = {\n    tradingMode: \"SimpleSpot\",\n    crypto1: \"\",\n    crypto2: \"\",\n    baseBid: 100,\n    multiplier: 1.005,\n    numDigits: 4,\n    slippagePercent: 0.2,\n    incomeSplitCrypto1Percent: 50,\n    incomeSplitCrypto2Percent: 50,\n    preferredStablecoin: _lib_types__WEBPACK_IMPORTED_MODULE_2__.AVAILABLE_STABLECOINS[0]\n};\nconst initialTradingState = {\n    config: initialBaseConfig,\n    targetPriceRows: [],\n    orderHistory: [],\n    appSettings: _lib_types__WEBPACK_IMPORTED_MODULE_2__.DEFAULT_APP_SETTINGS,\n    currentMarketPrice: calculateInitialMarketPrice(initialBaseConfig),\n    // isBotActive: false, // Remove\n    botSystemStatus: 'Stopped',\n    crypto1Balance: 10,\n    crypto2Balance: 100000,\n    stablecoinBalance: 0,\n    backendStatus: 'unknown'\n};\nconst lastActionTimestampPerCounter = new Map();\n// LocalStorage persistence functions\nconst STORAGE_KEY = 'pluto_trading_state';\nconst saveStateToLocalStorage = (state)=>{\n    try {\n        if (true) {\n            const stateToSave = {\n                config: state.config,\n                targetPriceRows: state.targetPriceRows,\n                orderHistory: state.orderHistory,\n                appSettings: state.appSettings,\n                currentMarketPrice: state.currentMarketPrice,\n                crypto1Balance: state.crypto1Balance,\n                crypto2Balance: state.crypto2Balance,\n                stablecoinBalance: state.stablecoinBalance,\n                botSystemStatus: state.botSystemStatus,\n                timestamp: Date.now()\n            };\n            localStorage.setItem(STORAGE_KEY, JSON.stringify(stateToSave));\n        }\n    } catch (error) {\n        console.error('Failed to save state to localStorage:', error);\n    }\n};\nconst loadStateFromLocalStorage = ()=>{\n    try {\n        if (true) {\n            const savedState = localStorage.getItem(STORAGE_KEY);\n            if (savedState) {\n                const parsed = JSON.parse(savedState);\n                // Check if state is not too old (24 hours)\n                if (parsed.timestamp && Date.now() - parsed.timestamp < 24 * 60 * 60 * 1000) {\n                    return parsed;\n                }\n            }\n        }\n    } catch (error) {\n        console.error('Failed to load state from localStorage:', error);\n    }\n    return null;\n};\nconst tradingReducer = (state, action)=>{\n    switch(action.type){\n        case 'SET_CONFIG':\n            const newConfig = {\n                ...state.config,\n                ...action.payload\n            };\n            // If trading pair changes, reset market price (it will be re-calculated by effect)\n            if (action.payload.crypto1 || action.payload.crypto2) {\n                return {\n                    ...state,\n                    config: newConfig,\n                    currentMarketPrice: calculateInitialMarketPrice(newConfig)\n                };\n            }\n            return {\n                ...state,\n                config: newConfig\n            };\n        case 'SET_TARGET_PRICE_ROWS':\n            return {\n                ...state,\n                targetPriceRows: action.payload.sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }))\n            };\n        case 'ADD_TARGET_PRICE_ROW':\n            {\n                const newRows = [\n                    ...state.targetPriceRows,\n                    action.payload\n                ].sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }));\n                return {\n                    ...state,\n                    targetPriceRows: newRows\n                };\n            }\n        case 'UPDATE_TARGET_PRICE_ROW':\n            {\n                const updatedRows = state.targetPriceRows.map((row)=>row.id === action.payload.id ? action.payload : row).sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }));\n                return {\n                    ...state,\n                    targetPriceRows: updatedRows\n                };\n            }\n        case 'REMOVE_TARGET_PRICE_ROW':\n            {\n                const filteredRows = state.targetPriceRows.filter((row)=>row.id !== action.payload).sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }));\n                return {\n                    ...state,\n                    targetPriceRows: filteredRows\n                };\n            }\n        case 'ADD_ORDER_HISTORY_ENTRY':\n            return {\n                ...state,\n                orderHistory: [\n                    action.payload,\n                    ...state.orderHistory\n                ]\n            };\n        case 'CLEAR_ORDER_HISTORY':\n            return {\n                ...state,\n                orderHistory: []\n            };\n        case 'SET_APP_SETTINGS':\n            return {\n                ...state,\n                appSettings: {\n                    ...state.appSettings,\n                    ...action.payload\n                }\n            };\n        case 'SET_MARKET_PRICE':\n            return {\n                ...state,\n                currentMarketPrice: action.payload\n            };\n        case 'FLUCTUATE_MARKET_PRICE':\n            {\n                if (state.currentMarketPrice <= 0) return state;\n                // More realistic fluctuation: smaller, more frequent changes\n                const fluctuationFactor = (Math.random() - 0.5) * 0.006; // Approx +/- 0.3% per update\n                const newPrice = state.currentMarketPrice * (1 + fluctuationFactor);\n                return {\n                    ...state,\n                    currentMarketPrice: newPrice > 0 ? newPrice : state.currentMarketPrice\n                };\n            }\n        // case 'SET_BOT_STATUS': // Removed\n        case 'UPDATE_BALANCES':\n            return {\n                ...state,\n                crypto1Balance: action.payload.crypto1 !== undefined ? action.payload.crypto1 : state.crypto1Balance,\n                crypto2Balance: action.payload.crypto2 !== undefined ? action.payload.crypto2 : state.crypto2Balance,\n                stablecoinBalance: action.payload.stablecoin !== undefined ? action.payload.stablecoin : state.stablecoinBalance\n            };\n        case 'SET_BALANCES':\n            return {\n                ...state,\n                crypto1Balance: action.payload.crypto1,\n                crypto2Balance: action.payload.crypto2,\n                stablecoinBalance: action.payload.stablecoin !== undefined ? action.payload.stablecoin : state.stablecoinBalance\n            };\n        case 'UPDATE_STABLECOIN_BALANCE':\n            return {\n                ...state,\n                stablecoinBalance: action.payload\n            };\n        case 'RESET_SESSION':\n            const configForReset = {\n                ...state.config\n            };\n            return {\n                ...initialTradingState,\n                config: configForReset,\n                appSettings: {\n                    ...state.appSettings\n                },\n                currentMarketPrice: calculateInitialMarketPrice(configForReset),\n                // Preserve current balances instead of resetting to initial values\n                crypto1Balance: state.crypto1Balance,\n                crypto2Balance: state.crypto2Balance,\n                stablecoinBalance: state.stablecoinBalance\n            };\n        case 'SET_BACKEND_STATUS':\n            return {\n                ...state,\n                backendStatus: action.payload\n            };\n        case 'SYSTEM_START_BOT_INITIATE':\n            // Continue from previous state instead of resetting\n            return {\n                ...state,\n                botSystemStatus: 'WarmingUp'\n            };\n        case 'SYSTEM_COMPLETE_WARMUP':\n            return {\n                ...state,\n                botSystemStatus: 'Running'\n            };\n        case 'SYSTEM_STOP_BOT':\n            return {\n                ...state,\n                botSystemStatus: 'Stopped'\n            };\n        case 'SYSTEM_RESET_BOT':\n            // Fresh Start: Clear all target price rows and order history completely\n            lastActionTimestampPerCounter.clear();\n            // Clear current session to force new session name generation\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            sessionManager.clearCurrentSession();\n            return {\n                ...state,\n                botSystemStatus: 'Stopped',\n                targetPriceRows: [],\n                orderHistory: []\n            };\n        case 'SET_TARGET_PRICE_ROWS':\n            return {\n                ...state,\n                targetPriceRows: action.payload\n            };\n        case 'RESET_FOR_NEW_CRYPTO':\n            return {\n                ...initialTradingState,\n                config: state.config,\n                backendStatus: state.backendStatus,\n                botSystemStatus: 'Stopped',\n                currentMarketPrice: 0,\n                // Preserve current balances instead of resetting to initial values\n                crypto1Balance: state.crypto1Balance,\n                crypto2Balance: state.crypto2Balance,\n                stablecoinBalance: state.stablecoinBalance\n            };\n        default:\n            return state;\n    }\n};\nconst TradingContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// SIMPLIFIED LOGIC - NO COMPLEX COOLDOWNS\nconst TradingProvider = (param)=>{\n    let { children } = param;\n    _s();\n    // Initialize state with localStorage data if available\n    const initializeState = ()=>{\n        // Check if this is a new session from URL parameter\n        if (true) {\n            const urlParams = new URLSearchParams(window.location.search);\n            const isNewSession = urlParams.get('newSession') === 'true';\n            if (isNewSession) {\n                console.log('🆕 New session requested - starting with completely fresh state');\n                // Note: URL parameter will be cleared in useEffect to avoid render-time state updates\n                return initialTradingState;\n            }\n        }\n        // Check if this is a new window/tab by checking if we have a current session\n        const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n        const currentSessionId = sessionManager.getCurrentSessionId();\n        // If no current session exists for this window, start fresh\n        if (!currentSessionId) {\n            console.log('🆕 New window detected - starting with fresh state');\n            return initialTradingState;\n        }\n        // If we have a session, try to load the session data first (priority over localStorage)\n        const currentSession = sessionManager.loadSession(currentSessionId);\n        if (currentSession) {\n            console.log('🔄 Loading session data with balances:', {\n                crypto1: currentSession.crypto1Balance,\n                crypto2: currentSession.crypto2Balance,\n                stablecoin: currentSession.stablecoinBalance\n            });\n            return {\n                ...initialTradingState,\n                config: currentSession.config,\n                targetPriceRows: currentSession.targetPriceRows,\n                orderHistory: currentSession.orderHistory,\n                currentMarketPrice: currentSession.currentMarketPrice,\n                crypto1Balance: currentSession.crypto1Balance,\n                crypto2Balance: currentSession.crypto2Balance,\n                stablecoinBalance: currentSession.stablecoinBalance,\n                botSystemStatus: currentSession.isActive ? 'Running' : 'Stopped'\n            };\n        }\n        // Fallback to localStorage if session loading fails\n        const savedState = loadStateFromLocalStorage();\n        if (savedState) {\n            return {\n                ...initialTradingState,\n                ...savedState\n            };\n        }\n        return initialTradingState;\n    };\n    const [state, dispatch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useReducer)(tradingReducer, initializeState());\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__.useToast)();\n    // Clear URL parameter after component mounts to avoid render-time state updates\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            if (true) {\n                const urlParams = new URLSearchParams(window.location.search);\n                const isNewSession = urlParams.get('newSession') === 'true';\n                if (isNewSession) {\n                    // Clear the URL parameter to avoid confusion\n                    const newUrl = window.location.pathname;\n                    window.history.replaceState({}, '', newUrl);\n                }\n            }\n        }\n    }[\"TradingProvider.useEffect\"], []); // Run only once on mount\n    const audioRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Removed processing locks and cooldowns for continuous trading\n    // Initialize fetchMarketPrice first\n    const fetchMarketPrice = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[fetchMarketPrice]\": async ()=>{\n            try {\n                // Don't fetch price if either crypto is not selected\n                if (!state.config.crypto1 || !state.config.crypto2) {\n                    dispatch({\n                        type: 'SET_MARKET_PRICE',\n                        payload: 0\n                    });\n                    return;\n                }\n                let price;\n                // For StablecoinSwap mode, use Binance USD prices for accurate market calculation\n                if (state.config.tradingMode === \"StablecoinSwap\") {\n                    try {\n                        const crypto1USDPrice = await getRealTimeUSDPrice(state.config.crypto1);\n                        const crypto2USDPrice = await getRealTimeUSDPrice(state.config.crypto2);\n                        price = crypto1USDPrice / crypto2USDPrice;\n                        console.log(\"\\uD83D\\uDCCA StablecoinSwap Market Price (Binance):\\n            - \".concat(state.config.crypto1, \" USD Price: $\").concat(crypto1USDPrice.toLocaleString(), \"\\n            - \").concat(state.config.crypto2, \" USD Price: $\").concat(crypto2USDPrice.toLocaleString(), \"\\n            - Market Price (\").concat(state.config.crypto1, \"/\").concat(state.config.crypto2, \"): \").concat(price.toFixed(6)));\n                    } catch (error) {\n                        // Fallback to cached/static prices\n                        const crypto1USDPrice = getUSDPrice(state.config.crypto1);\n                        const crypto2USDPrice = getUSDPrice(state.config.crypto2);\n                        price = crypto1USDPrice / crypto2USDPrice;\n                        console.log(\"\\uD83D\\uDCCA StablecoinSwap Market Price (Fallback):\\n            - \".concat(state.config.crypto1, \" USD Price: $\").concat(crypto1USDPrice.toLocaleString(), \"\\n            - \").concat(state.config.crypto2, \" USD Price: $\").concat(crypto2USDPrice.toLocaleString(), \"\\n            - Market Price (\").concat(state.config.crypto1, \"/\").concat(state.config.crypto2, \"): \").concat(price.toFixed(6)));\n                    }\n                } else {\n                    // For SimpleSpot mode, use backend API (which connects to Binance)\n                    price = await getMarketPriceFromBackend(state.config);\n                }\n                dispatch({\n                    type: 'SET_MARKET_PRICE',\n                    payload: price\n                });\n            } catch (error) {\n                console.error('Failed to fetch market price:', error);\n                sendAPIErrorNotification('Price API', \"Failed to fetch market price: \".concat(error)).catch(console.error);\n            }\n        }\n    }[\"TradingProvider.useCallback[fetchMarketPrice]\"], [\n        state.config,\n        dispatch\n    ]);\n    // Market price real-time updates from Binance\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            // Initial fetch\n            fetchMarketPrice();\n            // Set up real-time price updates every 2 seconds from Binance\n            const priceUpdateInterval = setInterval({\n                \"TradingProvider.useEffect.priceUpdateInterval\": async ()=>{\n                    const networkMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.NetworkMonitor.getInstance();\n                    if (networkMonitor.getStatus().isOnline) {\n                        try {\n                            await fetchMarketPrice();\n                            console.log('📊 Binance price updated');\n                        } catch (error) {\n                            console.warn('⚠️ Failed to update price from Binance:', error);\n                        }\n                    }\n                }\n            }[\"TradingProvider.useEffect.priceUpdateInterval\"], 2000); // Update every 2 seconds as requested\n            return ({\n                \"TradingProvider.useEffect\": ()=>{\n                    clearInterval(priceUpdateInterval);\n                }\n            })[\"TradingProvider.useEffect\"];\n        }\n    }[\"TradingProvider.useEffect\"], [\n        fetchMarketPrice,\n        state.config.crypto1,\n        state.config.crypto2,\n        state.config.tradingMode\n    ]);\n    // Audio initialization and user interaction detection\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            if (true) {\n                audioRef.current = new Audio();\n                // Add user interaction listener to enable audio\n                const enableAudio = {\n                    \"TradingProvider.useEffect.enableAudio\": ()=>{\n                        if (audioRef.current) {\n                            // Try to play a silent audio to enable audio context\n                            audioRef.current.volume = 0;\n                            audioRef.current.play().then({\n                                \"TradingProvider.useEffect.enableAudio\": ()=>{\n                                    if (audioRef.current) {\n                                        audioRef.current.volume = 1;\n                                    }\n                                    console.log('🔊 Audio context enabled after user interaction');\n                                }\n                            }[\"TradingProvider.useEffect.enableAudio\"]).catch({\n                                \"TradingProvider.useEffect.enableAudio\": ()=>{\n                                // Still blocked, but we tried\n                                }\n                            }[\"TradingProvider.useEffect.enableAudio\"]);\n                        }\n                        // Remove the listener after first interaction\n                        document.removeEventListener('click', enableAudio);\n                        document.removeEventListener('keydown', enableAudio);\n                    }\n                }[\"TradingProvider.useEffect.enableAudio\"];\n                // Listen for first user interaction\n                document.addEventListener('click', enableAudio);\n                document.addEventListener('keydown', enableAudio);\n                return ({\n                    \"TradingProvider.useEffect\": ()=>{\n                        document.removeEventListener('click', enableAudio);\n                        document.removeEventListener('keydown', enableAudio);\n                    }\n                })[\"TradingProvider.useEffect\"];\n            }\n        }\n    }[\"TradingProvider.useEffect\"], []);\n    const playSound = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[playSound]\": (soundKey)=>{\n            // Get current session's alarm settings, fallback to global app settings\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            const currentSessionId = sessionManager.getCurrentSessionId();\n            let alarmSettings = state.appSettings; // Default fallback\n            if (currentSessionId) {\n                const currentSession = sessionManager.loadSession(currentSessionId);\n                if (currentSession && currentSession.alarmSettings) {\n                    alarmSettings = currentSession.alarmSettings;\n                }\n            }\n            if (alarmSettings.soundAlertsEnabled && audioRef.current) {\n                let soundPath;\n                if (soundKey === 'soundOrderExecution' && alarmSettings.alertOnOrderExecution) {\n                    soundPath = alarmSettings.soundOrderExecution;\n                } else if (soundKey === 'soundError' && alarmSettings.alertOnError) {\n                    soundPath = alarmSettings.soundError;\n                }\n                if (soundPath) {\n                    try {\n                        // Stop any currently playing audio first\n                        audioRef.current.pause();\n                        audioRef.current.currentTime = 0;\n                        // Set the source and load the audio\n                        audioRef.current.src = soundPath;\n                        audioRef.current.load(); // Explicitly load the audio\n                        // Add error handler for audio loading\n                        audioRef.current.onerror = ({\n                            \"TradingProvider.useCallback[playSound]\": (e)=>{\n                                console.warn(\"⚠️ Audio loading failed for \".concat(soundPath, \":\"), e);\n                                // Try fallback sound\n                                if (soundPath !== '/sounds/chime2.wav' && audioRef.current) {\n                                    audioRef.current.src = '/sounds/chime2.wav';\n                                    audioRef.current.load();\n                                }\n                            }\n                        })[\"TradingProvider.useCallback[playSound]\"];\n                        // Play the sound and limit duration to 2 seconds\n                        const playPromise = audioRef.current.play();\n                        if (playPromise !== undefined) {\n                            playPromise.then({\n                                \"TradingProvider.useCallback[playSound]\": ()=>{\n                                    // Set a timeout to pause the audio after 2 seconds\n                                    setTimeout({\n                                        \"TradingProvider.useCallback[playSound]\": ()=>{\n                                            if (audioRef.current && !audioRef.current.paused) {\n                                                audioRef.current.pause();\n                                                audioRef.current.currentTime = 0; // Reset for next play\n                                            }\n                                        }\n                                    }[\"TradingProvider.useCallback[playSound]\"], 2000); // 2 seconds\n                                }\n                            }[\"TradingProvider.useCallback[playSound]\"]).catch({\n                                \"TradingProvider.useCallback[playSound]\": (err)=>{\n                                    // Handle different types of audio errors gracefully\n                                    const errorMessage = err.message || String(err);\n                                    if (errorMessage.includes('user didn\\'t interact') || errorMessage.includes('autoplay')) {\n                                        console.log('🔇 Audio autoplay blocked by browser - user interaction required');\n                                    // Could show a notification to user about enabling audio\n                                    } else if (errorMessage.includes('interrupted') || errorMessage.includes('supported source')) {\n                                    // These are expected errors, don't log them\n                                    } else {\n                                        console.warn(\"⚠️ Audio playback failed:\", errorMessage);\n                                    }\n                                }\n                            }[\"TradingProvider.useCallback[playSound]\"]);\n                        }\n                    } catch (error) {\n                        console.warn(\"⚠️ Audio setup failed for \".concat(soundPath, \":\"), error);\n                    }\n                }\n            }\n        }\n    }[\"TradingProvider.useCallback[playSound]\"], [\n        state.appSettings\n    ]);\n    // Enhanced Telegram notification function with error handling\n    const sendTelegramNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[sendTelegramNotification]\": async function(message) {\n            let isError = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n            try {\n                const telegramToken = localStorage.getItem('telegram_bot_token');\n                const telegramChatId = localStorage.getItem('telegram_chat_id');\n                if (!telegramToken || !telegramChatId) {\n                    console.log('Telegram not configured - skipping notification');\n                    return;\n                }\n                const response = await fetch(\"https://api.telegram.org/bot\".concat(telegramToken, \"/sendMessage\"), {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        chat_id: telegramChatId,\n                        text: message,\n                        parse_mode: 'HTML'\n                    })\n                });\n                if (!response.ok) {\n                    console.error('Failed to send Telegram notification:', response.statusText);\n                    // Don't retry error notifications to avoid infinite loops\n                    if (!isError) {\n                        await sendTelegramErrorNotification('Telegram API Error', \"Failed to send notification: \".concat(response.statusText));\n                    }\n                } else {\n                    console.log('✅ Telegram notification sent successfully');\n                }\n            } catch (error) {\n                console.error('Error sending Telegram notification:', error);\n                // Network disconnection detected\n                if (!isError) {\n                    await sendTelegramErrorNotification('Network Disconnection', \"Failed to connect to Telegram API: \".concat(error));\n                }\n            }\n        }\n    }[\"TradingProvider.useCallback[sendTelegramNotification]\"], []);\n    // Specific error notification functions\n    const sendTelegramErrorNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[sendTelegramErrorNotification]\": async (errorType, errorMessage)=>{\n            const timestamp = new Date().toLocaleString();\n            const message = \"⚠️ <b>ERROR ALERT</b>\\n\\n\" + \"\\uD83D\\uDD34 <b>Type:</b> \".concat(errorType, \"\\n\") + \"\\uD83D\\uDCDD <b>Message:</b> \".concat(errorMessage, \"\\n\") + \"⏰ <b>Time:</b> \".concat(timestamp, \"\\n\") + \"\\uD83E\\uDD16 <b>Bot:</b> \".concat(state.config.crypto1, \"/\").concat(state.config.crypto2, \" \").concat(state.config.tradingMode);\n            await sendTelegramNotification(message, true); // Mark as error to prevent recursion\n        }\n    }[\"TradingProvider.useCallback[sendTelegramErrorNotification]\"], [\n        state.config,\n        sendTelegramNotification\n    ]);\n    const sendLowBalanceNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[sendLowBalanceNotification]\": async (crypto, currentBalance, requiredAmount)=>{\n            const timestamp = new Date().toLocaleString();\n            const message = \"\\uD83D\\uDCB0 <b>LOW BALANCE ALERT</b>\\n\\n\" + \"\\uD83E\\uDE99 <b>Currency:</b> \".concat(crypto, \"\\n\") + \"\\uD83D\\uDCCA <b>Current Balance:</b> \".concat(currentBalance.toFixed(6), \" \").concat(crypto, \"\\n\") + \"⚡ <b>Required Amount:</b> \".concat(requiredAmount.toFixed(6), \" \").concat(crypto, \"\\n\") + \"\\uD83D\\uDCC9 <b>Shortage:</b> \".concat((requiredAmount - currentBalance).toFixed(6), \" \").concat(crypto, \"\\n\") + \"⏰ <b>Time:</b> \".concat(timestamp, \"\\n\") + \"\\uD83E\\uDD16 <b>Bot:</b> \".concat(state.config.crypto1, \"/\").concat(state.config.crypto2, \" \").concat(state.config.tradingMode);\n            await sendTelegramNotification(message);\n        }\n    }[\"TradingProvider.useCallback[sendLowBalanceNotification]\"], [\n        state.config,\n        sendTelegramNotification\n    ]);\n    const sendAPIErrorNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[sendAPIErrorNotification]\": async (apiName, errorDetails)=>{\n            const timestamp = new Date().toLocaleString();\n            const message = \"\\uD83D\\uDD0C <b>API ERROR ALERT</b>\\n\\n\" + \"\\uD83C\\uDF10 <b>API:</b> \".concat(apiName, \"\\n\") + \"❌ <b>Error:</b> \".concat(errorDetails, \"\\n\") + \"⏰ <b>Time:</b> \".concat(timestamp, \"\\n\") + \"\\uD83E\\uDD16 <b>Bot:</b> \".concat(state.config.crypto1, \"/\").concat(state.config.crypto2, \" \").concat(state.config.tradingMode);\n            await sendTelegramNotification(message);\n        }\n    }[\"TradingProvider.useCallback[sendAPIErrorNotification]\"], [\n        state.config,\n        sendTelegramNotification\n    ]);\n    // Effect to update market price when trading pair changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n        // When crypto1 or crypto2 (parts of state.config) change,\n        // the fetchMarketPrice useCallback gets a new reference.\n        // The useEffect above (which depends on fetchMarketPrice)\n        // will re-run, clear the old interval, make an initial fetch with the new config,\n        // and set up a new interval.\n        // The reducer for SET_CONFIG also sets an initial market price if crypto1/crypto2 changes.\n        // Thus, no explicit dispatch is needed here.\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.config.crypto1,\n        state.config.crypto2\n    ]); // Dependencies ensure this reacts to pair changes\n    const setTargetPrices = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[setTargetPrices]\": (prices)=>{\n            if (!prices || !Array.isArray(prices)) return;\n            // Sort prices from lowest to highest for proper counter assignment\n            const sortedPrices = [\n                ...prices\n            ].filter({\n                \"TradingProvider.useCallback[setTargetPrices].sortedPrices\": (price)=>!isNaN(price) && price > 0\n            }[\"TradingProvider.useCallback[setTargetPrices].sortedPrices\"]).sort({\n                \"TradingProvider.useCallback[setTargetPrices].sortedPrices\": (a, b)=>a - b\n            }[\"TradingProvider.useCallback[setTargetPrices].sortedPrices\"]);\n            const newRows = sortedPrices.map({\n                \"TradingProvider.useCallback[setTargetPrices].newRows\": (price, index)=>{\n                    const existingRow = state.targetPriceRows.find({\n                        \"TradingProvider.useCallback[setTargetPrices].newRows.existingRow\": (r)=>r.targetPrice === price\n                    }[\"TradingProvider.useCallback[setTargetPrices].newRows.existingRow\"]);\n                    if (existingRow) {\n                        // Update counter for existing row based on sorted position\n                        return {\n                            ...existingRow,\n                            counter: index + 1\n                        };\n                    }\n                    return {\n                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                        counter: index + 1,\n                        status: 'Free',\n                        orderLevel: 0,\n                        valueLevel: state.config.baseBid,\n                        targetPrice: price\n                    };\n                }\n            }[\"TradingProvider.useCallback[setTargetPrices].newRows\"]);\n            dispatch({\n                type: 'SET_TARGET_PRICE_ROWS',\n                payload: newRows\n            });\n        }\n    }[\"TradingProvider.useCallback[setTargetPrices]\"], [\n        state.targetPriceRows,\n        state.config.baseBid,\n        dispatch\n    ]);\n    // Core Trading Logic (Simulated) - CONTINUOUS TRADING VERSION\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            // Check network status first\n            const networkMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.NetworkMonitor.getInstance();\n            const isOnline = networkMonitor.getStatus().isOnline;\n            // Only check essential conditions AND network status\n            if (state.botSystemStatus !== 'Running' || state.targetPriceRows.length === 0 || state.currentMarketPrice <= 0 || !isOnline) {\n                if (!isOnline && state.botSystemStatus === 'Running') {\n                    console.log('🔴 Trading paused - network offline');\n                }\n                return;\n            }\n            // Execute trading logic immediately - no locks, no cooldowns, no delays\n            const { config, currentMarketPrice, targetPriceRows, crypto1Balance, crypto2Balance } = state;\n            const sortedRowsForLogic = [\n                ...targetPriceRows\n            ].sort({\n                \"TradingProvider.useEffect.sortedRowsForLogic\": (a, b)=>a.targetPrice - b.targetPrice\n            }[\"TradingProvider.useEffect.sortedRowsForLogic\"]);\n            // Use mutable variables for balance tracking within this cycle\n            let currentCrypto1Balance = crypto1Balance;\n            let currentCrypto2Balance = crypto2Balance;\n            let actionsTaken = 0;\n            console.log(\"\\uD83D\\uDE80 CONTINUOUS TRADING: Price $\".concat(currentMarketPrice.toFixed(2), \" | Targets: \").concat(sortedRowsForLogic.length, \" | Balance: $\").concat(currentCrypto2Balance, \" \").concat(config.crypto2));\n            // Show which targets are in range\n            const targetsInRange = sortedRowsForLogic.filter({\n                \"TradingProvider.useEffect.targetsInRange\": (row)=>{\n                    const diffPercent = Math.abs(currentMarketPrice - row.targetPrice) / currentMarketPrice * 100;\n                    return diffPercent <= config.slippagePercent;\n                }\n            }[\"TradingProvider.useEffect.targetsInRange\"]);\n            if (targetsInRange.length > 0) {\n                console.log(\"\\uD83C\\uDFAF TARGETS IN RANGE (\\xb1\".concat(config.slippagePercent, \"%):\"), targetsInRange.map({\n                    \"TradingProvider.useEffect\": (row)=>\"Counter \".concat(row.counter, \" (\").concat(row.status, \")\")\n                }[\"TradingProvider.useEffect\"]));\n            }\n            // CONTINUOUS TRADING LOGIC: Process all targets immediately\n            for(let i = 0; i < sortedRowsForLogic.length; i++){\n                const activeRow = sortedRowsForLogic[i];\n                const priceDiffPercent = Math.abs(currentMarketPrice - activeRow.targetPrice) / currentMarketPrice * 100;\n                // STEP 1: Check if TargetRowN is triggered (within slippage range)\n                if (priceDiffPercent <= config.slippagePercent) {\n                    if (config.tradingMode === \"SimpleSpot\") {\n                        // STEP 2: Evaluate Triggered TargetRowN's Status\n                        if (activeRow.status === \"Free\") {\n                            // STEP 2a: Execute BUY on TargetRowN\n                            const costCrypto2 = activeRow.valueLevel;\n                            if (currentCrypto2Balance >= costCrypto2) {\n                                const amountCrypto1Bought = costCrypto2 / currentMarketPrice;\n                                const updatedRow = {\n                                    ...activeRow,\n                                    status: \"Full\",\n                                    orderLevel: activeRow.orderLevel + 1,\n                                    valueLevel: config.baseBid * Math.pow(config.multiplier, activeRow.orderLevel + 1),\n                                    crypto1AmountHeld: amountCrypto1Bought,\n                                    originalCostCrypto2: costCrypto2,\n                                    crypto1Var: amountCrypto1Bought,\n                                    crypto2Var: -costCrypto2\n                                };\n                                dispatch({\n                                    type: 'UPDATE_TARGET_PRICE_ROW',\n                                    payload: updatedRow\n                                });\n                                dispatch({\n                                    type: 'UPDATE_BALANCES',\n                                    payload: {\n                                        crypto1: currentCrypto1Balance + amountCrypto1Bought,\n                                        crypto2: currentCrypto2Balance - costCrypto2\n                                    }\n                                });\n                                dispatch({\n                                    type: 'ADD_ORDER_HISTORY_ENTRY',\n                                    payload: {\n                                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                        timestamp: Date.now(),\n                                        pair: \"\".concat(config.crypto1, \"/\").concat(config.crypto2),\n                                        crypto1: config.crypto1,\n                                        orderType: \"BUY\",\n                                        amountCrypto1: amountCrypto1Bought,\n                                        avgPrice: currentMarketPrice,\n                                        valueCrypto2: costCrypto2,\n                                        price1: currentMarketPrice,\n                                        crypto1Symbol: config.crypto1 || '',\n                                        crypto2Symbol: config.crypto2 || ''\n                                    }\n                                });\n                                console.log(\"✅ BUY: Counter \".concat(activeRow.counter, \" bought \").concat(amountCrypto1Bought.toFixed(6), \" \").concat(config.crypto1, \" at $\").concat(currentMarketPrice.toFixed(2)));\n                                toast({\n                                    title: \"BUY Executed\",\n                                    description: \"Counter \".concat(activeRow.counter, \": \").concat(amountCrypto1Bought.toFixed(6), \" \").concat(config.crypto1),\n                                    duration: 2000\n                                });\n                                playSound('soundOrderExecution');\n                                // Send Telegram notification for BUY\n                                sendTelegramNotification(\"\\uD83D\\uDFE2 <b>BUY EXECUTED</b>\\n\" + \"\\uD83D\\uDCCA Counter: \".concat(activeRow.counter, \"\\n\") + \"\\uD83D\\uDCB0 Amount: \".concat(amountCrypto1Bought.toFixed(6), \" \").concat(config.crypto1, \"\\n\") + \"\\uD83D\\uDCB5 Price: $\".concat(currentMarketPrice.toFixed(2), \"\\n\") + \"\\uD83D\\uDCB8 Cost: $\".concat(costCrypto2.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\\uD83D\\uDCC8 Mode: Simple Spot\");\n                                actionsTaken++;\n                                // Update local balance for subsequent checks in this cycle\n                                currentCrypto2Balance -= costCrypto2;\n                                currentCrypto1Balance += amountCrypto1Bought;\n                            } else {\n                                // Insufficient balance - send Telegram notification\n                                console.log(\"❌ Insufficient \".concat(config.crypto2, \" balance: \").concat(currentCrypto2Balance.toFixed(6), \" < \").concat(costCrypto2.toFixed(6)));\n                                sendLowBalanceNotification(config.crypto2, currentCrypto2Balance, costCrypto2).catch(console.error);\n                            }\n                        }\n                        // STEP 3: Check and Process Inferior TargetRow (TargetRowN_minus_1) - ALWAYS, regardless of BUY\n                        const currentCounter = activeRow.counter;\n                        const inferiorRow = sortedRowsForLogic.find({\n                            \"TradingProvider.useEffect.inferiorRow\": (row)=>row.counter === currentCounter - 1\n                        }[\"TradingProvider.useEffect.inferiorRow\"]);\n                        if (inferiorRow && inferiorRow.status === \"Full\" && inferiorRow.crypto1AmountHeld && inferiorRow.originalCostCrypto2) {\n                            const amountCrypto1ToSell = inferiorRow.crypto1AmountHeld;\n                            const crypto2Received = amountCrypto1ToSell * currentMarketPrice;\n                            const realizedProfit = crypto2Received - inferiorRow.originalCostCrypto2;\n                            // Calculate Crypto1 profit/loss based on income split percentage\n                            const realizedProfitCrypto1 = currentMarketPrice > 0 ? realizedProfit * config.incomeSplitCrypto1Percent / 100 / currentMarketPrice : 0;\n                            const updatedInferiorRow = {\n                                ...inferiorRow,\n                                status: \"Free\",\n                                crypto1AmountHeld: undefined,\n                                originalCostCrypto2: undefined,\n                                valueLevel: config.baseBid * Math.pow(config.multiplier, inferiorRow.orderLevel),\n                                crypto1Var: -amountCrypto1ToSell,\n                                crypto2Var: crypto2Received\n                            };\n                            dispatch({\n                                type: 'UPDATE_TARGET_PRICE_ROW',\n                                payload: updatedInferiorRow\n                            });\n                            dispatch({\n                                type: 'UPDATE_BALANCES',\n                                payload: {\n                                    crypto1: currentCrypto1Balance - amountCrypto1ToSell,\n                                    crypto2: currentCrypto2Balance + crypto2Received\n                                }\n                            });\n                            dispatch({\n                                type: 'ADD_ORDER_HISTORY_ENTRY',\n                                payload: {\n                                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                    timestamp: Date.now(),\n                                    pair: \"\".concat(config.crypto1, \"/\").concat(config.crypto2),\n                                    crypto1: config.crypto1,\n                                    orderType: \"SELL\",\n                                    amountCrypto1: amountCrypto1ToSell,\n                                    avgPrice: currentMarketPrice,\n                                    valueCrypto2: crypto2Received,\n                                    price1: currentMarketPrice,\n                                    crypto1Symbol: config.crypto1 || '',\n                                    crypto2Symbol: config.crypto2 || '',\n                                    realizedProfitLossCrypto2: realizedProfit,\n                                    realizedProfitLossCrypto1: realizedProfitCrypto1\n                                }\n                            });\n                            console.log(\"✅ SELL: Counter \".concat(currentCounter - 1, \" sold \").concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \". Profit: $\").concat(realizedProfit.toFixed(2)));\n                            toast({\n                                title: \"SELL Executed\",\n                                description: \"Counter \".concat(currentCounter - 1, \": Profit $\").concat(realizedProfit.toFixed(2)),\n                                duration: 2000\n                            });\n                            playSound('soundOrderExecution');\n                            // Send Telegram notification for SELL\n                            const profitEmoji = realizedProfit > 0 ? '📈' : realizedProfit < 0 ? '📉' : '➖';\n                            sendTelegramNotification(\"\\uD83D\\uDD34 <b>SELL EXECUTED</b>\\n\" + \"\\uD83D\\uDCCA Counter: \".concat(currentCounter - 1, \"\\n\") + \"\\uD83D\\uDCB0 Amount: \".concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \"\\n\") + \"\\uD83D\\uDCB5 Price: $\".concat(currentMarketPrice.toFixed(2), \"\\n\") + \"\\uD83D\\uDCB8 Received: $\".concat(crypto2Received.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\".concat(profitEmoji, \" Profit: $\").concat(realizedProfit.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\\uD83D\\uDCC8 Mode: Simple Spot\");\n                            actionsTaken++;\n                            // Update local balance for subsequent checks in this cycle\n                            currentCrypto1Balance -= amountCrypto1ToSell;\n                            currentCrypto2Balance += crypto2Received;\n                        }\n                    } else if (config.tradingMode === \"StablecoinSwap\") {\n                        // STEP 2: Evaluate Triggered TargetRowN's Status (Stablecoin Swap Mode)\n                        if (activeRow.status === \"Free\") {\n                            // STEP 2a: Execute Two-Step \"Buy Crypto1 via Stablecoin\" on TargetRowN\n                            const amountCrypto2ToUse = activeRow.valueLevel; // Value = BaseBid * (Multiplier ^ Level)\n                            if (currentCrypto2Balance >= amountCrypto2ToUse) {\n                                // Step 1: Sell Crypto2 for PreferredStablecoin\n                                // Use current fluctuating market price for more realistic P/L\n                                const crypto2USDPrice = getUSDPrice(config.crypto2 || 'USDT');\n                                const stablecoinUSDPrice = getUSDPrice(config.preferredStablecoin || 'USDT');\n                                const crypto2StablecoinPrice = crypto2USDPrice / stablecoinUSDPrice;\n                                const stablecoinObtained = amountCrypto2ToUse * crypto2StablecoinPrice;\n                                // Step 2: Buy Crypto1 with Stablecoin\n                                // Use current fluctuating market price (this is the key for P/L calculation)\n                                const crypto1USDPrice = getUSDPrice(config.crypto1 || 'BTC');\n                                // Apply current market price fluctuation to Crypto1 price\n                                const fluctuatedCrypto1Price = crypto1USDPrice * (state.currentMarketPrice / (crypto1USDPrice / crypto2USDPrice));\n                                const crypto1StablecoinPrice = fluctuatedCrypto1Price / stablecoinUSDPrice;\n                                const crypto1Bought = stablecoinObtained / crypto1StablecoinPrice;\n                                // Update Row N: Free → Full, Level++, Value recalculated\n                                const newLevel = activeRow.orderLevel + 1;\n                                const newValue = config.baseBid * Math.pow(config.multiplier, newLevel);\n                                const updatedRow = {\n                                    ...activeRow,\n                                    status: \"Full\",\n                                    orderLevel: newLevel,\n                                    valueLevel: newValue,\n                                    crypto1AmountHeld: crypto1Bought,\n                                    originalCostCrypto2: amountCrypto2ToUse,\n                                    crypto1Var: crypto1Bought,\n                                    crypto2Var: -amountCrypto2ToUse\n                                };\n                                console.log(\"\\uD83D\\uDCB0 StablecoinSwap BUY - Setting originalCostCrypto2:\", {\n                                    counter: activeRow.counter,\n                                    amountCrypto2ToUse: amountCrypto2ToUse,\n                                    crypto1Bought: crypto1Bought,\n                                    originalCostCrypto2: amountCrypto2ToUse,\n                                    newLevel: newLevel,\n                                    crypto1StablecoinPrice: crypto1StablecoinPrice,\n                                    fluctuatedCrypto1Price: fluctuatedCrypto1Price,\n                                    currentMarketPrice: state.currentMarketPrice\n                                });\n                                dispatch({\n                                    type: 'UPDATE_TARGET_PRICE_ROW',\n                                    payload: updatedRow\n                                });\n                                dispatch({\n                                    type: 'UPDATE_BALANCES',\n                                    payload: {\n                                        crypto1: currentCrypto1Balance + crypto1Bought,\n                                        crypto2: currentCrypto2Balance - amountCrypto2ToUse\n                                    }\n                                });\n                                // Add history entries for both steps of the stablecoin swap\n                                // Calculate P/L for the crypto2 sell based on price fluctuation\n                                const baseCrypto2Price = getUSDPrice(config.crypto2) / getUSDPrice(config.preferredStablecoin);\n                                const estimatedProfitCrypto2 = amountCrypto2ToUse * (crypto2StablecoinPrice - baseCrypto2Price);\n                                const estimatedProfitCrypto1 = estimatedProfitCrypto2 / crypto1StablecoinPrice;\n                                console.log(\"\\uD83D\\uDCCA StablecoinSwap Step A SELL P/L:\", {\n                                    crypto2: config.crypto2,\n                                    amountSold: amountCrypto2ToUse,\n                                    currentPrice: crypto2StablecoinPrice,\n                                    basePrice: baseCrypto2Price,\n                                    estimatedProfitCrypto2: estimatedProfitCrypto2,\n                                    estimatedProfitCrypto1: estimatedProfitCrypto1\n                                });\n                                dispatch({\n                                    type: 'ADD_ORDER_HISTORY_ENTRY',\n                                    payload: {\n                                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                        timestamp: Date.now(),\n                                        pair: \"\".concat(config.crypto2, \"/\").concat(config.preferredStablecoin),\n                                        crypto1: config.crypto2,\n                                        orderType: \"SELL\",\n                                        amountCrypto1: amountCrypto2ToUse,\n                                        avgPrice: crypto2StablecoinPrice,\n                                        valueCrypto2: stablecoinObtained,\n                                        price1: crypto2StablecoinPrice,\n                                        crypto1Symbol: config.crypto2 || '',\n                                        crypto2Symbol: config.preferredStablecoin || ''\n                                    }\n                                });\n                                dispatch({\n                                    type: 'ADD_ORDER_HISTORY_ENTRY',\n                                    payload: {\n                                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                        timestamp: Date.now(),\n                                        pair: \"\".concat(config.crypto1, \"/\").concat(config.preferredStablecoin),\n                                        crypto1: config.crypto1,\n                                        orderType: \"BUY\",\n                                        amountCrypto1: crypto1Bought,\n                                        avgPrice: crypto1StablecoinPrice,\n                                        valueCrypto2: stablecoinObtained,\n                                        price1: crypto1StablecoinPrice,\n                                        crypto1Symbol: config.crypto1 || '',\n                                        crypto2Symbol: config.preferredStablecoin || ''\n                                    }\n                                });\n                                console.log(\"✅ STABLECOIN BUY: Counter \".concat(activeRow.counter, \" | Step 1: Sold \").concat(amountCrypto2ToUse, \" \").concat(config.crypto2, \" → \").concat(stablecoinObtained.toFixed(2), \" \").concat(config.preferredStablecoin, \" | Step 2: Bought \").concat(crypto1Bought.toFixed(6), \" \").concat(config.crypto1, \" | Level: \").concat(activeRow.orderLevel, \" → \").concat(newLevel));\n                                toast({\n                                    title: \"BUY Executed (Stablecoin)\",\n                                    description: \"Counter \".concat(activeRow.counter, \": \").concat(crypto1Bought.toFixed(6), \" \").concat(config.crypto1, \" via \").concat(config.preferredStablecoin),\n                                    duration: 2000\n                                });\n                                playSound('soundOrderExecution');\n                                // Send Telegram notification for Stablecoin BUY\n                                sendTelegramNotification(\"\\uD83D\\uDFE2 <b>BUY EXECUTED (Stablecoin Swap)</b>\\n\" + \"\\uD83D\\uDCCA Counter: \".concat(activeRow.counter, \"\\n\") + \"\\uD83D\\uDD04 Step 1: Sold \".concat(amountCrypto2ToUse.toFixed(2), \" \").concat(config.crypto2, \" → \").concat(stablecoinObtained.toFixed(2), \" \").concat(config.preferredStablecoin, \"\\n\") + \"\\uD83D\\uDD04 Step 2: Bought \".concat(crypto1Bought.toFixed(6), \" \").concat(config.crypto1, \"\\n\") + \"\\uD83D\\uDCCA Level: \".concat(activeRow.orderLevel, \" → \").concat(newLevel, \"\\n\") + \"\\uD83D\\uDCC8 Mode: Stablecoin Swap\");\n                                actionsTaken++;\n                                // Update local balance for subsequent checks in this cycle\n                                currentCrypto2Balance -= amountCrypto2ToUse;\n                                currentCrypto1Balance += crypto1Bought;\n                            } else {\n                                // Insufficient balance - send Telegram notification\n                                console.log(\"❌ Insufficient \".concat(config.crypto2, \" balance for stablecoin swap: \").concat(currentCrypto2Balance.toFixed(6), \" < \").concat(amountCrypto2ToUse.toFixed(6)));\n                                sendLowBalanceNotification(config.crypto2, currentCrypto2Balance, amountCrypto2ToUse).catch(console.error);\n                            }\n                        }\n                        // STEP 3: Check and Process Inferior TargetRow (TargetRowN_minus_1) - ALWAYS, regardless of BUY\n                        const currentCounter = activeRow.counter;\n                        const inferiorRow = sortedRowsForLogic.find({\n                            \"TradingProvider.useEffect.inferiorRow\": (row)=>row.counter === currentCounter - 1\n                        }[\"TradingProvider.useEffect.inferiorRow\"]);\n                        console.log(\"\\uD83D\\uDD0D StablecoinSwap SELL Check:\", {\n                            currentCounter: currentCounter,\n                            inferiorRowFound: !!inferiorRow,\n                            inferiorRowStatus: inferiorRow === null || inferiorRow === void 0 ? void 0 : inferiorRow.status,\n                            inferiorRowCrypto1Held: inferiorRow === null || inferiorRow === void 0 ? void 0 : inferiorRow.crypto1AmountHeld,\n                            inferiorRowOriginalCost: inferiorRow === null || inferiorRow === void 0 ? void 0 : inferiorRow.originalCostCrypto2,\n                            canExecuteSell: !!(inferiorRow && inferiorRow.status === \"Full\" && inferiorRow.crypto1AmountHeld && inferiorRow.originalCostCrypto2)\n                        });\n                        if (inferiorRow && inferiorRow.status === \"Full\" && inferiorRow.crypto1AmountHeld && inferiorRow.originalCostCrypto2) {\n                            // Execute Two-Step \"Sell Crypto1 & Reacquire Crypto2 via Stablecoin\" for TargetRowN_minus_1\n                            const amountCrypto1ToSell = inferiorRow.crypto1AmountHeld;\n                            // Step A: Sell Crypto1 for PreferredStablecoin\n                            // Use current fluctuating market price for realistic P/L\n                            const crypto1USDPrice = getUSDPrice(config.crypto1 || 'BTC');\n                            const crypto2USDPrice = getUSDPrice(config.crypto2 || 'USDT');\n                            const stablecoinUSDPrice = getUSDPrice(config.preferredStablecoin || 'USDT');\n                            // Apply current market price fluctuation to Crypto1 price\n                            const fluctuatedCrypto1Price = crypto1USDPrice * (state.currentMarketPrice / (crypto1USDPrice / crypto2USDPrice));\n                            const crypto1StablecoinPrice = fluctuatedCrypto1Price / stablecoinUSDPrice;\n                            const stablecoinFromC1Sell = amountCrypto1ToSell * crypto1StablecoinPrice;\n                            // Step B: Buy Crypto2 with Stablecoin\n                            const crypto2StablecoinPrice = crypto2USDPrice / stablecoinUSDPrice;\n                            const crypto2Reacquired = stablecoinFromC1Sell / crypto2StablecoinPrice;\n                            // Calculate realized profit in Crypto2\n                            // We compare the value of crypto2 we got back vs what we originally spent\n                            const originalCost = inferiorRow.originalCostCrypto2 || 0;\n                            const realizedProfitInCrypto2 = crypto2Reacquired - originalCost;\n                            // Calculate Crypto1 profit/loss - this should be the profit in terms of Crypto1\n                            // For StablecoinSwap, we need to calculate how much Crypto1 profit this represents\n                            const realizedProfitCrypto1 = crypto1StablecoinPrice > 0 ? realizedProfitInCrypto2 / crypto1StablecoinPrice : 0;\n                            // IMPORTANT: If P/L is exactly 0, it might indicate a price calculation issue\n                            if (realizedProfitInCrypto2 === 0) {\n                                console.warn(\"⚠️ P/L is exactly 0 - this might indicate an issue:\\n                - Are prices changing between BUY and SELL?\\n                - Is the market price fluctuation working?\\n                - Current market price: \".concat(state.currentMarketPrice));\n                            }\n                            console.log(\"\\uD83D\\uDCB0 StablecoinSwap P/L Calculation DETAILED:\\n              - Inferior Row Counter: \".concat(inferiorRow.counter, \"\\n              - Original Cost (Crypto2): \").concat(originalCost, \"\\n              - Crypto2 Reacquired: \").concat(crypto2Reacquired, \"\\n              - Realized P/L (Crypto2): \").concat(realizedProfitInCrypto2, \"\\n              - Crypto1 Stablecoin Price: \").concat(crypto1StablecoinPrice, \"\\n              - Realized P/L (Crypto1): \").concat(realizedProfitCrypto1, \"\\n              - Is Profitable: \").concat(realizedProfitInCrypto2 > 0 ? 'YES' : 'NO', \"\\n              - Calculation: \").concat(crypto2Reacquired, \" - \").concat(originalCost, \" = \").concat(realizedProfitInCrypto2));\n                            // Update Row N-1: Full → Free, Level UNCHANGED, Vars cleared\n                            const updatedInferiorRow = {\n                                ...inferiorRow,\n                                status: \"Free\",\n                                // orderLevel: REMAINS UNCHANGED per specification\n                                crypto1AmountHeld: undefined,\n                                originalCostCrypto2: undefined,\n                                valueLevel: config.baseBid * Math.pow(config.multiplier, inferiorRow.orderLevel),\n                                crypto1Var: 0,\n                                crypto2Var: 0\n                            };\n                            dispatch({\n                                type: 'UPDATE_TARGET_PRICE_ROW',\n                                payload: updatedInferiorRow\n                            });\n                            dispatch({\n                                type: 'UPDATE_BALANCES',\n                                payload: {\n                                    crypto1: currentCrypto1Balance - amountCrypto1ToSell,\n                                    crypto2: currentCrypto2Balance + crypto2Reacquired\n                                }\n                            });\n                            // Add history entries for both steps of the N-1 stablecoin swap\n                            // Step A: SELL Crypto1 for Stablecoin (with profit/loss data for analytics)\n                            console.log(\"\\uD83D\\uDCCA Adding StablecoinSwap SELL order to history with P/L:\", {\n                                realizedProfitLossCrypto2: realizedProfitInCrypto2,\n                                realizedProfitLossCrypto1: realizedProfitCrypto1,\n                                pair: \"\".concat(config.crypto1, \"/\").concat(config.preferredStablecoin),\n                                orderType: \"SELL\",\n                                amountCrypto1: amountCrypto1ToSell,\n                                avgPrice: crypto1StablecoinPrice,\n                                valueCrypto2: stablecoinFromC1Sell,\n                                price1: crypto1StablecoinPrice,\n                                crypto1Symbol: config.crypto1,\n                                crypto2Symbol: config.preferredStablecoin\n                            });\n                            dispatch({\n                                type: 'ADD_ORDER_HISTORY_ENTRY',\n                                payload: {\n                                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                    timestamp: Date.now(),\n                                    pair: \"\".concat(config.crypto1, \"/\").concat(config.preferredStablecoin),\n                                    crypto1: config.crypto1,\n                                    orderType: \"SELL\",\n                                    amountCrypto1: amountCrypto1ToSell,\n                                    avgPrice: crypto1StablecoinPrice,\n                                    valueCrypto2: stablecoinFromC1Sell,\n                                    price1: crypto1StablecoinPrice,\n                                    crypto1Symbol: config.crypto1 || '',\n                                    crypto2Symbol: config.preferredStablecoin || ''\n                                }\n                            });\n                            // Step B: BUY Crypto2 with Stablecoin (no profit/loss as it's just conversion)\n                            dispatch({\n                                type: 'ADD_ORDER_HISTORY_ENTRY',\n                                payload: {\n                                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                    timestamp: Date.now(),\n                                    pair: \"\".concat(config.crypto2, \"/\").concat(config.preferredStablecoin),\n                                    crypto1: config.crypto2,\n                                    orderType: \"BUY\",\n                                    amountCrypto1: crypto2Reacquired,\n                                    avgPrice: crypto2StablecoinPrice,\n                                    valueCrypto2: stablecoinFromC1Sell,\n                                    price1: crypto2StablecoinPrice,\n                                    crypto1Symbol: config.crypto2 || '',\n                                    crypto2Symbol: config.preferredStablecoin || ''\n                                }\n                            });\n                            console.log(\"✅ STABLECOIN SELL: Counter \".concat(currentCounter - 1, \" | Step A: Sold \").concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \" → \").concat(stablecoinFromC1Sell.toFixed(2), \" \").concat(config.preferredStablecoin, \" | Step B: Bought \").concat(crypto2Reacquired.toFixed(2), \" \").concat(config.crypto2, \" | Profit: \").concat(realizedProfitInCrypto2.toFixed(2), \" \").concat(config.crypto2, \" | Level: \").concat(inferiorRow.orderLevel, \" (unchanged)\"));\n                            toast({\n                                title: \"SELL Executed (Stablecoin)\",\n                                description: \"Counter \".concat(currentCounter - 1, \": Profit \").concat(realizedProfitInCrypto2.toFixed(2), \" \").concat(config.crypto2, \" via \").concat(config.preferredStablecoin),\n                                duration: 2000\n                            });\n                            playSound('soundOrderExecution');\n                            // Send Telegram notification for Stablecoin SELL\n                            const profitEmoji = realizedProfitInCrypto2 > 0 ? '📈' : realizedProfitInCrypto2 < 0 ? '📉' : '➖';\n                            sendTelegramNotification(\"\\uD83D\\uDD34 <b>SELL EXECUTED (Stablecoin Swap)</b>\\n\" + \"\\uD83D\\uDCCA Counter: \".concat(currentCounter - 1, \"\\n\") + \"\\uD83D\\uDD04 Step A: Sold \".concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \" → \").concat(stablecoinFromC1Sell.toFixed(2), \" \").concat(config.preferredStablecoin, \"\\n\") + \"\\uD83D\\uDD04 Step B: Bought \".concat(crypto2Reacquired.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\".concat(profitEmoji, \" Profit: \").concat(realizedProfitInCrypto2.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\\uD83D\\uDCCA Level: \".concat(inferiorRow.orderLevel, \" (unchanged)\\n\") + \"\\uD83D\\uDCC8 Mode: Stablecoin Swap\");\n                            actionsTaken++;\n                            // Update local balance for subsequent checks in this cycle\n                            currentCrypto1Balance -= amountCrypto1ToSell;\n                            currentCrypto2Balance += crypto2Reacquired;\n                        }\n                    }\n                }\n            }\n            if (actionsTaken > 0) {\n                console.log(\"\\uD83C\\uDFAF CYCLE COMPLETE: \".concat(actionsTaken, \" actions taken at price $\").concat(currentMarketPrice.toFixed(2)));\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus,\n        state.currentMarketPrice,\n        state.targetPriceRows,\n        state.config,\n        state.crypto1Balance,\n        state.crypto2Balance,\n        state.stablecoinBalance,\n        dispatch,\n        toast,\n        playSound,\n        sendTelegramNotification\n    ]);\n    const getDisplayOrders = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[getDisplayOrders]\": ()=>{\n            if (!state.targetPriceRows || !Array.isArray(state.targetPriceRows)) {\n                return [];\n            }\n            return state.targetPriceRows.map({\n                \"TradingProvider.useCallback[getDisplayOrders]\": (row)=>{\n                    const currentPrice = state.currentMarketPrice || 0;\n                    const targetPrice = row.targetPrice || 0;\n                    const percentFromActualPrice = currentPrice && targetPrice ? (currentPrice / targetPrice - 1) * 100 : 0;\n                    let incomeCrypto1;\n                    let incomeCrypto2;\n                    if (row.status === \"Full\" && row.crypto1AmountHeld && row.originalCostCrypto2) {\n                        const totalUnrealizedProfitInCrypto2 = currentPrice * row.crypto1AmountHeld - row.originalCostCrypto2;\n                        incomeCrypto2 = totalUnrealizedProfitInCrypto2 * state.config.incomeSplitCrypto2Percent / 100;\n                        if (currentPrice > 0) {\n                            incomeCrypto1 = totalUnrealizedProfitInCrypto2 * state.config.incomeSplitCrypto1Percent / 100 / currentPrice;\n                        }\n                    }\n                    return {\n                        ...row,\n                        currentPrice,\n                        priceDifference: targetPrice - currentPrice,\n                        priceDifferencePercent: currentPrice > 0 ? (targetPrice - currentPrice) / currentPrice * 100 : 0,\n                        potentialProfitCrypto1: state.config.incomeSplitCrypto1Percent / 100 * row.valueLevel / (targetPrice || 1),\n                        potentialProfitCrypto2: state.config.incomeSplitCrypto2Percent / 100 * row.valueLevel,\n                        percentFromActualPrice,\n                        incomeCrypto1,\n                        incomeCrypto2\n                    };\n                }\n            }[\"TradingProvider.useCallback[getDisplayOrders]\"]).sort({\n                \"TradingProvider.useCallback[getDisplayOrders]\": (a, b)=>b.targetPrice - a.targetPrice\n            }[\"TradingProvider.useCallback[getDisplayOrders]\"]);\n        }\n    }[\"TradingProvider.useCallback[getDisplayOrders]\"], [\n        state.targetPriceRows,\n        state.currentMarketPrice,\n        state.config.incomeSplitCrypto1Percent,\n        state.config.incomeSplitCrypto2Percent,\n        state.config.baseBid,\n        state.config.multiplier\n    ]);\n    // Backend Integration Functions\n    const saveConfigToBackend = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[saveConfigToBackend]\": async (config)=>{\n            try {\n                var _response_config;\n                const configData = {\n                    name: \"\".concat(config.crypto1, \"/\").concat(config.crypto2, \" \").concat(config.tradingMode),\n                    tradingMode: config.tradingMode,\n                    crypto1: config.crypto1,\n                    crypto2: config.crypto2,\n                    baseBid: config.baseBid,\n                    multiplier: config.multiplier,\n                    numDigits: config.numDigits,\n                    slippagePercent: config.slippagePercent,\n                    incomeSplitCrypto1Percent: config.incomeSplitCrypto1Percent,\n                    incomeSplitCrypto2Percent: config.incomeSplitCrypto2Percent,\n                    preferredStablecoin: config.preferredStablecoin,\n                    targetPrices: state.targetPriceRows.map({\n                        \"TradingProvider.useCallback[saveConfigToBackend]\": (row)=>row.targetPrice\n                    }[\"TradingProvider.useCallback[saveConfigToBackend]\"])\n                };\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.tradingApi.saveConfig(configData);\n                console.log('✅ Config saved to backend:', response);\n                return ((_response_config = response.config) === null || _response_config === void 0 ? void 0 : _response_config.id) || null;\n            } catch (error) {\n                console.error('❌ Failed to save config to backend:', error);\n                toast({\n                    title: \"Backend Error\",\n                    description: \"Failed to save configuration to backend\",\n                    variant: \"destructive\",\n                    duration: 3000\n                });\n                return null;\n            }\n        }\n    }[\"TradingProvider.useCallback[saveConfigToBackend]\"], [\n        state.targetPriceRows,\n        toast\n    ]);\n    const startBackendBot = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[startBackendBot]\": async (configId)=>{\n            try {\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.tradingApi.startBot(configId);\n                console.log('✅ Bot started on backend:', response);\n                toast({\n                    title: \"Bot Started\",\n                    description: \"Trading bot started successfully on backend\",\n                    duration: 3000\n                });\n                return true;\n            } catch (error) {\n                console.error('❌ Failed to start bot on backend:', error);\n                toast({\n                    title: \"Backend Error\",\n                    description: \"Failed to start bot on backend\",\n                    variant: \"destructive\",\n                    duration: 3000\n                });\n                return false;\n            }\n        }\n    }[\"TradingProvider.useCallback[startBackendBot]\"], [\n        toast\n    ]);\n    const stopBackendBot = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[stopBackendBot]\": async (configId)=>{\n            try {\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.tradingApi.stopBot(configId);\n                console.log('✅ Bot stopped on backend:', response);\n                toast({\n                    title: \"Bot Stopped\",\n                    description: \"Trading bot stopped successfully on backend\",\n                    duration: 3000\n                });\n                return true;\n            } catch (error) {\n                console.error('❌ Failed to stop bot on backend:', error);\n                toast({\n                    title: \"Backend Error\",\n                    description: \"Failed to stop bot on backend\",\n                    variant: \"destructive\",\n                    duration: 3000\n                });\n                return false;\n            }\n        }\n    }[\"TradingProvider.useCallback[stopBackendBot]\"], [\n        toast\n    ]);\n    const checkBackendStatus = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[checkBackendStatus]\": async ()=>{\n            const apiUrl = \"http://localhost:5000\";\n            if (!apiUrl) {\n                console.error('Error: NEXT_PUBLIC_API_URL is not defined. Backend connectivity check cannot be performed.');\n                dispatch({\n                    type: 'SET_BACKEND_STATUS',\n                    payload: 'offline'\n                });\n                return;\n            }\n            try {\n                const healthResponse = await fetch(\"\".concat(apiUrl, \"/health/\"));\n                if (!healthResponse.ok) {\n                    // Log more details if the response was received but not OK\n                    console.error(\"Backend health check failed with status: \".concat(healthResponse.status, \" \").concat(healthResponse.statusText));\n                    const responseText = await healthResponse.text().catch({\n                        \"TradingProvider.useCallback[checkBackendStatus]\": ()=>'Could not read response text.'\n                    }[\"TradingProvider.useCallback[checkBackendStatus]\"]);\n                    console.error('Backend health check response body:', responseText);\n                }\n                dispatch({\n                    type: 'SET_BACKEND_STATUS',\n                    payload: healthResponse.ok ? 'online' : 'offline'\n                });\n            } catch (error) {\n                dispatch({\n                    type: 'SET_BACKEND_STATUS',\n                    payload: 'offline'\n                });\n                console.error('Backend connectivity check failed. Error details:', error);\n                if (error.cause) {\n                    console.error('Fetch error cause:', error.cause);\n                }\n                // It's also useful to log the apiUrl to ensure it's what we expect\n                console.error('Attempted to fetch API URL:', \"\".concat(apiUrl, \"/health/\"));\n            }\n        }\n    }[\"TradingProvider.useCallback[checkBackendStatus]\"], [\n        dispatch\n    ]);\n    // Initialize backend status check (one-time only)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            // Initial check for backend status only\n            checkBackendStatus();\n        }\n    }[\"TradingProvider.useEffect\"], [\n        checkBackendStatus\n    ]);\n    // Save state to localStorage whenever it changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            saveStateToLocalStorage(state);\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state\n    ]);\n    // Effect to handle bot warm-up period (immediate execution)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            if (state.botSystemStatus === 'WarmingUp') {\n                console.log(\"Bot is Warming Up... Immediate execution enabled.\");\n                // Immediate transition to Running state - no delays\n                dispatch({\n                    type: 'SYSTEM_COMPLETE_WARMUP'\n                });\n                console.log(\"Bot is now Running immediately.\");\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus,\n        dispatch\n    ]);\n    // Effect to handle session runtime tracking\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            const currentSessionId = sessionManager.getCurrentSessionId();\n            if (currentSessionId) {\n                if (state.botSystemStatus === 'Running') {\n                    // Bot started running, start runtime tracking\n                    sessionManager.startSessionRuntime(currentSessionId);\n                    console.log('✅ Started runtime tracking for session:', currentSessionId);\n                } else if (state.botSystemStatus === 'Stopped') {\n                    // Bot stopped, stop runtime tracking\n                    sessionManager.stopSessionRuntime(currentSessionId);\n                    console.log('⏹️ Stopped runtime tracking for session:', currentSessionId);\n                }\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus\n    ]);\n    // Auto-create session when bot actually starts running (not during warmup)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            // Check if we need to create a session when bot actually starts running\n            if (state.botSystemStatus === 'Running' && !sessionManager.getCurrentSessionId()) {\n                // Only create if we have valid crypto configuration AND target prices set\n                // This prevents auto-creation for fresh windows\n                if (state.config.crypto1 && state.config.crypto2 && state.targetPriceRows.length > 0) {\n                    sessionManager.createNewSessionWithAutoName(state.config).then({\n                        \"TradingProvider.useEffect\": (sessionId)=>{\n                            sessionManager.setCurrentSession(sessionId);\n                            console.log('✅ Auto-created session when bot started:', sessionId);\n                        }\n                    }[\"TradingProvider.useEffect\"]).catch({\n                        \"TradingProvider.useEffect\": (error)=>{\n                            console.error('❌ Failed to auto-create session:', error);\n                        }\n                    }[\"TradingProvider.useEffect\"]);\n                }\n            }\n            // Handle runtime tracking based on bot status\n            const currentSessionId = sessionManager.getCurrentSessionId();\n            if (currentSessionId) {\n                if (state.botSystemStatus === 'Running') {\n                    // Start runtime tracking when bot becomes active\n                    sessionManager.startSessionRuntime(currentSessionId);\n                    console.log('⏱️ Started runtime tracking for session:', currentSessionId);\n                } else if (state.botSystemStatus === 'Stopped') {\n                    // Stop runtime tracking when bot stops\n                    sessionManager.stopSessionRuntime(currentSessionId);\n                    console.log('⏹️ Stopped runtime tracking for session:', currentSessionId);\n                }\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus,\n        state.config.crypto1,\n        state.config.crypto2\n    ]);\n    // Handle crypto pair changes during active trading\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            const currentSessionId = sessionManager.getCurrentSessionId();\n            if (currentSessionId) {\n                const currentSession = sessionManager.loadSession(currentSessionId);\n                // Check if crypto pair has changed from the current session\n                if (currentSession && (currentSession.config.crypto1 !== state.config.crypto1 || currentSession.config.crypto2 !== state.config.crypto2)) {\n                    console.log('🔄 Crypto pair changed during session, auto-saving and resetting...');\n                    // Auto-save current session if bot was running or has data\n                    if (state.botSystemStatus === 'Running' || state.targetPriceRows.length > 0 || state.orderHistory.length > 0) {\n                        // Stop the bot if it's running\n                        if (state.botSystemStatus === 'Running') {\n                            stopBackendBot(currentSessionId);\n                        }\n                        // Save current session with timestamp\n                        const timestamp = new Date().toLocaleString('en-US', {\n                            month: 'short',\n                            day: 'numeric',\n                            hour: '2-digit',\n                            minute: '2-digit',\n                            hour12: false\n                        });\n                        const savedName = \"\".concat(currentSession.name, \" (AutoSaved \").concat(timestamp, \")\");\n                        sessionManager.createNewSession(savedName, currentSession.config).then({\n                            \"TradingProvider.useEffect\": async (savedSessionId)=>{\n                                await sessionManager.saveSession(savedSessionId, currentSession.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, false);\n                                console.log('💾 AutoSaved session:', savedName);\n                            }\n                        }[\"TradingProvider.useEffect\"]);\n                    }\n                    // Reset for new crypto pair\n                    dispatch({\n                        type: 'RESET_FOR_NEW_CRYPTO'\n                    });\n                    // Only create new session for the new crypto pair if bot was actually running\n                    if (state.config.crypto1 && state.config.crypto2 && state.botSystemStatus === 'Running') {\n                        const currentBalances = {\n                            crypto1: state.crypto1Balance,\n                            crypto2: state.crypto2Balance,\n                            stablecoin: state.stablecoinBalance\n                        };\n                        sessionManager.createNewSessionWithAutoName(state.config, undefined, currentBalances).then({\n                            \"TradingProvider.useEffect\": (newSessionId)=>{\n                                sessionManager.setCurrentSession(newSessionId);\n                                console.log('🆕 Created new session for crypto pair:', state.config.crypto1, '/', state.config.crypto2, 'with balances:', currentBalances);\n                                toast({\n                                    title: \"Crypto Pair Changed\",\n                                    description: \"Previous session AutoSaved. New session created for \".concat(state.config.crypto1, \"/\").concat(state.config.crypto2),\n                                    duration: 5000\n                                });\n                            }\n                        }[\"TradingProvider.useEffect\"]);\n                    } else {\n                        // If bot wasn't running, just clear the current session without creating a new one\n                        sessionManager.clearCurrentSession();\n                        console.log('🔄 Crypto pair changed but bot wasn\\'t running - cleared current session');\n                    }\n                }\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.config.crypto1,\n        state.config.crypto2\n    ]);\n    // Initialize network monitoring and auto-save\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const networkMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.NetworkMonitor.getInstance();\n            const autoSaveManager = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.AutoSaveManager.getInstance();\n            const memoryMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.MemoryMonitor.getInstance();\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            // Set up network status listener\n            const unsubscribeNetwork = networkMonitor.addListener({\n                \"TradingProvider.useEffect.unsubscribeNetwork\": (isOnline, isInitial)=>{\n                    console.log(\"\\uD83C\\uDF10 Network status changed: \".concat(isOnline ? 'Online' : 'Offline'));\n                    // Handle offline state - stop bot and save session\n                    if (!isOnline && !isInitial) {\n                        // Stop the bot if it's running\n                        if (state.botSystemStatus === 'Running') {\n                            console.log('🔴 Internet lost - stopping bot and saving session');\n                            dispatch({\n                                type: 'SYSTEM_STOP_BOT'\n                            });\n                            // Auto-save current session\n                            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n                            const currentSessionId = sessionManager.getCurrentSessionId();\n                            if (currentSessionId) {\n                                const currentSession = sessionManager.loadSession(currentSessionId);\n                                if (currentSession) {\n                                    // Create offline backup session\n                                    const timestamp = new Date().toLocaleString('en-US', {\n                                        month: 'short',\n                                        day: 'numeric',\n                                        hour: '2-digit',\n                                        minute: '2-digit',\n                                        hour12: false\n                                    });\n                                    const offlineName = \"\".concat(currentSession.name, \" (Offline Backup \").concat(timestamp, \")\");\n                                    sessionManager.createNewSession(offlineName, currentSession.config).then({\n                                        \"TradingProvider.useEffect.unsubscribeNetwork\": async (backupSessionId)=>{\n                                            await sessionManager.saveSession(backupSessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, false);\n                                            console.log('💾 Created offline backup session:', offlineName);\n                                        }\n                                    }[\"TradingProvider.useEffect.unsubscribeNetwork\"]);\n                                }\n                            }\n                        }\n                        toast({\n                            title: \"Network Disconnected\",\n                            description: \"Bot stopped and session saved. Trading paused until connection restored.\",\n                            variant: \"destructive\",\n                            duration: 8000\n                        });\n                    } else if (isOnline && !isInitial) {\n                        toast({\n                            title: \"Network Reconnected\",\n                            description: \"Connection restored. You can resume trading.\",\n                            duration: 3000\n                        });\n                    }\n                }\n            }[\"TradingProvider.useEffect.unsubscribeNetwork\"]);\n            // Set up memory monitoring\n            const unsubscribeMemory = memoryMonitor.addListener({\n                \"TradingProvider.useEffect.unsubscribeMemory\": (memory)=>{\n                    const usedMB = memory.usedJSHeapSize / 1024 / 1024;\n                    if (usedMB > 150) {\n                        console.warn(\"\\uD83E\\uDDE0 High memory usage: \".concat(usedMB.toFixed(2), \"MB\"));\n                        toast({\n                            title: \"High Memory Usage\",\n                            description: \"Memory usage is high (\".concat(usedMB.toFixed(0), \"MB). Consider refreshing the page.\"),\n                            variant: \"destructive\",\n                            duration: 5000\n                        });\n                    }\n                }\n            }[\"TradingProvider.useEffect.unsubscribeMemory\"]);\n            // Set up auto-save\n            const saveFunction = {\n                \"TradingProvider.useEffect.saveFunction\": async ()=>{\n                    try {\n                        // Save to session manager if we have a current session\n                        const currentSessionId = sessionManager.getCurrentSessionId();\n                        if (currentSessionId) {\n                            await sessionManager.saveSession(currentSessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, state.botSystemStatus === 'Running');\n                        }\n                        // Also save to localStorage as backup\n                        saveStateToLocalStorage(state);\n                    } catch (error) {\n                        console.error('Auto-save failed:', error);\n                    }\n                }\n            }[\"TradingProvider.useEffect.saveFunction\"];\n            autoSaveManager.enable(saveFunction, 30000); // Auto-save every 30 seconds\n            // Add beforeunload listener to save session on browser close/refresh\n            const handleBeforeUnload = {\n                \"TradingProvider.useEffect.handleBeforeUnload\": (event)=>{\n                    // Save immediately before unload\n                    saveFunction();\n                    // If bot is running, show warning\n                    if (state.botSystemStatus === 'Running') {\n                        const message = 'Trading bot is currently running. Are you sure you want to leave?';\n                        event.returnValue = message;\n                        return message;\n                    }\n                }\n            }[\"TradingProvider.useEffect.handleBeforeUnload\"];\n            window.addEventListener('beforeunload', handleBeforeUnload);\n            // Cleanup function\n            return ({\n                \"TradingProvider.useEffect\": ()=>{\n                    unsubscribeNetwork();\n                    unsubscribeMemory();\n                    autoSaveManager.disable();\n                    window.removeEventListener('beforeunload', handleBeforeUnload);\n                }\n            })[\"TradingProvider.useEffect\"];\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state,\n        toast\n    ]);\n    // Force save when bot status changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const autoSaveManager = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.AutoSaveManager.getInstance();\n            autoSaveManager.saveNow();\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus\n    ]);\n    // Manual save session function\n    const saveCurrentSession = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[saveCurrentSession]\": async ()=>{\n            try {\n                const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n                const currentSessionId = sessionManager.getCurrentSessionId();\n                if (!currentSessionId) {\n                    // No current session, only create one if bot is running or has meaningful data\n                    if (state.config.crypto1 && state.config.crypto2 && (state.botSystemStatus === 'Running' || state.targetPriceRows.length > 0 || state.orderHistory.length > 0)) {\n                        const currentBalances = {\n                            crypto1: state.crypto1Balance,\n                            crypto2: state.crypto2Balance,\n                            stablecoin: state.stablecoinBalance\n                        };\n                        const sessionId = await sessionManager.createNewSessionWithAutoName(state.config, undefined, currentBalances);\n                        sessionManager.setCurrentSession(sessionId);\n                        await sessionManager.saveSession(sessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, state.botSystemStatus === 'Running');\n                        return true;\n                    }\n                    console.log('⚠️ No session to save - bot not running and no meaningful data');\n                    return false;\n                }\n                // Save existing session\n                return await sessionManager.saveSession(currentSessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, state.botSystemStatus === 'Running');\n            } catch (error) {\n                console.error('Failed to save current session:', error);\n                return false;\n            }\n        }\n    }[\"TradingProvider.useCallback[saveCurrentSession]\"], [\n        state\n    ]);\n    // Context value\n    const contextValue = {\n        ...state,\n        dispatch,\n        setTargetPrices,\n        getDisplayOrders,\n        checkBackendStatus,\n        fetchMarketPrice,\n        startBackendBot,\n        stopBackendBot,\n        saveConfigToBackend,\n        saveCurrentSession,\n        backendStatus: state.backendStatus,\n        botSystemStatus: state.botSystemStatus,\n        isBotActive: state.botSystemStatus === 'Running'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TradingContext.Provider, {\n        value: contextValue,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\contexts\\\\TradingContext.tsx\",\n        lineNumber: 1891,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TradingProvider, \"Dul/342NNfDBwKH7syILMVemn7Y=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__.useToast\n    ];\n});\n_c = TradingProvider;\n// Custom hook to use the trading context\nconst useTradingContext = ()=>{\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(TradingContext);\n    if (context === undefined) {\n        throw new Error('useTradingContext must be used within a TradingProvider');\n    }\n    return context;\n};\n_s1(useTradingContext, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"TradingProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/TradingContext.tsx\n"));

/***/ })

});