"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/contexts/TradingContext.tsx":
/*!*****************************************!*\
  !*** ./src/contexts/TradingContext.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TradingProvider: () => (/* binding */ TradingProvider),\n/* harmony export */   useTradingContext: () => (/* binding */ useTradingContext)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/types */ \"(app-pages-browser)/./src/lib/types.tsx\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/session-manager */ \"(app-pages-browser)/./src/lib/session-manager.ts\");\n/* harmony import */ var _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/network-monitor */ \"(app-pages-browser)/./src/lib/network-monitor.ts\");\n/* __next_internal_client_entry_do_not_use__ TradingProvider,useTradingContext auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n// Define locally to avoid import issues\nconst DEFAULT_QUOTE_CURRENCIES = [\n    \"USDT\",\n    \"USDC\",\n    \"BTC\"\n];\n\n\n\n\n// Static fallback prices (used when API is unavailable)\nconst getStaticUSDPrice = (crypto)=>{\n    const usdPrices = {\n        // Major cryptocurrencies - Updated to current market prices\n        'BTC': 106000,\n        'ETH': 2500,\n        'SOL': 180,\n        'ADA': 0.85,\n        'DOGE': 0.32,\n        'LINK': 22,\n        'MATIC': 0.42,\n        'DOT': 6.5,\n        'AVAX': 38,\n        'SHIB': 0.000022,\n        'XRP': 2.1,\n        'LTC': 95,\n        'BCH': 420,\n        // Stablecoins\n        'USDT': 1.0,\n        'USDC': 1.0,\n        'FDUSD': 1.0,\n        'BUSD': 1.0,\n        'DAI': 1.0\n    };\n    return usdPrices[crypto.toUpperCase()] || 100;\n};\n// Static fallback function for market price calculation (no fluctuations)\nconst calculateStaticMarketPrice = (config)=>{\n    const crypto1USDPrice = getStaticUSDPrice(config.crypto1);\n    const crypto2USDPrice = getStaticUSDPrice(config.crypto2);\n    // Calculate the ratio: how many units of crypto2 = 1 unit of crypto1\n    const basePrice = crypto1USDPrice / crypto2USDPrice;\n    console.log(\"\\uD83D\\uDCCA Static price calculation: \".concat(config.crypto1, \" ($\").concat(crypto1USDPrice, \") / \").concat(config.crypto2, \" ($\").concat(crypto2USDPrice, \") = \").concat(basePrice.toFixed(6)));\n    return basePrice;\n};\n// Add this function to calculate the initial market price\nconst calculateInitialMarketPrice = (config)=>{\n    // Return 0 if either crypto is not selected\n    if (!config.crypto1 || !config.crypto2) {\n        return 0;\n    }\n    // Default fallback value for valid pairs\n    return calculateStaticMarketPrice(config);\n};\n// Get real market price with multiple fallback strategies\nconst getRealBinancePrice = async (config)=>{\n    try {\n        // Return 0 if either crypto is not selected\n        if (!config.crypto1 || !config.crypto2) {\n            return 0;\n        }\n        const pair = \"\".concat(config.crypto1).concat(config.crypto2);\n        // Strategy 1: Try public backend endpoint (no auth required)\n        try {\n            const response = await fetch(\"http://localhost:5000/trading/public/price/\".concat(pair), {\n                method: 'GET',\n                headers: {\n                    'Accept': 'application/json',\n                    'Content-Type': 'application/json'\n                },\n                signal: AbortSignal.timeout(5000) // 5 second timeout\n            });\n            if (response.ok) {\n                const data = await response.json();\n                if (data.price > 0) {\n                    console.log(\"✅ Backend Binance price: \".concat(config.crypto1, \"/\").concat(config.crypto2, \" = \").concat(data.price));\n                    return data.price;\n                }\n            }\n        } catch (error) {\n            console.warn(\"⚠️ Backend API failed for \".concat(pair, \":\"), error);\n        }\n        // Strategy 2: Try CoinGecko API as reliable fallback\n        try {\n            const crypto1Id = getCoinGeckoId(config.crypto1);\n            const crypto2Id = getCoinGeckoId(config.crypto2);\n            if (crypto1Id && crypto2Id) {\n                console.log(\"\\uD83D\\uDD04 Trying CoinGecko API for \".concat(crypto1Id, \" vs \").concat(crypto2Id));\n                const controller = new AbortController();\n                const timeoutId = setTimeout(()=>controller.abort(), 8000); // 8 second timeout\n                const response = await fetch(\"https://api.coingecko.com/api/v3/simple/price?ids=\".concat(crypto1Id, \"&vs_currencies=\").concat(crypto2Id), {\n                    method: 'GET',\n                    headers: {\n                        'Accept': 'application/json',\n                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'\n                    },\n                    signal: controller.signal\n                });\n                clearTimeout(timeoutId);\n                if (response.ok) {\n                    var _data_crypto1Id;\n                    const data = await response.json();\n                    console.log(\"\\uD83D\\uDCCA CoinGecko response:\", data);\n                    const price = (_data_crypto1Id = data[crypto1Id]) === null || _data_crypto1Id === void 0 ? void 0 : _data_crypto1Id[crypto2Id];\n                    if (price && price > 0) {\n                        console.log(\"✅ CoinGecko price: \".concat(config.crypto1, \"/\").concat(config.crypto2, \" = \").concat(price));\n                        return price;\n                    }\n                } else {\n                    console.warn(\"⚠️ CoinGecko API response not OK:\", response.status, response.statusText);\n                }\n            } else {\n                console.warn(\"⚠️ CoinGecko IDs not found for \".concat(config.crypto1, \"/\").concat(config.crypto2));\n            }\n        } catch (error) {\n            console.warn(\"⚠️ CoinGecko API failed:\", error);\n        }\n        // Strategy 3: Try direct Binance public API (CORS might block this)\n        try {\n            const symbol = \"\".concat(config.crypto1).concat(config.crypto2).toUpperCase();\n            const response = await fetch(\"https://api.binance.com/api/v3/ticker/price?symbol=\".concat(symbol), {\n                method: 'GET',\n                headers: {\n                    'Accept': 'application/json'\n                },\n                signal: AbortSignal.timeout(5000) // 5 second timeout\n            });\n            if (response.ok) {\n                const data = await response.json();\n                const price = parseFloat(data.price);\n                if (price > 0) {\n                    console.log(\"✅ Direct Binance price: \".concat(config.crypto1, \"/\").concat(config.crypto2, \" = \").concat(price));\n                    return price;\n                }\n            }\n        } catch (error) {\n            console.warn(\"⚠️ Direct Binance API failed (CORS expected):\", error);\n        }\n        // If all APIs fail, throw error to use static fallback\n        throw new Error('All price APIs failed');\n    } catch (error) {\n        console.error('❌ Error fetching real price:', error);\n        throw error;\n    }\n};\n// Helper function to map crypto symbols to CoinGecko IDs\nconst getCoinGeckoId = (symbol)=>{\n    const mapping = {\n        'BTC': 'bitcoin',\n        'ETH': 'ethereum',\n        'SOL': 'solana',\n        'ADA': 'cardano',\n        'DOT': 'polkadot',\n        'MATIC': 'matic-network',\n        'AVAX': 'avalanche-2',\n        'LINK': 'chainlink',\n        'UNI': 'uniswap',\n        'USDT': 'tether',\n        'USDC': 'usd-coin',\n        'BUSD': 'binance-usd',\n        'DAI': 'dai'\n    };\n    return mapping[symbol.toUpperCase()] || null;\n};\n// Helper function to get stablecoin exchange rates for real market data\nconst getStablecoinExchangeRate = async (crypto, stablecoin)=>{\n    try {\n        // For stablecoin-to-stablecoin, assume 1:1 rate\n        if (getCoinGeckoId(crypto) && getCoinGeckoId(stablecoin)) {\n            const cryptoId = getCoinGeckoId(crypto);\n            const stablecoinId = getCoinGeckoId(stablecoin);\n            if (cryptoId === stablecoinId) return 1.0; // Same currency\n            // Get real exchange rate from CoinGecko\n            const response = await fetch(\"https://api.coingecko.com/api/v3/simple/price?ids=\".concat(cryptoId, \"&vs_currencies=\").concat(stablecoinId));\n            if (response.ok) {\n                var _data_cryptoId;\n                const data = await response.json();\n                const rate = cryptoId && stablecoinId ? (_data_cryptoId = data[cryptoId]) === null || _data_cryptoId === void 0 ? void 0 : _data_cryptoId[stablecoinId] : null;\n                if (rate > 0) {\n                    console.log(\"\\uD83D\\uDCCA Stablecoin rate: \".concat(crypto, \"/\").concat(stablecoin, \" = \").concat(rate));\n                    return rate;\n                }\n            }\n        }\n        // Fallback: calculate via USD prices\n        const cryptoUSDPrice = getUSDPrice(crypto);\n        const stablecoinUSDPrice = getUSDPrice(stablecoin);\n        const rate = cryptoUSDPrice / stablecoinUSDPrice;\n        console.log(\"\\uD83D\\uDCCA Fallback stablecoin rate: \".concat(crypto, \"/\").concat(stablecoin, \" = \").concat(rate, \" (via USD)\"));\n        return rate;\n    } catch (error) {\n        console.error('Error fetching stablecoin exchange rate:', error);\n        // Final fallback\n        const cryptoUSDPrice = getUSDPrice(crypto);\n        const stablecoinUSDPrice = getUSDPrice(stablecoin);\n        return cryptoUSDPrice / stablecoinUSDPrice;\n    }\n};\n// Real-time price cache\nlet priceCache = {};\nlet lastPriceUpdate = 0;\nconst PRICE_CACHE_DURATION = 2000; // 2 seconds cache for real-time updates\n// Get real USD price with multiple fallback strategies\nconst getRealBinanceUSDPrice = async (crypto)=>{\n    const now = Date.now();\n    const cacheKey = crypto.toUpperCase();\n    // Return cached price if still valid (2 seconds)\n    if (priceCache[cacheKey] && now - lastPriceUpdate < PRICE_CACHE_DURATION) {\n        return priceCache[cacheKey];\n    }\n    try {\n        // Strategy 1: Try public backend API to get real Binance USD price (via USDT)\n        try {\n            const pair = \"\".concat(crypto.toUpperCase(), \"USDT\");\n            const response = await fetch(\"http://localhost:5000/trading/public/price/\".concat(pair), {\n                method: 'GET',\n                headers: {\n                    'Accept': 'application/json',\n                    'Content-Type': 'application/json'\n                },\n                signal: AbortSignal.timeout(5000) // 5 second timeout\n            });\n            if (response.ok) {\n                const data = await response.json();\n                if (data.price > 0) {\n                    priceCache[cacheKey] = data.price;\n                    lastPriceUpdate = now;\n                    console.log(\"\\uD83D\\uDCCA Backend USD price for \".concat(crypto, \": $\").concat(data.price));\n                    return data.price;\n                }\n            }\n        } catch (error) {\n            console.warn(\"⚠️ Backend USD price failed for \".concat(crypto, \":\"), error);\n        }\n        // Strategy 2: Try CoinGecko API as reliable fallback\n        try {\n            const coinGeckoId = getCoinGeckoId(crypto);\n            if (coinGeckoId) {\n                console.log(\"\\uD83D\\uDD04 Trying CoinGecko USD price for \".concat(crypto, \" (\").concat(coinGeckoId, \")\"));\n                const controller = new AbortController();\n                const timeoutId = setTimeout(()=>controller.abort(), 8000); // 8 second timeout\n                const response = await fetch(\"https://api.coingecko.com/api/v3/simple/price?ids=\".concat(coinGeckoId, \"&vs_currencies=usd\"), {\n                    method: 'GET',\n                    headers: {\n                        'Accept': 'application/json',\n                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'\n                    },\n                    signal: controller.signal\n                });\n                clearTimeout(timeoutId);\n                if (response.ok) {\n                    var _data_coinGeckoId;\n                    const data = await response.json();\n                    console.log(\"\\uD83D\\uDCCA CoinGecko USD response for \".concat(crypto, \":\"), data);\n                    const price = (_data_coinGeckoId = data[coinGeckoId]) === null || _data_coinGeckoId === void 0 ? void 0 : _data_coinGeckoId.usd;\n                    if (price && price > 0) {\n                        priceCache[cacheKey] = price;\n                        lastPriceUpdate = now;\n                        console.log(\"\\uD83D\\uDCCA CoinGecko USD price for \".concat(crypto, \": $\").concat(price));\n                        return price;\n                    }\n                } else {\n                    console.warn(\"⚠️ CoinGecko USD API response not OK:\", response.status, response.statusText);\n                }\n            } else {\n                console.warn(\"⚠️ CoinGecko ID not found for \".concat(crypto));\n            }\n        } catch (error) {\n            console.warn(\"⚠️ CoinGecko USD price failed for \".concat(crypto, \":\"), error);\n        }\n        // Strategy 3: Try direct Binance API for USDT price\n        try {\n            const symbol = \"\".concat(crypto.toUpperCase(), \"USDT\");\n            const response = await fetch(\"https://api.binance.com/api/v3/ticker/price?symbol=\".concat(symbol), {\n                method: 'GET',\n                headers: {\n                    'Accept': 'application/json'\n                },\n                signal: AbortSignal.timeout(5000) // 5 second timeout\n            });\n            if (response.ok) {\n                const data = await response.json();\n                const price = parseFloat(data.price);\n                if (price > 0) {\n                    priceCache[cacheKey] = price;\n                    lastPriceUpdate = now;\n                    console.log(\"\\uD83D\\uDCCA Direct Binance USD price for \".concat(crypto, \": $\").concat(price));\n                    return price;\n                }\n            }\n        } catch (error) {\n            console.warn(\"⚠️ Direct Binance USD API failed (CORS expected):\", error);\n        }\n        // If all APIs fail, throw error to use static fallback\n        throw new Error(\"All USD price APIs failed for \".concat(crypto));\n    } catch (error) {\n        console.error(\"❌ Failed to get USD price for \".concat(crypto, \":\"), error);\n        throw error;\n    }\n};\n// Synchronous helper function to get USD price (uses cached values or static fallback)\nconst getUSDPrice = (crypto)=>{\n    const cacheKey = crypto.toUpperCase();\n    // Return cached price if available\n    if (priceCache[cacheKey]) {\n        return priceCache[cacheKey];\n    }\n    // Fallback to static prices\n    return getStaticUSDPrice(crypto);\n};\nconst initialBaseConfig = {\n    tradingMode: \"SimpleSpot\",\n    crypto1: \"\",\n    crypto2: \"\",\n    baseBid: 100,\n    multiplier: 1.005,\n    numDigits: 4,\n    slippagePercent: 0.2,\n    incomeSplitCrypto1Percent: 50,\n    incomeSplitCrypto2Percent: 50,\n    preferredStablecoin: _lib_types__WEBPACK_IMPORTED_MODULE_2__.AVAILABLE_STABLECOINS[0]\n};\nconst initialTradingState = {\n    config: initialBaseConfig,\n    targetPriceRows: [],\n    orderHistory: [],\n    appSettings: _lib_types__WEBPACK_IMPORTED_MODULE_2__.DEFAULT_APP_SETTINGS,\n    currentMarketPrice: calculateInitialMarketPrice(initialBaseConfig),\n    // isBotActive: false, // Remove\n    botSystemStatus: 'Stopped',\n    crypto1Balance: 10,\n    crypto2Balance: 100000,\n    stablecoinBalance: 0,\n    backendStatus: 'unknown'\n};\nconst lastActionTimestampPerCounter = new Map();\n// LocalStorage persistence functions\nconst STORAGE_KEY = 'pluto_trading_state';\nconst saveStateToLocalStorage = (state)=>{\n    try {\n        if (true) {\n            const stateToSave = {\n                config: state.config,\n                targetPriceRows: state.targetPriceRows,\n                orderHistory: state.orderHistory,\n                appSettings: state.appSettings,\n                currentMarketPrice: state.currentMarketPrice,\n                crypto1Balance: state.crypto1Balance,\n                crypto2Balance: state.crypto2Balance,\n                stablecoinBalance: state.stablecoinBalance,\n                botSystemStatus: state.botSystemStatus,\n                timestamp: Date.now()\n            };\n            localStorage.setItem(STORAGE_KEY, JSON.stringify(stateToSave));\n        }\n    } catch (error) {\n        console.error('Failed to save state to localStorage:', error);\n    }\n};\nconst loadStateFromLocalStorage = ()=>{\n    try {\n        if (true) {\n            const savedState = localStorage.getItem(STORAGE_KEY);\n            if (savedState) {\n                const parsed = JSON.parse(savedState);\n                // Check if state is not too old (24 hours)\n                if (parsed.timestamp && Date.now() - parsed.timestamp < 24 * 60 * 60 * 1000) {\n                    return parsed;\n                }\n            }\n        }\n    } catch (error) {\n        console.error('Failed to load state from localStorage:', error);\n    }\n    return null;\n};\nconst tradingReducer = (state, action)=>{\n    switch(action.type){\n        case 'SET_CONFIG':\n            const newConfig = {\n                ...state.config,\n                ...action.payload\n            };\n            // If trading pair changes, reset market price (it will be re-calculated by effect)\n            if (action.payload.crypto1 || action.payload.crypto2) {\n                return {\n                    ...state,\n                    config: newConfig,\n                    currentMarketPrice: calculateInitialMarketPrice(newConfig)\n                };\n            }\n            return {\n                ...state,\n                config: newConfig\n            };\n        case 'SET_TARGET_PRICE_ROWS':\n            return {\n                ...state,\n                targetPriceRows: action.payload.sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }))\n            };\n        case 'ADD_TARGET_PRICE_ROW':\n            {\n                const newRows = [\n                    ...state.targetPriceRows,\n                    action.payload\n                ].sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }));\n                return {\n                    ...state,\n                    targetPriceRows: newRows\n                };\n            }\n        case 'UPDATE_TARGET_PRICE_ROW':\n            {\n                const updatedRows = state.targetPriceRows.map((row)=>row.id === action.payload.id ? action.payload : row).sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }));\n                return {\n                    ...state,\n                    targetPriceRows: updatedRows\n                };\n            }\n        case 'REMOVE_TARGET_PRICE_ROW':\n            {\n                const filteredRows = state.targetPriceRows.filter((row)=>row.id !== action.payload).sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }));\n                return {\n                    ...state,\n                    targetPriceRows: filteredRows\n                };\n            }\n        case 'ADD_ORDER_HISTORY_ENTRY':\n            return {\n                ...state,\n                orderHistory: [\n                    action.payload,\n                    ...state.orderHistory\n                ]\n            };\n        case 'CLEAR_ORDER_HISTORY':\n            return {\n                ...state,\n                orderHistory: []\n            };\n        case 'SET_APP_SETTINGS':\n            return {\n                ...state,\n                appSettings: {\n                    ...state.appSettings,\n                    ...action.payload\n                }\n            };\n        case 'SET_MARKET_PRICE':\n            return {\n                ...state,\n                currentMarketPrice: action.payload\n            };\n        // case 'SET_BOT_STATUS': // Removed\n        case 'UPDATE_BALANCES':\n            return {\n                ...state,\n                crypto1Balance: action.payload.crypto1 !== undefined ? action.payload.crypto1 : state.crypto1Balance,\n                crypto2Balance: action.payload.crypto2 !== undefined ? action.payload.crypto2 : state.crypto2Balance,\n                stablecoinBalance: action.payload.stablecoin !== undefined ? action.payload.stablecoin : state.stablecoinBalance\n            };\n        case 'SET_BALANCES':\n            return {\n                ...state,\n                crypto1Balance: action.payload.crypto1,\n                crypto2Balance: action.payload.crypto2,\n                stablecoinBalance: action.payload.stablecoin !== undefined ? action.payload.stablecoin : state.stablecoinBalance\n            };\n        case 'UPDATE_STABLECOIN_BALANCE':\n            return {\n                ...state,\n                stablecoinBalance: action.payload\n            };\n        case 'RESET_SESSION':\n            const configForReset = {\n                ...state.config\n            };\n            return {\n                ...initialTradingState,\n                config: configForReset,\n                appSettings: {\n                    ...state.appSettings\n                },\n                currentMarketPrice: calculateInitialMarketPrice(configForReset),\n                // Preserve current balances instead of resetting to initial values\n                crypto1Balance: state.crypto1Balance,\n                crypto2Balance: state.crypto2Balance,\n                stablecoinBalance: state.stablecoinBalance\n            };\n        case 'SET_BACKEND_STATUS':\n            return {\n                ...state,\n                backendStatus: action.payload\n            };\n        case 'SYSTEM_START_BOT_INITIATE':\n            // Continue from previous state instead of resetting\n            return {\n                ...state,\n                botSystemStatus: 'WarmingUp'\n            };\n        case 'SYSTEM_COMPLETE_WARMUP':\n            return {\n                ...state,\n                botSystemStatus: 'Running'\n            };\n        case 'SYSTEM_STOP_BOT':\n            return {\n                ...state,\n                botSystemStatus: 'Stopped'\n            };\n        case 'SYSTEM_RESET_BOT':\n            // Fresh Start: Clear all target price rows and order history completely\n            lastActionTimestampPerCounter.clear();\n            // Clear current session to force new session name generation\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            sessionManager.clearCurrentSession();\n            return {\n                ...state,\n                botSystemStatus: 'Stopped',\n                targetPriceRows: [],\n                orderHistory: []\n            };\n        case 'SET_TARGET_PRICE_ROWS':\n            return {\n                ...state,\n                targetPriceRows: action.payload\n            };\n        case 'RESET_FOR_NEW_CRYPTO':\n            return {\n                ...initialTradingState,\n                config: state.config,\n                backendStatus: state.backendStatus,\n                botSystemStatus: 'Stopped',\n                currentMarketPrice: 0,\n                // Preserve current balances instead of resetting to initial values\n                crypto1Balance: state.crypto1Balance,\n                crypto2Balance: state.crypto2Balance,\n                stablecoinBalance: state.stablecoinBalance\n            };\n        default:\n            return state;\n    }\n};\nconst TradingContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// SIMPLIFIED LOGIC - NO COMPLEX COOLDOWNS\nconst TradingProvider = (param)=>{\n    let { children } = param;\n    _s();\n    // Initialize state with localStorage data if available\n    const initializeState = ()=>{\n        // Check if this is a new session from URL parameter\n        if (true) {\n            const urlParams = new URLSearchParams(window.location.search);\n            const isNewSession = urlParams.get('newSession') === 'true';\n            if (isNewSession) {\n                console.log('🆕 New session requested - starting with completely fresh state');\n                // Note: URL parameter will be cleared in useEffect to avoid render-time state updates\n                return initialTradingState;\n            }\n        }\n        // Check if this is a new window/tab by checking if we have a current session\n        const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n        const currentSessionId = sessionManager.getCurrentSessionId();\n        // If no current session exists for this window, start fresh\n        if (!currentSessionId) {\n            console.log('🆕 New window detected - starting with fresh state');\n            return initialTradingState;\n        }\n        // If we have a session, try to load the session data first (priority over localStorage)\n        const currentSession = sessionManager.loadSession(currentSessionId);\n        if (currentSession) {\n            console.log('🔄 Loading session data with balances:', {\n                crypto1: currentSession.crypto1Balance,\n                crypto2: currentSession.crypto2Balance,\n                stablecoin: currentSession.stablecoinBalance\n            });\n            return {\n                ...initialTradingState,\n                config: currentSession.config,\n                targetPriceRows: currentSession.targetPriceRows,\n                orderHistory: currentSession.orderHistory,\n                currentMarketPrice: currentSession.currentMarketPrice,\n                crypto1Balance: currentSession.crypto1Balance,\n                crypto2Balance: currentSession.crypto2Balance,\n                stablecoinBalance: currentSession.stablecoinBalance,\n                botSystemStatus: currentSession.isActive ? 'Running' : 'Stopped'\n            };\n        }\n        // Fallback to localStorage if session loading fails\n        const savedState = loadStateFromLocalStorage();\n        if (savedState) {\n            return {\n                ...initialTradingState,\n                ...savedState\n            };\n        }\n        return initialTradingState;\n    };\n    const [state, dispatch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useReducer)(tradingReducer, initializeState());\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__.useToast)();\n    // Clear URL parameter after component mounts to avoid render-time state updates\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            if (true) {\n                const urlParams = new URLSearchParams(window.location.search);\n                const isNewSession = urlParams.get('newSession') === 'true';\n                if (isNewSession) {\n                    // Clear the URL parameter to avoid confusion\n                    const newUrl = window.location.pathname;\n                    window.history.replaceState({}, '', newUrl);\n                }\n            }\n        }\n    }[\"TradingProvider.useEffect\"], []); // Run only once on mount\n    const audioRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Removed processing locks and cooldowns for continuous trading\n    // Initialize fetchMarketPrice to get real prices with reliable fallbacks\n    const fetchMarketPrice = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[fetchMarketPrice]\": async ()=>{\n            try {\n                // Don't fetch price if either crypto is not selected\n                if (!state.config.crypto1 || !state.config.crypto2) {\n                    dispatch({\n                        type: 'SET_MARKET_PRICE',\n                        payload: 0\n                    });\n                    return;\n                }\n                let price;\n                try {\n                    if (state.config.tradingMode === \"StablecoinSwap\") {\n                        // For StablecoinSwap mode, get USD prices and calculate ratio\n                        const [crypto1USDPrice, crypto2USDPrice] = await Promise.all([\n                            getRealBinanceUSDPrice(state.config.crypto1),\n                            getRealBinanceUSDPrice(state.config.crypto2)\n                        ]);\n                        price = crypto1USDPrice / crypto2USDPrice;\n                        console.log(\"\\uD83D\\uDCCA StablecoinSwap Real Price:\\n            - \".concat(state.config.crypto1, \" USD Price: $\").concat(crypto1USDPrice.toLocaleString(), \"\\n            - \").concat(state.config.crypto2, \" USD Price: $\").concat(crypto2USDPrice.toLocaleString(), \"\\n            - Market Price (\").concat(state.config.crypto1, \"/\").concat(state.config.crypto2, \"): \").concat(price.toFixed(6)));\n                    } else {\n                        // For SimpleSpot mode, get direct pair price\n                        price = await getRealBinancePrice(state.config);\n                    }\n                    dispatch({\n                        type: 'SET_MARKET_PRICE',\n                        payload: price\n                    });\n                    console.log(\"✅ Successfully fetched real price: \".concat(price));\n                } catch (error) {\n                    console.warn('⚠️ Real API failed, using static price:', error);\n                    // Use static fallback if real API fails\n                    const fallbackPrice = calculateStaticMarketPrice(state.config);\n                    dispatch({\n                        type: 'SET_MARKET_PRICE',\n                        payload: fallbackPrice\n                    });\n                    console.log(\"\\uD83D\\uDCCA Using static fallback price: \".concat(fallbackPrice));\n                }\n            } catch (error) {\n                console.error('❌ Critical error in fetchMarketPrice:', error);\n                const fallbackPrice = calculateStaticMarketPrice(state.config);\n                dispatch({\n                    type: 'SET_MARKET_PRICE',\n                    payload: fallbackPrice\n                });\n            }\n        }\n    }[\"TradingProvider.useCallback[fetchMarketPrice]\"], [\n        state.config,\n        dispatch\n    ]);\n    // Market price real-time updates from Binance\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            // Initial fetch\n            fetchMarketPrice();\n            // Set up real-time price updates every 2 seconds from Binance\n            const priceUpdateInterval = setInterval({\n                \"TradingProvider.useEffect.priceUpdateInterval\": async ()=>{\n                    const networkMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.NetworkMonitor.getInstance();\n                    if (networkMonitor.getStatus().isOnline) {\n                        try {\n                            await fetchMarketPrice();\n                            console.log('📊 Binance price updated');\n                        } catch (error) {\n                            console.warn('⚠️ Failed to update price from Binance:', error);\n                        }\n                    }\n                }\n            }[\"TradingProvider.useEffect.priceUpdateInterval\"], 2000); // Update every 2 seconds as requested\n            return ({\n                \"TradingProvider.useEffect\": ()=>{\n                    clearInterval(priceUpdateInterval);\n                }\n            })[\"TradingProvider.useEffect\"];\n        }\n    }[\"TradingProvider.useEffect\"], [\n        fetchMarketPrice,\n        state.config.crypto1,\n        state.config.crypto2,\n        state.config.tradingMode\n    ]);\n    // Audio initialization and user interaction detection\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            if (true) {\n                audioRef.current = new Audio();\n                // Add user interaction listener to enable audio\n                const enableAudio = {\n                    \"TradingProvider.useEffect.enableAudio\": ()=>{\n                        if (audioRef.current) {\n                            // Try to play a silent audio to enable audio context\n                            audioRef.current.volume = 0;\n                            audioRef.current.play().then({\n                                \"TradingProvider.useEffect.enableAudio\": ()=>{\n                                    if (audioRef.current) {\n                                        audioRef.current.volume = 1;\n                                    }\n                                    console.log('🔊 Audio context enabled after user interaction');\n                                }\n                            }[\"TradingProvider.useEffect.enableAudio\"]).catch({\n                                \"TradingProvider.useEffect.enableAudio\": ()=>{\n                                // Still blocked, but we tried\n                                }\n                            }[\"TradingProvider.useEffect.enableAudio\"]);\n                        }\n                        // Remove the listener after first interaction\n                        document.removeEventListener('click', enableAudio);\n                        document.removeEventListener('keydown', enableAudio);\n                    }\n                }[\"TradingProvider.useEffect.enableAudio\"];\n                // Listen for first user interaction\n                document.addEventListener('click', enableAudio);\n                document.addEventListener('keydown', enableAudio);\n                return ({\n                    \"TradingProvider.useEffect\": ()=>{\n                        document.removeEventListener('click', enableAudio);\n                        document.removeEventListener('keydown', enableAudio);\n                    }\n                })[\"TradingProvider.useEffect\"];\n            }\n        }\n    }[\"TradingProvider.useEffect\"], []);\n    const playSound = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[playSound]\": (soundKey)=>{\n            // Get current session's alarm settings, fallback to global app settings\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            const currentSessionId = sessionManager.getCurrentSessionId();\n            let alarmSettings = state.appSettings; // Default fallback\n            if (currentSessionId) {\n                const currentSession = sessionManager.loadSession(currentSessionId);\n                if (currentSession && currentSession.alarmSettings) {\n                    alarmSettings = currentSession.alarmSettings;\n                }\n            }\n            if (alarmSettings.soundAlertsEnabled && audioRef.current) {\n                let soundPath;\n                if (soundKey === 'soundOrderExecution' && alarmSettings.alertOnOrderExecution) {\n                    soundPath = alarmSettings.soundOrderExecution;\n                } else if (soundKey === 'soundError' && alarmSettings.alertOnError) {\n                    soundPath = alarmSettings.soundError;\n                }\n                if (soundPath) {\n                    try {\n                        // Stop any currently playing audio first\n                        audioRef.current.pause();\n                        audioRef.current.currentTime = 0;\n                        // Set the source and load the audio\n                        audioRef.current.src = soundPath;\n                        audioRef.current.load(); // Explicitly load the audio\n                        // Add error handler for audio loading\n                        audioRef.current.onerror = ({\n                            \"TradingProvider.useCallback[playSound]\": (e)=>{\n                                console.warn(\"⚠️ Audio loading failed for \".concat(soundPath, \":\"), e);\n                                // Try fallback sound\n                                if (soundPath !== '/sounds/chime2.wav' && audioRef.current) {\n                                    audioRef.current.src = '/sounds/chime2.wav';\n                                    audioRef.current.load();\n                                }\n                            }\n                        })[\"TradingProvider.useCallback[playSound]\"];\n                        // Play the sound and limit duration to 2 seconds\n                        const playPromise = audioRef.current.play();\n                        if (playPromise !== undefined) {\n                            playPromise.then({\n                                \"TradingProvider.useCallback[playSound]\": ()=>{\n                                    // Set a timeout to pause the audio after 2 seconds\n                                    setTimeout({\n                                        \"TradingProvider.useCallback[playSound]\": ()=>{\n                                            if (audioRef.current && !audioRef.current.paused) {\n                                                audioRef.current.pause();\n                                                audioRef.current.currentTime = 0; // Reset for next play\n                                            }\n                                        }\n                                    }[\"TradingProvider.useCallback[playSound]\"], 2000); // 2 seconds\n                                }\n                            }[\"TradingProvider.useCallback[playSound]\"]).catch({\n                                \"TradingProvider.useCallback[playSound]\": (err)=>{\n                                    // Handle different types of audio errors gracefully\n                                    const errorMessage = err.message || String(err);\n                                    if (errorMessage.includes('user didn\\'t interact') || errorMessage.includes('autoplay')) {\n                                        console.log('🔇 Audio autoplay blocked by browser - user interaction required');\n                                    // Could show a notification to user about enabling audio\n                                    } else if (errorMessage.includes('interrupted') || errorMessage.includes('supported source')) {\n                                    // These are expected errors, don't log them\n                                    } else {\n                                        console.warn(\"⚠️ Audio playback failed:\", errorMessage);\n                                    }\n                                }\n                            }[\"TradingProvider.useCallback[playSound]\"]);\n                        }\n                    } catch (error) {\n                        console.warn(\"⚠️ Audio setup failed for \".concat(soundPath, \":\"), error);\n                    }\n                }\n            }\n        }\n    }[\"TradingProvider.useCallback[playSound]\"], [\n        state.appSettings\n    ]);\n    // Enhanced Telegram notification function with error handling\n    const sendTelegramNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[sendTelegramNotification]\": async function(message) {\n            let isError = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n            try {\n                const telegramToken = localStorage.getItem('telegram_bot_token');\n                const telegramChatId = localStorage.getItem('telegram_chat_id');\n                if (!telegramToken || !telegramChatId) {\n                    console.log('Telegram not configured - skipping notification');\n                    return;\n                }\n                const response = await fetch(\"https://api.telegram.org/bot\".concat(telegramToken, \"/sendMessage\"), {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        chat_id: telegramChatId,\n                        text: message,\n                        parse_mode: 'HTML'\n                    })\n                });\n                if (!response.ok) {\n                    console.error('Failed to send Telegram notification:', response.statusText);\n                    // Don't retry error notifications to avoid infinite loops\n                    if (!isError) {\n                        await sendTelegramErrorNotification('Telegram API Error', \"Failed to send notification: \".concat(response.statusText));\n                    }\n                } else {\n                    console.log('✅ Telegram notification sent successfully');\n                }\n            } catch (error) {\n                console.error('Error sending Telegram notification:', error);\n                // Network disconnection detected\n                if (!isError) {\n                    await sendTelegramErrorNotification('Network Disconnection', \"Failed to connect to Telegram API: \".concat(error));\n                }\n            }\n        }\n    }[\"TradingProvider.useCallback[sendTelegramNotification]\"], []);\n    // Specific error notification functions\n    const sendTelegramErrorNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[sendTelegramErrorNotification]\": async (errorType, errorMessage)=>{\n            const timestamp = new Date().toLocaleString();\n            const message = \"⚠️ <b>ERROR ALERT</b>\\n\\n\" + \"\\uD83D\\uDD34 <b>Type:</b> \".concat(errorType, \"\\n\") + \"\\uD83D\\uDCDD <b>Message:</b> \".concat(errorMessage, \"\\n\") + \"⏰ <b>Time:</b> \".concat(timestamp, \"\\n\") + \"\\uD83E\\uDD16 <b>Bot:</b> \".concat(state.config.crypto1, \"/\").concat(state.config.crypto2, \" \").concat(state.config.tradingMode);\n            await sendTelegramNotification(message, true); // Mark as error to prevent recursion\n        }\n    }[\"TradingProvider.useCallback[sendTelegramErrorNotification]\"], [\n        state.config,\n        sendTelegramNotification\n    ]);\n    const sendLowBalanceNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[sendLowBalanceNotification]\": async (crypto, currentBalance, requiredAmount)=>{\n            const timestamp = new Date().toLocaleString();\n            const message = \"\\uD83D\\uDCB0 <b>LOW BALANCE ALERT</b>\\n\\n\" + \"\\uD83E\\uDE99 <b>Currency:</b> \".concat(crypto, \"\\n\") + \"\\uD83D\\uDCCA <b>Current Balance:</b> \".concat(currentBalance.toFixed(6), \" \").concat(crypto, \"\\n\") + \"⚡ <b>Required Amount:</b> \".concat(requiredAmount.toFixed(6), \" \").concat(crypto, \"\\n\") + \"\\uD83D\\uDCC9 <b>Shortage:</b> \".concat((requiredAmount - currentBalance).toFixed(6), \" \").concat(crypto, \"\\n\") + \"⏰ <b>Time:</b> \".concat(timestamp, \"\\n\") + \"\\uD83E\\uDD16 <b>Bot:</b> \".concat(state.config.crypto1, \"/\").concat(state.config.crypto2, \" \").concat(state.config.tradingMode);\n            await sendTelegramNotification(message);\n        }\n    }[\"TradingProvider.useCallback[sendLowBalanceNotification]\"], [\n        state.config,\n        sendTelegramNotification\n    ]);\n    const sendAPIErrorNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[sendAPIErrorNotification]\": async (apiName, errorDetails)=>{\n            const timestamp = new Date().toLocaleString();\n            const message = \"\\uD83D\\uDD0C <b>API ERROR ALERT</b>\\n\\n\" + \"\\uD83C\\uDF10 <b>API:</b> \".concat(apiName, \"\\n\") + \"❌ <b>Error:</b> \".concat(errorDetails, \"\\n\") + \"⏰ <b>Time:</b> \".concat(timestamp, \"\\n\") + \"\\uD83E\\uDD16 <b>Bot:</b> \".concat(state.config.crypto1, \"/\").concat(state.config.crypto2, \" \").concat(state.config.tradingMode);\n            await sendTelegramNotification(message);\n        }\n    }[\"TradingProvider.useCallback[sendAPIErrorNotification]\"], [\n        state.config,\n        sendTelegramNotification\n    ]);\n    // Effect to update market price when trading pair changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n        // When crypto1 or crypto2 (parts of state.config) change,\n        // the fetchMarketPrice useCallback gets a new reference.\n        // The useEffect above (which depends on fetchMarketPrice)\n        // will re-run, clear the old interval, make an initial fetch with the new config,\n        // and set up a new interval.\n        // The reducer for SET_CONFIG also sets an initial market price if crypto1/crypto2 changes.\n        // Thus, no explicit dispatch is needed here.\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.config.crypto1,\n        state.config.crypto2\n    ]); // Dependencies ensure this reacts to pair changes\n    const setTargetPrices = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[setTargetPrices]\": (prices)=>{\n            if (!prices || !Array.isArray(prices)) return;\n            // Sort prices from lowest to highest for proper counter assignment\n            const sortedPrices = [\n                ...prices\n            ].filter({\n                \"TradingProvider.useCallback[setTargetPrices].sortedPrices\": (price)=>!isNaN(price) && price > 0\n            }[\"TradingProvider.useCallback[setTargetPrices].sortedPrices\"]).sort({\n                \"TradingProvider.useCallback[setTargetPrices].sortedPrices\": (a, b)=>a - b\n            }[\"TradingProvider.useCallback[setTargetPrices].sortedPrices\"]);\n            const newRows = sortedPrices.map({\n                \"TradingProvider.useCallback[setTargetPrices].newRows\": (price, index)=>{\n                    const existingRow = state.targetPriceRows.find({\n                        \"TradingProvider.useCallback[setTargetPrices].newRows.existingRow\": (r)=>r.targetPrice === price\n                    }[\"TradingProvider.useCallback[setTargetPrices].newRows.existingRow\"]);\n                    if (existingRow) {\n                        // Update counter for existing row based on sorted position\n                        return {\n                            ...existingRow,\n                            counter: index + 1\n                        };\n                    }\n                    return {\n                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                        counter: index + 1,\n                        status: 'Free',\n                        orderLevel: 0,\n                        valueLevel: state.config.baseBid,\n                        targetPrice: price\n                    };\n                }\n            }[\"TradingProvider.useCallback[setTargetPrices].newRows\"]);\n            dispatch({\n                type: 'SET_TARGET_PRICE_ROWS',\n                payload: newRows\n            });\n        }\n    }[\"TradingProvider.useCallback[setTargetPrices]\"], [\n        state.targetPriceRows,\n        state.config.baseBid,\n        dispatch\n    ]);\n    // Core Trading Logic (Simulated) - CONTINUOUS TRADING VERSION\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            // Check network status first\n            const networkMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.NetworkMonitor.getInstance();\n            const isOnline = networkMonitor.getStatus().isOnline;\n            // Only check essential conditions AND network status\n            if (state.botSystemStatus !== 'Running' || state.targetPriceRows.length === 0 || state.currentMarketPrice <= 0 || !isOnline) {\n                if (!isOnline && state.botSystemStatus === 'Running') {\n                    console.log('🔴 Trading paused - network offline');\n                }\n                return;\n            }\n            // Execute trading logic immediately - no locks, no cooldowns, no delays\n            const { config, currentMarketPrice, targetPriceRows, crypto1Balance, crypto2Balance } = state;\n            const sortedRowsForLogic = [\n                ...targetPriceRows\n            ].sort({\n                \"TradingProvider.useEffect.sortedRowsForLogic\": (a, b)=>a.targetPrice - b.targetPrice\n            }[\"TradingProvider.useEffect.sortedRowsForLogic\"]);\n            // Use mutable variables for balance tracking within this cycle\n            let currentCrypto1Balance = crypto1Balance;\n            let currentCrypto2Balance = crypto2Balance;\n            let actionsTaken = 0;\n            console.log(\"\\uD83D\\uDE80 CONTINUOUS TRADING: Price $\".concat(currentMarketPrice.toFixed(2), \" | Targets: \").concat(sortedRowsForLogic.length, \" | Balance: $\").concat(currentCrypto2Balance, \" \").concat(config.crypto2));\n            // Show which targets are in range\n            const targetsInRange = sortedRowsForLogic.filter({\n                \"TradingProvider.useEffect.targetsInRange\": (row)=>{\n                    const diffPercent = Math.abs(currentMarketPrice - row.targetPrice) / currentMarketPrice * 100;\n                    return diffPercent <= config.slippagePercent;\n                }\n            }[\"TradingProvider.useEffect.targetsInRange\"]);\n            if (targetsInRange.length > 0) {\n                console.log(\"\\uD83C\\uDFAF TARGETS IN RANGE (\\xb1\".concat(config.slippagePercent, \"%):\"), targetsInRange.map({\n                    \"TradingProvider.useEffect\": (row)=>\"Counter \".concat(row.counter, \" (\").concat(row.status, \")\")\n                }[\"TradingProvider.useEffect\"]));\n            }\n            // CONTINUOUS TRADING LOGIC: Process all targets immediately\n            for(let i = 0; i < sortedRowsForLogic.length; i++){\n                const activeRow = sortedRowsForLogic[i];\n                const priceDiffPercent = Math.abs(currentMarketPrice - activeRow.targetPrice) / currentMarketPrice * 100;\n                // STEP 1: Check if TargetRowN is triggered (within slippage range)\n                if (priceDiffPercent <= config.slippagePercent) {\n                    if (config.tradingMode === \"SimpleSpot\") {\n                        // STEP 2: Evaluate Triggered TargetRowN's Status\n                        if (activeRow.status === \"Free\") {\n                            // STEP 2a: Execute BUY on TargetRowN\n                            const costCrypto2 = activeRow.valueLevel;\n                            if (currentCrypto2Balance >= costCrypto2) {\n                                const amountCrypto1Bought = costCrypto2 / currentMarketPrice;\n                                const updatedRow = {\n                                    ...activeRow,\n                                    status: \"Full\",\n                                    orderLevel: activeRow.orderLevel + 1,\n                                    valueLevel: config.baseBid * Math.pow(config.multiplier, activeRow.orderLevel + 1),\n                                    crypto1AmountHeld: amountCrypto1Bought,\n                                    originalCostCrypto2: costCrypto2,\n                                    crypto1Var: amountCrypto1Bought,\n                                    crypto2Var: -costCrypto2\n                                };\n                                dispatch({\n                                    type: 'UPDATE_TARGET_PRICE_ROW',\n                                    payload: updatedRow\n                                });\n                                dispatch({\n                                    type: 'UPDATE_BALANCES',\n                                    payload: {\n                                        crypto1: currentCrypto1Balance + amountCrypto1Bought,\n                                        crypto2: currentCrypto2Balance - costCrypto2\n                                    }\n                                });\n                                dispatch({\n                                    type: 'ADD_ORDER_HISTORY_ENTRY',\n                                    payload: {\n                                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                        timestamp: Date.now(),\n                                        pair: \"\".concat(config.crypto1, \"/\").concat(config.crypto2),\n                                        crypto1: config.crypto1,\n                                        orderType: \"BUY\",\n                                        amountCrypto1: amountCrypto1Bought,\n                                        avgPrice: currentMarketPrice,\n                                        valueCrypto2: costCrypto2,\n                                        price1: currentMarketPrice,\n                                        crypto1Symbol: config.crypto1 || '',\n                                        crypto2Symbol: config.crypto2 || ''\n                                    }\n                                });\n                                console.log(\"✅ BUY: Counter \".concat(activeRow.counter, \" bought \").concat(amountCrypto1Bought.toFixed(6), \" \").concat(config.crypto1, \" at $\").concat(currentMarketPrice.toFixed(2)));\n                                toast({\n                                    title: \"BUY Executed\",\n                                    description: \"Counter \".concat(activeRow.counter, \": \").concat(amountCrypto1Bought.toFixed(6), \" \").concat(config.crypto1),\n                                    duration: 2000\n                                });\n                                playSound('soundOrderExecution');\n                                // Send Telegram notification for BUY\n                                sendTelegramNotification(\"\\uD83D\\uDFE2 <b>BUY EXECUTED</b>\\n\" + \"\\uD83D\\uDCCA Counter: \".concat(activeRow.counter, \"\\n\") + \"\\uD83D\\uDCB0 Amount: \".concat(amountCrypto1Bought.toFixed(6), \" \").concat(config.crypto1, \"\\n\") + \"\\uD83D\\uDCB5 Price: $\".concat(currentMarketPrice.toFixed(2), \"\\n\") + \"\\uD83D\\uDCB8 Cost: $\".concat(costCrypto2.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\\uD83D\\uDCC8 Mode: Simple Spot\");\n                                actionsTaken++;\n                                // Update local balance for subsequent checks in this cycle\n                                currentCrypto2Balance -= costCrypto2;\n                                currentCrypto1Balance += amountCrypto1Bought;\n                            } else {\n                                // Insufficient balance - send Telegram notification\n                                console.log(\"❌ Insufficient \".concat(config.crypto2, \" balance: \").concat(currentCrypto2Balance.toFixed(6), \" < \").concat(costCrypto2.toFixed(6)));\n                                sendLowBalanceNotification(config.crypto2, currentCrypto2Balance, costCrypto2).catch(console.error);\n                            }\n                        }\n                        // STEP 3: Check and Process Inferior TargetRow (TargetRowN_minus_1) - ALWAYS, regardless of BUY\n                        const currentCounter = activeRow.counter;\n                        const inferiorRow = sortedRowsForLogic.find({\n                            \"TradingProvider.useEffect.inferiorRow\": (row)=>row.counter === currentCounter - 1\n                        }[\"TradingProvider.useEffect.inferiorRow\"]);\n                        if (inferiorRow && inferiorRow.status === \"Full\" && inferiorRow.crypto1AmountHeld && inferiorRow.originalCostCrypto2) {\n                            const amountCrypto1ToSell = inferiorRow.crypto1AmountHeld;\n                            const crypto2Received = amountCrypto1ToSell * currentMarketPrice;\n                            const realizedProfit = crypto2Received - inferiorRow.originalCostCrypto2;\n                            // Calculate Crypto1 profit/loss based on income split percentage\n                            const realizedProfitCrypto1 = currentMarketPrice > 0 ? realizedProfit * config.incomeSplitCrypto1Percent / 100 / currentMarketPrice : 0;\n                            const updatedInferiorRow = {\n                                ...inferiorRow,\n                                status: \"Free\",\n                                crypto1AmountHeld: undefined,\n                                originalCostCrypto2: undefined,\n                                valueLevel: config.baseBid * Math.pow(config.multiplier, inferiorRow.orderLevel),\n                                crypto1Var: -amountCrypto1ToSell,\n                                crypto2Var: crypto2Received\n                            };\n                            dispatch({\n                                type: 'UPDATE_TARGET_PRICE_ROW',\n                                payload: updatedInferiorRow\n                            });\n                            dispatch({\n                                type: 'UPDATE_BALANCES',\n                                payload: {\n                                    crypto1: currentCrypto1Balance - amountCrypto1ToSell,\n                                    crypto2: currentCrypto2Balance + crypto2Received\n                                }\n                            });\n                            dispatch({\n                                type: 'ADD_ORDER_HISTORY_ENTRY',\n                                payload: {\n                                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                    timestamp: Date.now(),\n                                    pair: \"\".concat(config.crypto1, \"/\").concat(config.crypto2),\n                                    crypto1: config.crypto1,\n                                    orderType: \"SELL\",\n                                    amountCrypto1: amountCrypto1ToSell,\n                                    avgPrice: currentMarketPrice,\n                                    valueCrypto2: crypto2Received,\n                                    price1: currentMarketPrice,\n                                    crypto1Symbol: config.crypto1 || '',\n                                    crypto2Symbol: config.crypto2 || '',\n                                    realizedProfitLossCrypto2: realizedProfit,\n                                    realizedProfitLossCrypto1: realizedProfitCrypto1\n                                }\n                            });\n                            console.log(\"✅ SELL: Counter \".concat(currentCounter - 1, \" sold \").concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \". Profit: $\").concat(realizedProfit.toFixed(2)));\n                            toast({\n                                title: \"SELL Executed\",\n                                description: \"Counter \".concat(currentCounter - 1, \": Profit $\").concat(realizedProfit.toFixed(2)),\n                                duration: 2000\n                            });\n                            playSound('soundOrderExecution');\n                            // Send Telegram notification for SELL\n                            const profitEmoji = realizedProfit > 0 ? '📈' : realizedProfit < 0 ? '📉' : '➖';\n                            sendTelegramNotification(\"\\uD83D\\uDD34 <b>SELL EXECUTED</b>\\n\" + \"\\uD83D\\uDCCA Counter: \".concat(currentCounter - 1, \"\\n\") + \"\\uD83D\\uDCB0 Amount: \".concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \"\\n\") + \"\\uD83D\\uDCB5 Price: $\".concat(currentMarketPrice.toFixed(2), \"\\n\") + \"\\uD83D\\uDCB8 Received: $\".concat(crypto2Received.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\".concat(profitEmoji, \" Profit: $\").concat(realizedProfit.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\\uD83D\\uDCC8 Mode: Simple Spot\");\n                            actionsTaken++;\n                            // Update local balance for subsequent checks in this cycle\n                            currentCrypto1Balance -= amountCrypto1ToSell;\n                            currentCrypto2Balance += crypto2Received;\n                        }\n                    } else if (config.tradingMode === \"StablecoinSwap\") {\n                        // STEP 2: Evaluate Triggered TargetRowN's Status (Stablecoin Swap Mode)\n                        if (activeRow.status === \"Free\") {\n                            // STEP 2a: Execute Two-Step \"Buy Crypto1 via Stablecoin\" on TargetRowN\n                            const amountCrypto2ToUse = activeRow.valueLevel; // Value = BaseBid * (Multiplier ^ Level)\n                            if (currentCrypto2Balance >= amountCrypto2ToUse) {\n                                // Step 1: Sell Crypto2 for PreferredStablecoin\n                                // Use current fluctuating market price for more realistic P/L\n                                const crypto2USDPrice = getUSDPrice(config.crypto2 || 'USDT');\n                                const stablecoinUSDPrice = getUSDPrice(config.preferredStablecoin || 'USDT');\n                                const crypto2StablecoinPrice = crypto2USDPrice / stablecoinUSDPrice;\n                                const stablecoinObtained = amountCrypto2ToUse * crypto2StablecoinPrice;\n                                // Step 2: Buy Crypto1 with Stablecoin\n                                // Use current fluctuating market price (this is the key for P/L calculation)\n                                const crypto1USDPrice = getUSDPrice(config.crypto1 || 'BTC');\n                                // Apply current market price fluctuation to Crypto1 price\n                                const fluctuatedCrypto1Price = crypto1USDPrice * (state.currentMarketPrice / (crypto1USDPrice / crypto2USDPrice));\n                                const crypto1StablecoinPrice = fluctuatedCrypto1Price / stablecoinUSDPrice;\n                                const crypto1Bought = stablecoinObtained / crypto1StablecoinPrice;\n                                // Update Row N: Free → Full, Level++, Value recalculated\n                                const newLevel = activeRow.orderLevel + 1;\n                                const newValue = config.baseBid * Math.pow(config.multiplier, newLevel);\n                                const updatedRow = {\n                                    ...activeRow,\n                                    status: \"Full\",\n                                    orderLevel: newLevel,\n                                    valueLevel: newValue,\n                                    crypto1AmountHeld: crypto1Bought,\n                                    originalCostCrypto2: amountCrypto2ToUse,\n                                    crypto1Var: crypto1Bought,\n                                    crypto2Var: -amountCrypto2ToUse\n                                };\n                                console.log(\"\\uD83D\\uDCB0 StablecoinSwap BUY - Setting originalCostCrypto2:\", {\n                                    counter: activeRow.counter,\n                                    amountCrypto2ToUse: amountCrypto2ToUse,\n                                    crypto1Bought: crypto1Bought,\n                                    originalCostCrypto2: amountCrypto2ToUse,\n                                    newLevel: newLevel,\n                                    crypto1StablecoinPrice: crypto1StablecoinPrice,\n                                    fluctuatedCrypto1Price: fluctuatedCrypto1Price,\n                                    currentMarketPrice: state.currentMarketPrice\n                                });\n                                dispatch({\n                                    type: 'UPDATE_TARGET_PRICE_ROW',\n                                    payload: updatedRow\n                                });\n                                dispatch({\n                                    type: 'UPDATE_BALANCES',\n                                    payload: {\n                                        crypto1: currentCrypto1Balance + crypto1Bought,\n                                        crypto2: currentCrypto2Balance - amountCrypto2ToUse\n                                    }\n                                });\n                                // Add history entries for both steps of the stablecoin swap\n                                // Calculate P/L for the crypto2 sell based on price fluctuation\n                                const baseCrypto2Price = getUSDPrice(config.crypto2) / getUSDPrice(config.preferredStablecoin);\n                                const estimatedProfitCrypto2 = amountCrypto2ToUse * (crypto2StablecoinPrice - baseCrypto2Price);\n                                const estimatedProfitCrypto1 = estimatedProfitCrypto2 / crypto1StablecoinPrice;\n                                console.log(\"\\uD83D\\uDCCA StablecoinSwap Step A SELL P/L:\", {\n                                    crypto2: config.crypto2,\n                                    amountSold: amountCrypto2ToUse,\n                                    currentPrice: crypto2StablecoinPrice,\n                                    basePrice: baseCrypto2Price,\n                                    estimatedProfitCrypto2: estimatedProfitCrypto2,\n                                    estimatedProfitCrypto1: estimatedProfitCrypto1\n                                });\n                                dispatch({\n                                    type: 'ADD_ORDER_HISTORY_ENTRY',\n                                    payload: {\n                                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                        timestamp: Date.now(),\n                                        pair: \"\".concat(config.crypto2, \"/\").concat(config.preferredStablecoin),\n                                        crypto1: config.crypto2,\n                                        orderType: \"SELL\",\n                                        amountCrypto1: amountCrypto2ToUse,\n                                        avgPrice: crypto2StablecoinPrice,\n                                        valueCrypto2: stablecoinObtained,\n                                        price1: crypto2StablecoinPrice,\n                                        crypto1Symbol: config.crypto2 || '',\n                                        crypto2Symbol: config.preferredStablecoin || ''\n                                    }\n                                });\n                                dispatch({\n                                    type: 'ADD_ORDER_HISTORY_ENTRY',\n                                    payload: {\n                                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                        timestamp: Date.now(),\n                                        pair: \"\".concat(config.crypto1, \"/\").concat(config.preferredStablecoin),\n                                        crypto1: config.crypto1,\n                                        orderType: \"BUY\",\n                                        amountCrypto1: crypto1Bought,\n                                        avgPrice: crypto1StablecoinPrice,\n                                        valueCrypto2: stablecoinObtained,\n                                        price1: crypto1StablecoinPrice,\n                                        crypto1Symbol: config.crypto1 || '',\n                                        crypto2Symbol: config.preferredStablecoin || ''\n                                    }\n                                });\n                                console.log(\"✅ STABLECOIN BUY: Counter \".concat(activeRow.counter, \" | Step 1: Sold \").concat(amountCrypto2ToUse, \" \").concat(config.crypto2, \" → \").concat(stablecoinObtained.toFixed(2), \" \").concat(config.preferredStablecoin, \" | Step 2: Bought \").concat(crypto1Bought.toFixed(6), \" \").concat(config.crypto1, \" | Level: \").concat(activeRow.orderLevel, \" → \").concat(newLevel));\n                                toast({\n                                    title: \"BUY Executed (Stablecoin)\",\n                                    description: \"Counter \".concat(activeRow.counter, \": \").concat(crypto1Bought.toFixed(6), \" \").concat(config.crypto1, \" via \").concat(config.preferredStablecoin),\n                                    duration: 2000\n                                });\n                                playSound('soundOrderExecution');\n                                // Send Telegram notification for Stablecoin BUY\n                                sendTelegramNotification(\"\\uD83D\\uDFE2 <b>BUY EXECUTED (Stablecoin Swap)</b>\\n\" + \"\\uD83D\\uDCCA Counter: \".concat(activeRow.counter, \"\\n\") + \"\\uD83D\\uDD04 Step 1: Sold \".concat(amountCrypto2ToUse.toFixed(2), \" \").concat(config.crypto2, \" → \").concat(stablecoinObtained.toFixed(2), \" \").concat(config.preferredStablecoin, \"\\n\") + \"\\uD83D\\uDD04 Step 2: Bought \".concat(crypto1Bought.toFixed(6), \" \").concat(config.crypto1, \"\\n\") + \"\\uD83D\\uDCCA Level: \".concat(activeRow.orderLevel, \" → \").concat(newLevel, \"\\n\") + \"\\uD83D\\uDCC8 Mode: Stablecoin Swap\");\n                                actionsTaken++;\n                                // Update local balance for subsequent checks in this cycle\n                                currentCrypto2Balance -= amountCrypto2ToUse;\n                                currentCrypto1Balance += crypto1Bought;\n                            } else {\n                                // Insufficient balance - send Telegram notification\n                                console.log(\"❌ Insufficient \".concat(config.crypto2, \" balance for stablecoin swap: \").concat(currentCrypto2Balance.toFixed(6), \" < \").concat(amountCrypto2ToUse.toFixed(6)));\n                                sendLowBalanceNotification(config.crypto2, currentCrypto2Balance, amountCrypto2ToUse).catch(console.error);\n                            }\n                        }\n                        // STEP 3: Check and Process Inferior TargetRow (TargetRowN_minus_1) - ALWAYS, regardless of BUY\n                        const currentCounter = activeRow.counter;\n                        const inferiorRow = sortedRowsForLogic.find({\n                            \"TradingProvider.useEffect.inferiorRow\": (row)=>row.counter === currentCounter - 1\n                        }[\"TradingProvider.useEffect.inferiorRow\"]);\n                        console.log(\"\\uD83D\\uDD0D StablecoinSwap SELL Check:\", {\n                            currentCounter: currentCounter,\n                            inferiorRowFound: !!inferiorRow,\n                            inferiorRowStatus: inferiorRow === null || inferiorRow === void 0 ? void 0 : inferiorRow.status,\n                            inferiorRowCrypto1Held: inferiorRow === null || inferiorRow === void 0 ? void 0 : inferiorRow.crypto1AmountHeld,\n                            inferiorRowOriginalCost: inferiorRow === null || inferiorRow === void 0 ? void 0 : inferiorRow.originalCostCrypto2,\n                            canExecuteSell: !!(inferiorRow && inferiorRow.status === \"Full\" && inferiorRow.crypto1AmountHeld && inferiorRow.originalCostCrypto2)\n                        });\n                        if (inferiorRow && inferiorRow.status === \"Full\" && inferiorRow.crypto1AmountHeld && inferiorRow.originalCostCrypto2) {\n                            // Execute Two-Step \"Sell Crypto1 & Reacquire Crypto2 via Stablecoin\" for TargetRowN_minus_1\n                            const amountCrypto1ToSell = inferiorRow.crypto1AmountHeld;\n                            // Step A: Sell Crypto1 for PreferredStablecoin\n                            // Use current fluctuating market price for realistic P/L\n                            const crypto1USDPrice = getUSDPrice(config.crypto1 || 'BTC');\n                            const crypto2USDPrice = getUSDPrice(config.crypto2 || 'USDT');\n                            const stablecoinUSDPrice = getUSDPrice(config.preferredStablecoin || 'USDT');\n                            // Apply current market price fluctuation to Crypto1 price\n                            const fluctuatedCrypto1Price = crypto1USDPrice * (state.currentMarketPrice / (crypto1USDPrice / crypto2USDPrice));\n                            const crypto1StablecoinPrice = fluctuatedCrypto1Price / stablecoinUSDPrice;\n                            const stablecoinFromC1Sell = amountCrypto1ToSell * crypto1StablecoinPrice;\n                            // Step B: Buy Crypto2 with Stablecoin\n                            const crypto2StablecoinPrice = crypto2USDPrice / stablecoinUSDPrice;\n                            const crypto2Reacquired = stablecoinFromC1Sell / crypto2StablecoinPrice;\n                            // Calculate realized profit in Crypto2\n                            // We compare the value of crypto2 we got back vs what we originally spent\n                            const originalCost = inferiorRow.originalCostCrypto2 || 0;\n                            const realizedProfitInCrypto2 = crypto2Reacquired - originalCost;\n                            // Calculate Crypto1 profit/loss - this should be the profit in terms of Crypto1\n                            // For StablecoinSwap, we need to calculate how much Crypto1 profit this represents\n                            const realizedProfitCrypto1 = crypto1StablecoinPrice > 0 ? realizedProfitInCrypto2 / crypto1StablecoinPrice : 0;\n                            // IMPORTANT: If P/L is exactly 0, it might indicate a price calculation issue\n                            if (realizedProfitInCrypto2 === 0) {\n                                console.warn(\"⚠️ P/L is exactly 0 - this might indicate an issue:\\n                - Are prices changing between BUY and SELL?\\n                - Is the market price fluctuation working?\\n                - Current market price: \".concat(state.currentMarketPrice));\n                            }\n                            console.log(\"\\uD83D\\uDCB0 StablecoinSwap P/L Calculation DETAILED:\\n              - Inferior Row Counter: \".concat(inferiorRow.counter, \"\\n              - Original Cost (Crypto2): \").concat(originalCost, \"\\n              - Crypto2 Reacquired: \").concat(crypto2Reacquired, \"\\n              - Realized P/L (Crypto2): \").concat(realizedProfitInCrypto2, \"\\n              - Crypto1 Stablecoin Price: \").concat(crypto1StablecoinPrice, \"\\n              - Realized P/L (Crypto1): \").concat(realizedProfitCrypto1, \"\\n              - Is Profitable: \").concat(realizedProfitInCrypto2 > 0 ? 'YES' : 'NO', \"\\n              - Calculation: \").concat(crypto2Reacquired, \" - \").concat(originalCost, \" = \").concat(realizedProfitInCrypto2));\n                            // Update Row N-1: Full → Free, Level UNCHANGED, Vars cleared\n                            const updatedInferiorRow = {\n                                ...inferiorRow,\n                                status: \"Free\",\n                                // orderLevel: REMAINS UNCHANGED per specification\n                                crypto1AmountHeld: undefined,\n                                originalCostCrypto2: undefined,\n                                valueLevel: config.baseBid * Math.pow(config.multiplier, inferiorRow.orderLevel),\n                                crypto1Var: 0,\n                                crypto2Var: 0\n                            };\n                            dispatch({\n                                type: 'UPDATE_TARGET_PRICE_ROW',\n                                payload: updatedInferiorRow\n                            });\n                            dispatch({\n                                type: 'UPDATE_BALANCES',\n                                payload: {\n                                    crypto1: currentCrypto1Balance - amountCrypto1ToSell,\n                                    crypto2: currentCrypto2Balance + crypto2Reacquired\n                                }\n                            });\n                            // Add history entries for both steps of the N-1 stablecoin swap\n                            // Step A: SELL Crypto1 for Stablecoin (with profit/loss data for analytics)\n                            console.log(\"\\uD83D\\uDCCA Adding StablecoinSwap SELL order to history with P/L:\", {\n                                realizedProfitLossCrypto2: realizedProfitInCrypto2,\n                                realizedProfitLossCrypto1: realizedProfitCrypto1,\n                                pair: \"\".concat(config.crypto1, \"/\").concat(config.preferredStablecoin),\n                                orderType: \"SELL\",\n                                amountCrypto1: amountCrypto1ToSell,\n                                avgPrice: crypto1StablecoinPrice,\n                                valueCrypto2: stablecoinFromC1Sell,\n                                price1: crypto1StablecoinPrice,\n                                crypto1Symbol: config.crypto1,\n                                crypto2Symbol: config.preferredStablecoin\n                            });\n                            dispatch({\n                                type: 'ADD_ORDER_HISTORY_ENTRY',\n                                payload: {\n                                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                    timestamp: Date.now(),\n                                    pair: \"\".concat(config.crypto1, \"/\").concat(config.preferredStablecoin),\n                                    crypto1: config.crypto1,\n                                    orderType: \"SELL\",\n                                    amountCrypto1: amountCrypto1ToSell,\n                                    avgPrice: crypto1StablecoinPrice,\n                                    valueCrypto2: stablecoinFromC1Sell,\n                                    price1: crypto1StablecoinPrice,\n                                    crypto1Symbol: config.crypto1 || '',\n                                    crypto2Symbol: config.preferredStablecoin || ''\n                                }\n                            });\n                            // Step B: BUY Crypto2 with Stablecoin (no profit/loss as it's just conversion)\n                            dispatch({\n                                type: 'ADD_ORDER_HISTORY_ENTRY',\n                                payload: {\n                                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                    timestamp: Date.now(),\n                                    pair: \"\".concat(config.crypto2, \"/\").concat(config.preferredStablecoin),\n                                    crypto1: config.crypto2,\n                                    orderType: \"BUY\",\n                                    amountCrypto1: crypto2Reacquired,\n                                    avgPrice: crypto2StablecoinPrice,\n                                    valueCrypto2: stablecoinFromC1Sell,\n                                    price1: crypto2StablecoinPrice,\n                                    crypto1Symbol: config.crypto2 || '',\n                                    crypto2Symbol: config.preferredStablecoin || ''\n                                }\n                            });\n                            console.log(\"✅ STABLECOIN SELL: Counter \".concat(currentCounter - 1, \" | Step A: Sold \").concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \" → \").concat(stablecoinFromC1Sell.toFixed(2), \" \").concat(config.preferredStablecoin, \" | Step B: Bought \").concat(crypto2Reacquired.toFixed(2), \" \").concat(config.crypto2, \" | Profit: \").concat(realizedProfitInCrypto2.toFixed(2), \" \").concat(config.crypto2, \" | Level: \").concat(inferiorRow.orderLevel, \" (unchanged)\"));\n                            toast({\n                                title: \"SELL Executed (Stablecoin)\",\n                                description: \"Counter \".concat(currentCounter - 1, \": Profit \").concat(realizedProfitInCrypto2.toFixed(2), \" \").concat(config.crypto2, \" via \").concat(config.preferredStablecoin),\n                                duration: 2000\n                            });\n                            playSound('soundOrderExecution');\n                            // Send Telegram notification for Stablecoin SELL\n                            const profitEmoji = realizedProfitInCrypto2 > 0 ? '📈' : realizedProfitInCrypto2 < 0 ? '📉' : '➖';\n                            sendTelegramNotification(\"\\uD83D\\uDD34 <b>SELL EXECUTED (Stablecoin Swap)</b>\\n\" + \"\\uD83D\\uDCCA Counter: \".concat(currentCounter - 1, \"\\n\") + \"\\uD83D\\uDD04 Step A: Sold \".concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \" → \").concat(stablecoinFromC1Sell.toFixed(2), \" \").concat(config.preferredStablecoin, \"\\n\") + \"\\uD83D\\uDD04 Step B: Bought \".concat(crypto2Reacquired.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\".concat(profitEmoji, \" Profit: \").concat(realizedProfitInCrypto2.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\\uD83D\\uDCCA Level: \".concat(inferiorRow.orderLevel, \" (unchanged)\\n\") + \"\\uD83D\\uDCC8 Mode: Stablecoin Swap\");\n                            actionsTaken++;\n                            // Update local balance for subsequent checks in this cycle\n                            currentCrypto1Balance -= amountCrypto1ToSell;\n                            currentCrypto2Balance += crypto2Reacquired;\n                        }\n                    }\n                }\n            }\n            if (actionsTaken > 0) {\n                console.log(\"\\uD83C\\uDFAF CYCLE COMPLETE: \".concat(actionsTaken, \" actions taken at price $\").concat(currentMarketPrice.toFixed(2)));\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus,\n        state.currentMarketPrice,\n        state.targetPriceRows,\n        state.config,\n        state.crypto1Balance,\n        state.crypto2Balance,\n        state.stablecoinBalance,\n        dispatch,\n        toast,\n        playSound,\n        sendTelegramNotification\n    ]);\n    const getDisplayOrders = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[getDisplayOrders]\": ()=>{\n            if (!state.targetPriceRows || !Array.isArray(state.targetPriceRows)) {\n                return [];\n            }\n            return state.targetPriceRows.map({\n                \"TradingProvider.useCallback[getDisplayOrders]\": (row)=>{\n                    const currentPrice = state.currentMarketPrice || 0;\n                    const targetPrice = row.targetPrice || 0;\n                    const percentFromActualPrice = currentPrice && targetPrice ? (currentPrice / targetPrice - 1) * 100 : 0;\n                    let incomeCrypto1;\n                    let incomeCrypto2;\n                    if (row.status === \"Full\" && row.crypto1AmountHeld && row.originalCostCrypto2) {\n                        const totalUnrealizedProfitInCrypto2 = currentPrice * row.crypto1AmountHeld - row.originalCostCrypto2;\n                        incomeCrypto2 = totalUnrealizedProfitInCrypto2 * state.config.incomeSplitCrypto2Percent / 100;\n                        if (currentPrice > 0) {\n                            incomeCrypto1 = totalUnrealizedProfitInCrypto2 * state.config.incomeSplitCrypto1Percent / 100 / currentPrice;\n                        }\n                    }\n                    return {\n                        ...row,\n                        currentPrice,\n                        priceDifference: targetPrice - currentPrice,\n                        priceDifferencePercent: currentPrice > 0 ? (targetPrice - currentPrice) / currentPrice * 100 : 0,\n                        potentialProfitCrypto1: state.config.incomeSplitCrypto1Percent / 100 * row.valueLevel / (targetPrice || 1),\n                        potentialProfitCrypto2: state.config.incomeSplitCrypto2Percent / 100 * row.valueLevel,\n                        percentFromActualPrice,\n                        incomeCrypto1,\n                        incomeCrypto2\n                    };\n                }\n            }[\"TradingProvider.useCallback[getDisplayOrders]\"]).sort({\n                \"TradingProvider.useCallback[getDisplayOrders]\": (a, b)=>b.targetPrice - a.targetPrice\n            }[\"TradingProvider.useCallback[getDisplayOrders]\"]);\n        }\n    }[\"TradingProvider.useCallback[getDisplayOrders]\"], [\n        state.targetPriceRows,\n        state.currentMarketPrice,\n        state.config.incomeSplitCrypto1Percent,\n        state.config.incomeSplitCrypto2Percent,\n        state.config.baseBid,\n        state.config.multiplier\n    ]);\n    // Backend Integration Functions\n    const saveConfigToBackend = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[saveConfigToBackend]\": async (config)=>{\n            try {\n                var _response_config;\n                const configData = {\n                    name: \"\".concat(config.crypto1, \"/\").concat(config.crypto2, \" \").concat(config.tradingMode),\n                    tradingMode: config.tradingMode,\n                    crypto1: config.crypto1,\n                    crypto2: config.crypto2,\n                    baseBid: config.baseBid,\n                    multiplier: config.multiplier,\n                    numDigits: config.numDigits,\n                    slippagePercent: config.slippagePercent,\n                    incomeSplitCrypto1Percent: config.incomeSplitCrypto1Percent,\n                    incomeSplitCrypto2Percent: config.incomeSplitCrypto2Percent,\n                    preferredStablecoin: config.preferredStablecoin,\n                    targetPrices: state.targetPriceRows.map({\n                        \"TradingProvider.useCallback[saveConfigToBackend]\": (row)=>row.targetPrice\n                    }[\"TradingProvider.useCallback[saveConfigToBackend]\"])\n                };\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.tradingApi.saveConfig(configData);\n                console.log('✅ Config saved to backend:', response);\n                return ((_response_config = response.config) === null || _response_config === void 0 ? void 0 : _response_config.id) || null;\n            } catch (error) {\n                console.error('❌ Failed to save config to backend:', error);\n                toast({\n                    title: \"Backend Error\",\n                    description: \"Failed to save configuration to backend\",\n                    variant: \"destructive\",\n                    duration: 3000\n                });\n                return null;\n            }\n        }\n    }[\"TradingProvider.useCallback[saveConfigToBackend]\"], [\n        state.targetPriceRows,\n        toast\n    ]);\n    const startBackendBot = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[startBackendBot]\": async (configId)=>{\n            try {\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.tradingApi.startBot(configId);\n                console.log('✅ Bot started on backend:', response);\n                toast({\n                    title: \"Bot Started\",\n                    description: \"Trading bot started successfully on backend\",\n                    duration: 3000\n                });\n                return true;\n            } catch (error) {\n                console.error('❌ Failed to start bot on backend:', error);\n                toast({\n                    title: \"Backend Error\",\n                    description: \"Failed to start bot on backend\",\n                    variant: \"destructive\",\n                    duration: 3000\n                });\n                return false;\n            }\n        }\n    }[\"TradingProvider.useCallback[startBackendBot]\"], [\n        toast\n    ]);\n    const stopBackendBot = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[stopBackendBot]\": async (configId)=>{\n            try {\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.tradingApi.stopBot(configId);\n                console.log('✅ Bot stopped on backend:', response);\n                toast({\n                    title: \"Bot Stopped\",\n                    description: \"Trading bot stopped successfully on backend\",\n                    duration: 3000\n                });\n                return true;\n            } catch (error) {\n                console.error('❌ Failed to stop bot on backend:', error);\n                toast({\n                    title: \"Backend Error\",\n                    description: \"Failed to stop bot on backend\",\n                    variant: \"destructive\",\n                    duration: 3000\n                });\n                return false;\n            }\n        }\n    }[\"TradingProvider.useCallback[stopBackendBot]\"], [\n        toast\n    ]);\n    const checkBackendStatus = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[checkBackendStatus]\": async ()=>{\n            const apiUrl = \"http://localhost:5000\";\n            if (!apiUrl) {\n                console.error('Error: NEXT_PUBLIC_API_URL is not defined. Backend connectivity check cannot be performed.');\n                dispatch({\n                    type: 'SET_BACKEND_STATUS',\n                    payload: 'offline'\n                });\n                return;\n            }\n            try {\n                const healthResponse = await fetch(\"\".concat(apiUrl, \"/health/\"));\n                if (!healthResponse.ok) {\n                    // Log more details if the response was received but not OK\n                    console.error(\"Backend health check failed with status: \".concat(healthResponse.status, \" \").concat(healthResponse.statusText));\n                    const responseText = await healthResponse.text().catch({\n                        \"TradingProvider.useCallback[checkBackendStatus]\": ()=>'Could not read response text.'\n                    }[\"TradingProvider.useCallback[checkBackendStatus]\"]);\n                    console.error('Backend health check response body:', responseText);\n                }\n                dispatch({\n                    type: 'SET_BACKEND_STATUS',\n                    payload: healthResponse.ok ? 'online' : 'offline'\n                });\n            } catch (error) {\n                dispatch({\n                    type: 'SET_BACKEND_STATUS',\n                    payload: 'offline'\n                });\n                console.error('Backend connectivity check failed. Error details:', error);\n                if (error.cause) {\n                    console.error('Fetch error cause:', error.cause);\n                }\n                // It's also useful to log the apiUrl to ensure it's what we expect\n                console.error('Attempted to fetch API URL:', \"\".concat(apiUrl, \"/health/\"));\n            }\n        }\n    }[\"TradingProvider.useCallback[checkBackendStatus]\"], [\n        dispatch\n    ]);\n    // Initialize backend status check (one-time only)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            // Initial check for backend status only\n            checkBackendStatus();\n        }\n    }[\"TradingProvider.useEffect\"], [\n        checkBackendStatus\n    ]);\n    // Save state to localStorage whenever it changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            saveStateToLocalStorage(state);\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state\n    ]);\n    // Effect to handle bot warm-up period (immediate execution)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            if (state.botSystemStatus === 'WarmingUp') {\n                console.log(\"Bot is Warming Up... Immediate execution enabled.\");\n                // Immediate transition to Running state - no delays\n                dispatch({\n                    type: 'SYSTEM_COMPLETE_WARMUP'\n                });\n                console.log(\"Bot is now Running immediately.\");\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus,\n        dispatch\n    ]);\n    // Effect to handle session runtime tracking\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            const currentSessionId = sessionManager.getCurrentSessionId();\n            if (currentSessionId) {\n                if (state.botSystemStatus === 'Running') {\n                    // Bot started running, start runtime tracking\n                    sessionManager.startSessionRuntime(currentSessionId);\n                    console.log('✅ Started runtime tracking for session:', currentSessionId);\n                } else if (state.botSystemStatus === 'Stopped') {\n                    // Bot stopped, stop runtime tracking\n                    sessionManager.stopSessionRuntime(currentSessionId);\n                    console.log('⏹️ Stopped runtime tracking for session:', currentSessionId);\n                }\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus\n    ]);\n    // Auto-create session when bot actually starts running (not during warmup)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            // Check if we need to create a session when bot actually starts running\n            if (state.botSystemStatus === 'Running' && !sessionManager.getCurrentSessionId()) {\n                // Only create if we have valid crypto configuration AND target prices set\n                // This prevents auto-creation for fresh windows\n                if (state.config.crypto1 && state.config.crypto2 && state.targetPriceRows.length > 0) {\n                    sessionManager.createNewSessionWithAutoName(state.config).then({\n                        \"TradingProvider.useEffect\": (sessionId)=>{\n                            sessionManager.setCurrentSession(sessionId);\n                            console.log('✅ Auto-created session when bot started:', sessionId);\n                        }\n                    }[\"TradingProvider.useEffect\"]).catch({\n                        \"TradingProvider.useEffect\": (error)=>{\n                            console.error('❌ Failed to auto-create session:', error);\n                        }\n                    }[\"TradingProvider.useEffect\"]);\n                }\n            }\n            // Handle runtime tracking based on bot status\n            const currentSessionId = sessionManager.getCurrentSessionId();\n            if (currentSessionId) {\n                if (state.botSystemStatus === 'Running') {\n                    // Start runtime tracking when bot becomes active\n                    sessionManager.startSessionRuntime(currentSessionId);\n                    console.log('⏱️ Started runtime tracking for session:', currentSessionId);\n                } else if (state.botSystemStatus === 'Stopped') {\n                    // Stop runtime tracking when bot stops\n                    sessionManager.stopSessionRuntime(currentSessionId);\n                    console.log('⏹️ Stopped runtime tracking for session:', currentSessionId);\n                }\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus,\n        state.config.crypto1,\n        state.config.crypto2\n    ]);\n    // Handle crypto pair changes during active trading\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            const currentSessionId = sessionManager.getCurrentSessionId();\n            if (currentSessionId) {\n                const currentSession = sessionManager.loadSession(currentSessionId);\n                // Check if crypto pair has changed from the current session\n                if (currentSession && (currentSession.config.crypto1 !== state.config.crypto1 || currentSession.config.crypto2 !== state.config.crypto2)) {\n                    console.log('🔄 Crypto pair changed during session, auto-saving and resetting...');\n                    // Auto-save current session if bot was running or has data\n                    if (state.botSystemStatus === 'Running' || state.targetPriceRows.length > 0 || state.orderHistory.length > 0) {\n                        // Stop the bot if it's running\n                        if (state.botSystemStatus === 'Running') {\n                            stopBackendBot(currentSessionId);\n                        }\n                        // Save current session with timestamp\n                        const timestamp = new Date().toLocaleString('en-US', {\n                            month: 'short',\n                            day: 'numeric',\n                            hour: '2-digit',\n                            minute: '2-digit',\n                            hour12: false\n                        });\n                        const savedName = \"\".concat(currentSession.name, \" (AutoSaved \").concat(timestamp, \")\");\n                        sessionManager.createNewSession(savedName, currentSession.config).then({\n                            \"TradingProvider.useEffect\": async (savedSessionId)=>{\n                                await sessionManager.saveSession(savedSessionId, currentSession.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, false);\n                                console.log('💾 AutoSaved session:', savedName);\n                            }\n                        }[\"TradingProvider.useEffect\"]);\n                    }\n                    // Reset for new crypto pair\n                    dispatch({\n                        type: 'RESET_FOR_NEW_CRYPTO'\n                    });\n                    // Only create new session for the new crypto pair if bot was actually running\n                    if (state.config.crypto1 && state.config.crypto2 && state.botSystemStatus === 'Running') {\n                        const currentBalances = {\n                            crypto1: state.crypto1Balance,\n                            crypto2: state.crypto2Balance,\n                            stablecoin: state.stablecoinBalance\n                        };\n                        sessionManager.createNewSessionWithAutoName(state.config, undefined, currentBalances).then({\n                            \"TradingProvider.useEffect\": (newSessionId)=>{\n                                sessionManager.setCurrentSession(newSessionId);\n                                console.log('🆕 Created new session for crypto pair:', state.config.crypto1, '/', state.config.crypto2, 'with balances:', currentBalances);\n                                toast({\n                                    title: \"Crypto Pair Changed\",\n                                    description: \"Previous session AutoSaved. New session created for \".concat(state.config.crypto1, \"/\").concat(state.config.crypto2),\n                                    duration: 5000\n                                });\n                            }\n                        }[\"TradingProvider.useEffect\"]);\n                    } else {\n                        // If bot wasn't running, just clear the current session without creating a new one\n                        sessionManager.clearCurrentSession();\n                        console.log('🔄 Crypto pair changed but bot wasn\\'t running - cleared current session');\n                    }\n                }\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.config.crypto1,\n        state.config.crypto2\n    ]);\n    // Initialize network monitoring and auto-save\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const networkMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.NetworkMonitor.getInstance();\n            const autoSaveManager = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.AutoSaveManager.getInstance();\n            const memoryMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.MemoryMonitor.getInstance();\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            // Set up network status listener\n            const unsubscribeNetwork = networkMonitor.addListener({\n                \"TradingProvider.useEffect.unsubscribeNetwork\": (isOnline, isInitial)=>{\n                    console.log(\"\\uD83C\\uDF10 Network status changed: \".concat(isOnline ? 'Online' : 'Offline'));\n                    // Handle offline state - stop bot and save session\n                    if (!isOnline && !isInitial) {\n                        // Stop the bot if it's running\n                        if (state.botSystemStatus === 'Running') {\n                            console.log('🔴 Internet lost - stopping bot and saving session');\n                            dispatch({\n                                type: 'SYSTEM_STOP_BOT'\n                            });\n                            // Auto-save current session\n                            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n                            const currentSessionId = sessionManager.getCurrentSessionId();\n                            if (currentSessionId) {\n                                const currentSession = sessionManager.loadSession(currentSessionId);\n                                if (currentSession) {\n                                    // Create offline backup session\n                                    const timestamp = new Date().toLocaleString('en-US', {\n                                        month: 'short',\n                                        day: 'numeric',\n                                        hour: '2-digit',\n                                        minute: '2-digit',\n                                        hour12: false\n                                    });\n                                    const offlineName = \"\".concat(currentSession.name, \" (Offline Backup \").concat(timestamp, \")\");\n                                    sessionManager.createNewSession(offlineName, currentSession.config).then({\n                                        \"TradingProvider.useEffect.unsubscribeNetwork\": async (backupSessionId)=>{\n                                            await sessionManager.saveSession(backupSessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, false);\n                                            console.log('💾 Created offline backup session:', offlineName);\n                                        }\n                                    }[\"TradingProvider.useEffect.unsubscribeNetwork\"]);\n                                }\n                            }\n                        }\n                        toast({\n                            title: \"Network Disconnected\",\n                            description: \"Bot stopped and session saved. Trading paused until connection restored.\",\n                            variant: \"destructive\",\n                            duration: 8000\n                        });\n                    } else if (isOnline && !isInitial) {\n                        toast({\n                            title: \"Network Reconnected\",\n                            description: \"Connection restored. You can resume trading.\",\n                            duration: 3000\n                        });\n                    }\n                }\n            }[\"TradingProvider.useEffect.unsubscribeNetwork\"]);\n            // Set up memory monitoring\n            const unsubscribeMemory = memoryMonitor.addListener({\n                \"TradingProvider.useEffect.unsubscribeMemory\": (memory)=>{\n                    const usedMB = memory.usedJSHeapSize / 1024 / 1024;\n                    if (usedMB > 150) {\n                        console.warn(\"\\uD83E\\uDDE0 High memory usage: \".concat(usedMB.toFixed(2), \"MB\"));\n                        toast({\n                            title: \"High Memory Usage\",\n                            description: \"Memory usage is high (\".concat(usedMB.toFixed(0), \"MB). Consider refreshing the page.\"),\n                            variant: \"destructive\",\n                            duration: 5000\n                        });\n                    }\n                }\n            }[\"TradingProvider.useEffect.unsubscribeMemory\"]);\n            // Set up auto-save\n            const saveFunction = {\n                \"TradingProvider.useEffect.saveFunction\": async ()=>{\n                    try {\n                        // Save to session manager if we have a current session\n                        const currentSessionId = sessionManager.getCurrentSessionId();\n                        if (currentSessionId) {\n                            await sessionManager.saveSession(currentSessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, state.botSystemStatus === 'Running');\n                        }\n                        // Also save to localStorage as backup\n                        saveStateToLocalStorage(state);\n                    } catch (error) {\n                        console.error('Auto-save failed:', error);\n                    }\n                }\n            }[\"TradingProvider.useEffect.saveFunction\"];\n            autoSaveManager.enable(saveFunction, 30000); // Auto-save every 30 seconds\n            // Add beforeunload listener to save session on browser close/refresh\n            const handleBeforeUnload = {\n                \"TradingProvider.useEffect.handleBeforeUnload\": (event)=>{\n                    // Save immediately before unload\n                    saveFunction();\n                    // If bot is running, show warning\n                    if (state.botSystemStatus === 'Running') {\n                        const message = 'Trading bot is currently running. Are you sure you want to leave?';\n                        event.returnValue = message;\n                        return message;\n                    }\n                }\n            }[\"TradingProvider.useEffect.handleBeforeUnload\"];\n            window.addEventListener('beforeunload', handleBeforeUnload);\n            // Cleanup function\n            return ({\n                \"TradingProvider.useEffect\": ()=>{\n                    unsubscribeNetwork();\n                    unsubscribeMemory();\n                    autoSaveManager.disable();\n                    window.removeEventListener('beforeunload', handleBeforeUnload);\n                }\n            })[\"TradingProvider.useEffect\"];\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state,\n        toast\n    ]);\n    // Force save when bot status changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const autoSaveManager = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.AutoSaveManager.getInstance();\n            autoSaveManager.saveNow();\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus\n    ]);\n    // Manual save session function\n    const saveCurrentSession = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[saveCurrentSession]\": async ()=>{\n            try {\n                const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n                const currentSessionId = sessionManager.getCurrentSessionId();\n                if (!currentSessionId) {\n                    // No current session, only create one if bot is running or has meaningful data\n                    if (state.config.crypto1 && state.config.crypto2 && (state.botSystemStatus === 'Running' || state.targetPriceRows.length > 0 || state.orderHistory.length > 0)) {\n                        const currentBalances = {\n                            crypto1: state.crypto1Balance,\n                            crypto2: state.crypto2Balance,\n                            stablecoin: state.stablecoinBalance\n                        };\n                        const sessionId = await sessionManager.createNewSessionWithAutoName(state.config, undefined, currentBalances);\n                        sessionManager.setCurrentSession(sessionId);\n                        await sessionManager.saveSession(sessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, state.botSystemStatus === 'Running');\n                        return true;\n                    }\n                    console.log('⚠️ No session to save - bot not running and no meaningful data');\n                    return false;\n                }\n                // Save existing session\n                return await sessionManager.saveSession(currentSessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, state.botSystemStatus === 'Running');\n            } catch (error) {\n                console.error('Failed to save current session:', error);\n                return false;\n            }\n        }\n    }[\"TradingProvider.useCallback[saveCurrentSession]\"], [\n        state\n    ]);\n    // Context value\n    const contextValue = {\n        ...state,\n        dispatch,\n        setTargetPrices,\n        getDisplayOrders,\n        checkBackendStatus,\n        fetchMarketPrice,\n        startBackendBot,\n        stopBackendBot,\n        saveConfigToBackend,\n        saveCurrentSession,\n        backendStatus: state.backendStatus,\n        botSystemStatus: state.botSystemStatus,\n        isBotActive: state.botSystemStatus === 'Running'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TradingContext.Provider, {\n        value: contextValue,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\contexts\\\\TradingContext.tsx\",\n        lineNumber: 1951,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TradingProvider, \"Dul/342NNfDBwKH7syILMVemn7Y=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__.useToast\n    ];\n});\n_c = TradingProvider;\n// Custom hook to use the trading context\nconst useTradingContext = ()=>{\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(TradingContext);\n    if (context === undefined) {\n        throw new Error('useTradingContext must be used within a TradingProvider');\n    }\n    return context;\n};\n_s1(useTradingContext, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"TradingProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/TradingContext.tsx\n"));

/***/ })

});