"use client";

import React from 'react';
import { useTradingContext } from '@/contexts/TradingContext';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area';
import { format } from 'date-fns';
import type { OrderHistoryEntry } from '@/lib/types';
import { cn } from '@/lib/utils';

export default function HistoryTable() {
  const { orderHistory, config } = useTradingContext();

  const formatNum = (num?: number) => {
    if (num === null || num === undefined || isNaN(num)) return '-';
    try {
      return num.toFixed(config?.numDigits || 2);
    } catch (error) {
      console.warn('Error formatting number in HistoryTable:', error, 'Number:', num);
      return '-';
    }
  };



  const columns = [
    { key: "date", label: "Date" },
    { key: "hour", label: "Hour" },
    { key: "pair", label: "Couple" },
    { key: "crypto", label: `Crypto (${config.crypto1})` },
    { key: "orderType", label: "Order Type" },
    { key: "amount", label: "Amount" },
    { key: "avgPrice", label: "Avg Price" },
    { key: "value", label: `Value (${config.crypto2})` },
    { key: "price1", label: "Price 1" },
    { key: "crypto1Symbol", label: `Crypto (${config.crypto1})` },
    { key: "price2", label: "Price 2" },
    { key: "crypto2Symbol", label: `Crypto (${config.crypto2})` },
  ];

  return (
    <div className="border-2 border-border rounded-sm">
      <ScrollArea className="w-full whitespace-nowrap">
        <Table className="min-w-full">
          <TableHeader>
            <TableRow className="bg-card hover:bg-card">
              {columns.map((col) => (
                <TableHead key={col.key} className="font-bold text-foreground whitespace-nowrap px-3 py-2 text-sm">{col.label}</TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            {orderHistory.length === 0 ? (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center text-muted-foreground">
                  No trading history yet.
                </TableCell>
              </TableRow>
            ) : (
              orderHistory.map((entry: OrderHistoryEntry) => (
                <TableRow key={entry.id} className="hover:bg-card/80">
                  <TableCell className="px-3 py-2 text-xs">{format(new Date(entry.timestamp), 'yyyy-MM-dd')}</TableCell>
                  <TableCell className="px-3 py-2 text-xs">{format(new Date(entry.timestamp), 'HH:mm:ss')}</TableCell>
                  <TableCell className="px-3 py-2 text-xs">{entry.pair}</TableCell>
                  <TableCell className="px-3 py-2 text-xs">{entry.crypto1Symbol}</TableCell>
                  <TableCell className={cn("px-3 py-2 text-xs font-semibold", entry.orderType === "BUY" ? "text-green-400" : "text-destructive")}>
                    {entry.orderType}
                  </TableCell>
                  <TableCell className="px-3 py-2 text-xs">{formatNum(entry.amountCrypto1)}</TableCell>
                  <TableCell className="px-3 py-2 text-xs">{formatNum(entry.avgPrice)}</TableCell>
                  <TableCell className="px-3 py-2 text-xs">{formatNum(entry.valueCrypto2)}</TableCell>
                  <TableCell className="px-3 py-2 text-xs">{formatNum(entry.price1)}</TableCell>
                  <TableCell className="px-3 py-2 text-xs">{entry.crypto1Symbol}</TableCell>
                  <TableCell className="px-3 py-2 text-xs">{formatNum(entry.price2) ?? "-"}</TableCell>
                  <TableCell className="px-3 py-2 text-xs">{entry.crypto2Symbol}</TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
        <ScrollBar orientation="horizontal" />
      </ScrollArea>
    </div>
  );
}
